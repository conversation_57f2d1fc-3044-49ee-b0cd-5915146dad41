import {PAGINATION_PAGE_SIZE} from "@/constants/Constants";

class UserService {

    async walletBalanceStats(httpServiceObject): Promise<null>{
        return new Promise((resolve,reject) => {
            try {
                httpServiceObject.post('/api/user-wallets').then(result => {
                    if (result.hasOwnProperty('success')) {
                        return resolve(result.response);
                    } else {
                        return reject('Something went wrong. Try after sometime.')
                    }
                }).catch(err => {
                    return reject('Something went wrong. Try again.')
                });
            }
            catch (e:any) {
                return reject('Something went wrong. Try later.')
            }
        });
    }

    async dashboardStats(httpServiceObject): Promise<null>{
        return new Promise((resolve,reject) => {
            try {
                httpServiceObject.post('/api/dashboard-stats').then(result => {
                    if (result.hasOwnProperty('success')) {
                        return resolve(result.response);
                    } else {
                        return reject('Something went wrong. Try after sometime.')
                    }
                }).catch(err => {
                    return reject('Something went wrong. Try again.')
                });
            }
            catch (e:any) {
                return reject('Something went wrong. Try later.')
            }
        });
    }

    async stakedSchedule(httpServiceObject): Promise<null>{
        return new Promise((resolve,reject) => {
            try {
                httpServiceObject.post('/api/staked-schedule').then(result => {
                    if (result.hasOwnProperty('success')) {
                        return resolve(result.response);
                    } else {
                        return reject('Something went wrong. Try after sometime.')
                    }
                }).catch(err => {
                    return reject('Something went wrong. Try again.')
                });
            }
            catch (e:any) {
                return reject('Something went wrong. Try later.')
            }
        });
    }

    async referralSchedule(httpServiceObject): Promise<null>{
        return new Promise((resolve,reject) => {
            try {
                httpServiceObject.post('/api/referral-schedule').then(result => {
                    if (result.hasOwnProperty('success')) {
                        return resolve(result.response);
                    } else {
                        return reject('Something went wrong. Try after sometime.')
                    }
                }).catch(err => {
                    return reject('Something went wrong. Try again.')
                });
            }
            catch (e:any) {
                return reject('Something went wrong. Try later.')
            }
        });
    }

    async dbStats(httpServiceObject): Promise<null>{
        return new Promise((resolve,reject) => {
            try {
                httpServiceObject.post('/api/user-db-logs').then(result => {
                    if (result.hasOwnProperty('success')) {
                        return resolve(result.data);
                    } else {
                        return reject('Something went wrong. Try after sometime.')
                    }
                }).catch(err => {
                    return reject('Something went wrong. Try again.')
                });
            }
            catch (e:any) {
                return reject('Something went wrong. Try later.')
            }
        });
    }

    async getStakes(httpServiceObject): Promise<null>{
        return new Promise((resolve,reject) => {
            try {
                httpServiceObject.post('/api/user-stakes').then(result => {
                    if (result.hasOwnProperty('success')) {
                        return resolve(result.response);
                    } else {
                        return reject('Something went wrong. Try after sometime.')
                    }
                }).catch(err => {
                    return reject('Something went wrong. Try again.')
                });
            }
            catch (e:any) {
                return reject('Something went wrong. Try later.')
            }
        });
    }

    async getDirects(httpServiceObject,code=null): Promise<null>{
        let ref = {code: code};
        return new Promise((resolve,reject) => {
            try {
                httpServiceObject.post('/api/user-directs', ref).then(result => {
                    if (result.hasOwnProperty('success')) {
                        return resolve(result.response);
                    } else {
                        return reject('Something went wrong. Try after sometime.')
                    }
                }).catch(err => {
                    return reject('Something went wrong. Try again.')
                });
            }
            catch (e:any) {
                return reject('Something went wrong. Try later.')
            }
        });
    }

    async getLevelBusiness(httpServiceObject,level): Promise<null>{
        let data = {level: level};
        return new Promise((resolve,reject) => {
            try {
                httpServiceObject.post('/api/level-wise-business', data).then(result => {
                    if (result.hasOwnProperty('success')) {
                        return resolve(result.response);
                    } else {
                        return reject('Something went wrong. Try after sometime.')
                    }
                }).catch(err => {
                    return reject('Something went wrong. Try again.')
                });
            }
            catch (e:any) {
                return reject('Something went wrong. Try later.')
            }
        });
    }

    async InvestRequest(httpServiceObject,formData): Promise<null>{

        return new Promise((resolve,reject) => {
            try {
                httpServiceObject.post('/api/validate-investment-request', formData).then(result => {
                    if (result.hasOwnProperty('success')) {
                        return resolve(result.response);
                    } else if (result.hasOwnProperty('error')) {
                        return reject(result.error.amount[0]);
                    }
                }).catch(err => {
                    return reject('Something went wrong. Try again.')
                });
            }
            catch (e:any) {
                return reject('Something went wrong. Try later.')
            }
        });
    }
    async saveHash(httpServiceObject,hash): Promise<null>{
        let data = {hash: hash};
        return new Promise((resolve,reject) => {
            try {
                httpServiceObject.post('/api/save-hash', data).then(result => {
                    if (result.hasOwnProperty('success')) {
                        return resolve(result.response);
                    } else if (result.hasOwnProperty('error')) {
                        return reject(result.error.hash[0]);
                    }
                }).catch(err => {
                    return reject('Something went wrong. Try again.')
                });
            }
            catch (e:any) {
                return reject('Something went wrong. Try later.')
            }
        });
    }

    async withdrawalRequest(httpServiceObject,formData): Promise<null>{

        return new Promise((resolve,reject) => {
            try {
                httpServiceObject.post('/api/validate-withdrawal-request', formData).then(result => {
                    if (result.hasOwnProperty('success')) {
                        return resolve(result.response);
                    } else if (result.hasOwnProperty('error')) {
                        return reject(result.error.amount[0]);
                    }
                }).catch(err => {
                    return reject('Something went wrong. Try again.')
                });
            }
            catch (e:any) {
                return reject('Something went wrong. Try later.')
            }
        });
    }

    async withdrawals(httpServiceObject): Promise<null>{
        return new Promise((resolve,reject) => {
            try {
                httpServiceObject.post('/api/user-withdrawals').then(result => {
                    if (result.hasOwnProperty('success')) {
                        return resolve(result.response);
                    } else {
                        return reject('Something went wrong. Try after sometime.')
                    }
                }).catch(err => {
                    return reject('Something went wrong. Try again.')
                });
            }
            catch (e:any) {
                return reject('Something went wrong. Try later.')
            }
        });
    }

    async ranks(httpServiceObject): Promise<null>{
        return new Promise((resolve,reject) => {
            try {
                httpServiceObject.post('/api/user-rank').then(result => {
                    if (result.hasOwnProperty('success')) {
                        return resolve(result.response);
                    } else {
                        return reject('Something went wrong. Try after sometime.')
                    }
                }).catch(err => {
                    return reject('Something went wrong. Try again.')
                });
            }
            catch (e:any) {
                return reject('Something went wrong. Try later.')
            }
        });
    }

    async openLevels(httpServiceObject): Promise<null>{
        return new Promise((resolve,reject) => {
            try {
                httpServiceObject.post('/api/opened-levels').then(result => {
                    if (result.hasOwnProperty('success')) {
                        return resolve(result.response);
                    } else {
                        return reject('Something went wrong. Try after sometime.')
                    }
                }).catch(err => {
                    return reject('Something went wrong. Try again.')
                });
            }
            catch (e:any) {
                return reject('Something went wrong. Try later.')
            }
        });
    }

    async monthlyTeamBusiness(httpServiceObject): Promise<null>{
        return new Promise((resolve,reject) => {
            try {
                httpServiceObject.post('/api/monthly-business').then(result => {
                    if (result.hasOwnProperty('success')) {
                        return resolve(result.response);
                    } else {
                        return reject('Something went wrong. Try after sometime.')
                    }
                }).catch(err => {
                    return reject('Something went wrong. Try again.')
                });
            }
            catch (e:any) {
                return reject('Something went wrong. Try later.')
            }
        });
    }

    async transactions(httpServiceObject,page,per_page=PAGINATION_PAGE_SIZE, searchFilter={}): Promise<null>{
        return new Promise((resolve,reject) => {
            try {
                httpServiceObject.post(`/api/transaction?page=${page}&per_page=${per_page}`,searchFilter).then(result => {
                    if (result.hasOwnProperty('success')) {
                        return resolve(result.response);
                    } else {
                        return reject('Something went wrong. Try after sometime.')
                    }
                }).catch(err => {
                    return reject('Something went wrong. Try again.')
                });
            }
            catch (e:any) {
                return reject('Something went wrong. Try later.')
            }
        });
    }

    async transactionTypes(httpServiceObject): Promise<null>{
        return new Promise((resolve,reject) => {
            try {
                httpServiceObject.post('/api/transaction-types').then(result => {
                    if (result.hasOwnProperty('success')) {
                        return resolve(result.response);
                    } else {
                        return reject('Something went wrong. Try after sometime.')
                    }
                }).catch(err => {
                    return reject('Something went wrong. Try again.')
                });
            }
            catch (e:any) {
                return reject('Something went wrong. Try later.')
            }
        });
    }

    async getTickets(httpServiceObject): Promise<null>{
        return new Promise((resolve,reject) => {
            try {
                httpServiceObject.post('/api/user-tickets').then(result => {
                    if (result.hasOwnProperty('success')) {
                        return resolve(result.response);
                    } else {
                        return reject('Something went wrong. Try after sometime.')
                    }
                }).catch(err => {
                    return reject('Something went wrong. Try again.')
                });
            }
            catch (e:any) {
                return reject('Something went wrong. Try later.')
            }
        });
    }

    async createTicket(httpServiceObject,formData): Promise<null>{

        return new Promise((resolve,reject) => {
            try {
                httpServiceObject.post('/api/create-ticket', formData).then(result => {
                    if (result.hasOwnProperty('success')) {
                        return resolve(result.response);
                    } else if (result.hasOwnProperty('error')) {
                        return reject(result.error.message[0]);
                    }
                }).catch(err => {
                    return reject('Something went wrong. Try again.')
                });
            }
            catch (e:any) {
                console.log(e.message);
                return reject('Something went wrong. Try later.')
            }
        });
    }

    async sendMessage(httpServiceObject,formData): Promise<null>{

        return new Promise((resolve,reject) => {
            try {
                httpServiceObject.post('/api/add-ticket-message', formData).then(result => {
                    if (result.hasOwnProperty('success')) {
                        return resolve(result.response);
                    } else if (result.hasOwnProperty('error')) {
                        return reject('Something went wrong. Try again letter.');
                    }
                }).catch(err => {
                    console.log(err.message);
                    return reject('Something went wrong. Try again.')
                });
            }
            catch (e:any) {
                console.log(e.message);
                return reject('Something went wrong. Try later.')
            }
        });
    }

    async closeTicket(httpServiceObject, ref): Promise<null>{
        ref=ref.toString();
        let data = {ref_no: ref};
        return new Promise((resolve,reject) => {
            try {
                httpServiceObject.post('/api/close-ticket', data).then(result => {
                    if (result.hasOwnProperty('success')) {
                        return resolve(result.response);
                    } else {
                        return reject('Something went wrong. Try after sometime.')
                    }
                }).catch(err => {
                    return reject('Something went wrong. Try again.')
                });
            }
            catch (e:any) {
                return reject('Something went wrong. Try later.')
            }
        });
    }

    async getTree(httpServiceObject, searchFilter): Promise<null>{
        return new Promise((resolve,reject) => {
            try {
                httpServiceObject.post('/api/user-tree', searchFilter).then(result => {
                    if (result.hasOwnProperty('success')) {
                        return resolve(result.response);
                    } else {
                        return reject('Something went wrong. Try after sometime.')
                    }
                }).catch(err => {
                    return reject('Something went wrong. Try again.')
                });
            }
            catch (e:any) {
                return reject('Something went wrong. Try later.')
            }
        });
    }

}

export default new UserService()
