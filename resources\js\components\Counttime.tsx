import React, { useEffect, useState } from 'react';

const CountdownTimer = ({ endDate }) => {
    const calculateTimeLeft = () => {
        const now = new Date().getTime();
        const distance = new Date(endDate).getTime() - now;

        if (distance <= 0) return null;

        return {
            days: Math.floor(distance / (1000 * 60 * 60 * 24)),
            hours: Math.floor((distance % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60)),
            minutes: Math.floor((distance % (1000 * 60 * 60)) / (1000 * 60)),
            seconds: Math.floor((distance % (1000 * 60)) / 1000),
        };
    };

    const [timeLeft, setTimeLeft] = useState(calculateTimeLeft());

    useEffect(() => {
        const timer = setInterval(() => {
            const newTime = calculateTimeLeft();
            setTimeLeft(newTime);

            if (!newTime) clearInterval(timer);
        }, 1000);

        return () => clearInterval(timer);
    }, [endDate]);

    if (!timeLeft) return <div className="timer-container">Time's up!</div>;

    return (
        <div className="timer-container">
            <div className="time-box">
                <span id="days">{timeLeft.days}</span>
                <p>Days</p>
            </div>
            <div className="time-box">
                <span id="hours">{String(timeLeft.hours).padStart(2, '0')}</span>
                <p>Hour</p>
            </div>
            <div className="time-box">
                <span id="minutes">{String(timeLeft.minutes).padStart(2, '0')}</span>
                <p>Minute</p>
            </div>
            <div className="time-box">
                <span id="seconds">{String(timeLeft.seconds).padStart(2, '0')}</span>
                <p>Second</p>
            </div>
        </div>
    );
};

export default CountdownTimer;
