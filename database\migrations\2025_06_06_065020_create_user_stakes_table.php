<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends \App\Base\Database\MigrationBase
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        $this->schema->create('user_stakes', function (\App\Base\Database\BlueprintBase $table) {
            $table->id();
            $table->bigInteger('ref_no')->unique();
            $table->integer('user_id')->nullable()->index();
            $table->integer('wallet_id')->nullable()->index();
            $table->integer('investment_counter')->default(1)->index();
            $table->string('reference_id')->nullable()->index();
            $table->double('amount')->default(\App\Constants\CommonConstants::NO)->index();
            $table->double('tokens')->default(\App\Constants\CommonConstants::NO)->index();
            $table->decimal('token_price',48,8)->default(0)->comment('in usd')->index();
            $table->decimal('earnings',48,8)->default(0)->comment('in usd')->index();
            $table->double('monthly_dividend')->default(0)->comment('in %')->index();
            $table->integer('is_active')->default(\App\Constants\CommonConstants::YES)->comment('0=no 1=yes')->index();
            $table->integer('is_admin')->default(\App\Constants\CommonConstants::NO)->index();
            $table->integer('is_conditional')->default(\App\Constants\CommonConstants::NO)->index();
            $table->integer('is_archived')->default(\App\Constants\CommonConstants::NO)->index();
            $table->integer('is_saved')->default(\App\Constants\CommonConstants::NO)->index();
            $table->integer('is_reset')->default(\App\Constants\CommonConstants::NO)->index();
            $table->date('next_roi')->nullable()->index();
            $table->date('end_date')->nullable()->index();
            $table->date('roi_end_date')->nullable()->index();
            $table->text('comments')->nullable();
            $table->date('time')->nullable()->index();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('user_stakes');
    }
};
