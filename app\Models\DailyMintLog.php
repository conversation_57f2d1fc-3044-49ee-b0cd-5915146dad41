<?php

namespace App\Models;

use App\Base\Model\BaseModel;
use App\Components\Web3NodeBackend;
use App\Constants\CommonConstants;

class DailyMintLog extends BaseModel
{
    protected $fillable = [
        'date',
        'hash',
        'created_at',
        'updated_at',
    ];

    public static function mintToken() {
        try {
            $model = new DailyMintLog();
            $model->date = date(CommonConstants::PHP_DATE_FORMAT_SHORT);
            if ($model->save()) {
                $mintRequest = Web3NodeBackend::mintToken();
                if ($mintRequest) {
                    $model->hash = $mintRequest;
                    if ($model->update(['hash'])) {
                        return true;
                    }
                } else {
                    throw new \Error('Mint token transaction not called successfully');
                }
            } else {
                throw new \Error('Unable to save mint token record');
            }
        } catch (\Exception $e) {
            Log::insertLog([
                'user_id'=>CommonConstants::ADMINISTRATIVE,
                'particulars' => 'Unable to mint token',
                'type' => 'mint_token_error',
                'data' => json_encode([
                    'date'=>date(CommonConstants::PHP_DATE_FORMAT_SHORT),
                    'message'=>$e->getMessage(),
                    'message_trace'=>$e->getTraceAsString(),
                ])
            ]);
        }
        return false;
    }
}
