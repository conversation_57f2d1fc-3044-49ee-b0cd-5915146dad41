<?php

namespace Database\Seeders;

use App\Constants\CommonConstants;
use App\Models\TransactionType;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class TransactionTypeSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $rows = array(
            ['id'=>TransactionType::TRANSACTION_TYPE_DEPOSIT,'name'=>'Deposit'],
            ['id'=>TransactionType::TRANSACTION_TYPE_WITHDRAWAL,'name'=>'Withdrawal'],
            ['id'=>TransactionType::TRANSACTION_TYPE_STAKE,'name'=>'Stake'],
            ['id'=>TransactionType::TRANSACTION_TYPE_DIVIDEND,'name'=>'Dividend'],
            ['id'=>TransactionType::TRANSACTION_TYPE_DIVIDEND_COMMISSION,'name'=>'Dividend Commission'],
            ['id'=>TransactionType::TRANSACTION_TYPE_ADMIN_ADJUSTMENT,'name'=>'Adjustment'],
            ['id'=>TransactionType::TRANSACTION_TYPE_EARNING_CLOSING,'name'=>'Closing'],
            ['id'=>TransactionType::TRANSACTION_TYPE_TRANSFER,'name'=>'Transfer'],
            ['id'=>TransactionType::TRANSACTION_TYPE_RANK_REWARD,'name'=>'Rank Reward'],
            ['id'=>TransactionType::TRANSACTION_TYPE_ROYALTY,'name'=>'Royalty'],
        );

        foreach ($rows as $row) {
            if(!\App\Models\TransactionType::query()->where('id','=',$row['id'])->first()) {
                DB::table(TransactionType::getTableName())->insert($row);
            }
        }
    }
}
