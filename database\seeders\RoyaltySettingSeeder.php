<?php

namespace Database\Seeders;

use App\Constants\CommonConstants;
use App\Models\Country;
use App\Models\ReferralSetting;
use App\Models\RoyaltySetting;
use App\Models\Setting;
use App\Models\TopupSetting;
use App\Models\User;
use App\Models\UserWallet;
use App\Models\Wallet;
use Faker\Provider\UserAgent;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;

class RoyaltySettingSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $rows=[
            ['rank_id'=>1,'incentive'=>4,'allowed_days'=>'15,30'],
            ['rank_id'=>2,'incentive'=>4,'allowed_days'=>'15,30'],
            ['rank_id'=>3,'incentive'=>4,'allowed_days'=>'15,30'],
            ['rank_id'=>4,'incentive'=>3,'allowed_days'=>'10,20,30'],
            ['rank_id'=>5,'incentive'=>3,'allowed_days'=>'10,20,30'],
            ['rank_id'=>6,'incentive'=>3,'allowed_days'=>'10,20,30'],
            ['rank_id'=>7,'incentive'=>2,'allowed_days'=>'10,20,30'],
            ['rank_id'=>8,'incentive'=>2,'allowed_days'=>'10,20,30'],
            ['rank_id'=>9,'incentive'=>2,'allowed_days'=>'10,20,30'],
            ['rank_id'=>10,'incentive'=>1,'allowed_days'=>'10,20,30'],
            ['rank_id'=>11,'incentive'=>1,'allowed_days'=>'10,20,30'],
            ['rank_id'=>12,'incentive'=>1,'allowed_days'=>'10,20,30'],
        ];

        foreach ($rows as $row) {
            if(!RoyaltySetting::query()->where('rank_id','=',$row['rank_id'])->first()) {
                RoyaltySetting::create($row);
            }
        }
    }
}
