import {PlaceholderPattern} from '@/components/ui/placeholder-pattern';
import AppLayout from '@/layouts/app-layout';
import {type BreadcrumbItem} from '@/types';
import {Head, Link} from '@inertiajs/react';
import UserLayout from "@/layouts/user/user-layout";
import {<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>} from "@mui/x-charts";
import BreadcrumbsButtons from "@/components/user/breadcrumbButtons";
import {useUserContext} from "@/context/UserContext";
import {useEffect, useState} from "react";
import UserService from "@/services/UserService";
import {YES} from "@/constants/Constants";


export default function openedLevels() {
    const {
        httpServiceObject,
        copyButton
    } = useUserContext();

    const [levels, setLevels] = useState<any>(null);
    const [stats, setStats] = useState<any>([]);

    const fetchStats = async () => {

        try {
            let stats = await UserService.openLevels(httpServiceObject);
            setStats(stats?.stats);
            setLevels(stats?.levels);

        } catch (error) {
            console.error("Error fetching stats:", error);
        }
    };
    useEffect(() => {
        fetchStats();
    }, [])


    return (
        <UserLayout>

            <main className="adminuiux-content has-sidebar">

                <div className="container mt-4">
                    <div className="row gx-3 align-items-center">
                        <div className="col-12 col-sm">
                            <nav aria-label="breadcrumb" className="mb-2">
                                <ol className="breadcrumb mb-0">
                                    <li className="breadcrumb-item bi"><Link href={route('user.dashboard')}><i
                                        className="bi bi-house-door me-1 fs-14"></i> Dashboard</Link></li>
                                    <li className="breadcrumb-item active bi" aria-current="page">Opened Levels</li>
                                </ol>
                            </nav>
                            <h5>Opened Levels</h5>
                        </div>
                       {/* <div className="col-12 col-sm-auto text-end py-3 py-sm-0">
                            <BreadcrumbsButtons/>
                        </div>*/}
                    </div>
                </div>

                <div className="container mt-4" id="main-content" data-bs-spy="scroll" data-bs-target="#list-example"
                     data-bs-smooth-scroll="true">

                    <div className="row" id="list-item-2">

                        {(stats && stats.length > 0 ) &&
                        <>
                            <div className="col-12">
                                <div className="alert alert-warning">
                                    <ul style={{listStyle: 'circle', marginBottom: 0}}>
                                        {Object.entries(stats)
                                            .map(([key, item]) => (<>
                                                <li key={key} dangerouslySetInnerHTML={{__html:item}}></li>
                                            </>))
                                        }
                                    </ul>
                                </div>
                            </div>
                        </>
                        }

                        {(levels && levels.length > 0 ) ? (
                            levels &&
                            Object.entries(levels)
                                .map(([key, item]) => (
                                    <div className="col-12 col-md-3 mb-4" key={key}>
                                        <div className="card adminuiux-card {/*bg-theme-1*/}">
                                            <div className="card-body">
                                                <div className="row align-items-center text-center mb-3">
                                                    <div className="col">
                                                        <h5 className="mb-0 fw-medium"> Level {item?.id}</h5>
                                                    </div>
                                                </div>

                                                <div className="row">
                                                    <div className="text-center mb-3">
                                                        <h4 className="mb-1 text-center ">
                                                            <span className={(item?.is_achieved==YES) ? 'avatar avatar-100 bg-theme-1 rounded w-160' : 'avatar avatar-100 bg-theme-1-subtle text-theme-1 rounded w-160'}>{item?.incentive}</span>
                                                        </h4>
                                                    </div>
                                                </div>

                                                <div className="text-center">
                                                    <h6 className="fw-normal">Directs: <b className={'fw-bold'}>{item?.directs}</b> </h6>
                                                    <h6 className="fw-normal">Opened Levels: <b className={'fw-bold'}>{item?.opened_levels}</b> </h6>
                                                    <h6 className="fw-normal">Direct Business: <b className={'fw-bold'}>{item?.direct_business}</b> </h6>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                ))
                        ) : (
                            <>
                                <div className="col-12 mb-2">
                                    <div className="card adminuiux-card mb-3">
                                        <div className="card-body">
                                            <div className="row align-items-center">
                                                <div className="col-12 col-sm-9 col-xxl mb-3 mb-xxl-0">
                                                    <div className="row align-items-center">
                                                        <div className="col">
                                                            <h5 className={'text-center pt-5 pb-5'}>No Data Found</h5>

                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </>
                        )}
                    </div>

                </div>



            </main>


        </UserLayout>
    );
}
