import {Head, Link, router} from '@inertiajs/react';
import UserLayout from "@/layouts/user/user-layout";
import {Line<PERSON><PERSON>, PieChart} from "@mui/x-charts";
import {useUserContext} from "@/context/UserContext";
import {useEffect, useState} from "react";
import UserService from "@/services/UserService";
import {YES} from "@/constants/Constants";


export default function Dashboard() {
    const valueFormatter = (item: { value: number }) => `${item.value}%`;
    const {
        userDetails,
        address,
        userToken,
        disconnectWallet,
        showNotification,
        httpServiceObject,
        walletBalances,
        updateWalletBalances,
        setWalletBalances,
        fetchBalanceFromApi,
        neonixBalance,
        usdtBalance,
        dxeBalance,
        dxeUsdPrice
    } = useUserContext();

    const [dashboardStats, setDashboardStats] = useState<any>(null);
    const [investmentsChartStats, setInvestmentsChartStats] = useState<any>([]);
    const [stakedTokenSchedule, setStakedTokenSchedule] = useState<any>([]);
    const [referralTokenSchedule, setReferralTokenSchedule] = useState<any>([]);

    const [userPowerLeg, setUserPowerLeg] = useState<any>(null);
    const [userSecondPowerLeg, setUserSecondPowerLeg] = useState<any>(null);
    const [userOtherLeg, setUserOtherLeg] = useState<any>(null);

    const fetchStats = async () => {

        try {
            await fetchBalanceFromApi();

            let stats = await UserService.dashboardStats(httpServiceObject);
            setDashboardStats(stats);

            stats?.stakes &&
            Object.entries(stats?.stakes)
                .map(([key, item]) => (
                    investmentsChartStats.push({
                        label: 'Investment ' + item?.counter,
                        value: item?.amount,
                        color: item?.color_code
                    })
                ))

            let dbStats = await UserService.dbStats(httpServiceObject);
            if(dbStats?.team) {
                if (dbStats.team?.calculations) {
                    if (dbStats.team.calculations.hasOwnProperty('power')) {
                        setUserPowerLeg(dbStats.team.calculations.power);
                    }
                    if (dbStats.team.calculations.hasOwnProperty('second_power')) {
                        setUserSecondPowerLeg(dbStats.team.calculations.second_power);
                    }
                    if (dbStats.team.calculations.hasOwnProperty('other')) {
                        setUserOtherLeg(dbStats.team.calculations.other);
                    }
                }
            }

            let stakedSchedule = await UserService.stakedSchedule(httpServiceObject);
            setStakedTokenSchedule(stakedSchedule);

            let referralSchedule = await UserService.referralSchedule(httpServiceObject);
            setReferralTokenSchedule(referralSchedule);
        } catch (error) {
            console.error("Error fetching stats:", error);
        }
    };
    useEffect(() => {
        fetchStats();
        updateWalletBalances();
    }, []);

    const openModal = (item) => {
        let myModal = new bootstrap.Modal(document.getElementById('stakedTokenModal'), {});
        myModal.show();
    }

    const openModal2 = (item) => {
        let myModal = new bootstrap.Modal(document.getElementById('referralTokenModal'), {});
        myModal.show();
    }

    return (
        <UserLayout>

            <div className="modal fade" id="stakedTokenModal" tabIndex="-1" aria-labelledby="stakedtokenLabel"
                 aria-hidden="true">
                <div className="modal-dialog">
                    <div className="modal-content">
                        <div className="modal-header">
                            <p className="modal-title h5" id="stakedtokenmodalLabel">Staked Token Release Schedule</p>
                            <button type="button" className="btn-close" data-bs-dismiss="modal"
                                    aria-label="Close"></button>
                        </div>
                        <div className="modal-body">
                            <div style={{maxHeight:'500px',overflowX:'auto'}}>
                                <table className="table">
                                    <thead>
                                    <tr>
                                        <th>Release Date</th>
                                        <th>Tokens</th>
                                    </tr>
                                    </thead>
                                    <tbody>
                                    {(stakedTokenSchedule && stakedTokenSchedule.length > 0) ? (<>
                                        {
                                            (
                                                stakedTokenSchedule &&
                                                Object.entries(stakedTokenSchedule)
                                                    .map(([key, item]) => (
                                                        <tr key={key}>
                                                            <td>{item.date}</td>
                                                            <td>{item.tokens} DRIXE</td>
                                                        </tr>
                                                    ))

                                            )
                                        }
                                    </>) : (<>
                                        <tr><td colSpan={"2"}>No Data Found</td></tr>
                                    </>)}
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>


            <div className="modal fade" id="referralTokenModal" tabIndex="-1" aria-labelledby="referraltokenLabel"
                 aria-hidden="true">
                <div className="modal-dialog">
                    <div className="modal-content">
                        <div className="modal-header">
                            <p className="modal-title h5" id="referraltokenmodalLabel">Referral Token Release Schedule</p>
                            <button type="button" className="btn-close" data-bs-dismiss="modal"
                                    aria-label="Close"></button>
                        </div>
                        <div className="modal-body">
                            <div style={{maxHeight:'500px',overflowX:'auto'}}>
                            <table className="table">
                                <thead>
                                    <tr>
                                        <th>Release Date</th>
                                        <th>Tokens</th>
                                    </tr>
                                </thead>
                                <tbody>
                                {(referralTokenSchedule && referralTokenSchedule.length > 0) ? (<>
                                    {
                                        (
                                            referralTokenSchedule &&
                                            Object.entries(referralTokenSchedule)
                                                .map(([key, item]) => (
                                                    <tr key={key}>
                                                       <td>{item.date}</td>
                                                       <td>{item.tokens} DRIXE</td>
                                                    </tr>
                                                ))

                                        )
                                    }
                                </>) : (<>
                                    <tr><td colSpan={"2"}>No Data Found</td></tr>
                                </>)}
                                </tbody>
                            </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <main className="adminuiux-content has-sidebar">

                <div className="container mt-4" id="main-content">


                    <div className="row align-items-center">
                        <div className="col-12 col-lg mb-4 m-d-flex m-direction-row">
                            <h3 className="fw-normal mb-0 text-secondary">Hello,</h3>
                            <h3>{userDetails?.username}</h3>
                        </div>

                        <div className="col-6 col-sm-4 col-lg-3 col-xl-2 mb-4">
                            <div className="card adminuiux-card" style={{overflow: 'hidden'}}>
                                <div className="card-body mx-auto text-end pe-0">
                                    <p className="text-secondary small mb-2">Total Directs</p>
                                    <div style={{marginTop: '23px'}}>
                                        <div className="text-center">
                                            <i className="bi bi-people"
                                               style={{
                                                   background: 'rgb(236 232 246)',
                                                   borderRadius: '50%',
                                                   height: '70px',
                                                   color: '#664604',
                                                   width: '70px',
                                                   display: 'block',
                                                   fontSize: '30px',
                                                   textAlign: 'center',
                                                   position: 'absolute',
                                                   left: '-10px',
                                                   bottom: '-12px',
                                                   paddingTop: '5px'
                                               }}></i>
                                        </div>
                                        <h4 className="m-0">{dashboardStats?.team?.total_directs}</h4>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div className="col-6 col-sm-4 col-lg-3 col-xl-2 mb-4">
                            <div className="card adminuiux-card" style={{overflow: 'hidden'}}>
                                <div className="card-body mx-auto text-end pe-0">
                                    <p className="text-secondary small mb-2">Active Directs</p>
                                    <div style={{marginTop: '23px'}}>
                                        <div className="text-center">
                                            <i className="bi bi-people"
                                               style={{
                                                   background: 'rgb(236 232 246)',
                                                   borderRadius: '50%',
                                                   height: '70px',
                                                   color: '#664604',
                                                   width: '70px',
                                                   display: 'block',
                                                   fontSize: '30px',
                                                   textAlign: 'center',
                                                   position: 'absolute',
                                                   left: '-10px',
                                                   bottom: '-12px',
                                                   paddingTop: '5px'
                                               }}></i>
                                        </div>
                                        <h4 className="m-0">{dashboardStats?.team?.active_directs}</h4>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div className="col-6 col-sm-4 col-lg-3 col-xl-2 mb-4">
                            <div className="card adminuiux-card" style={{overflow: 'hidden'}}>
                                <div className="card-body mx-auto text-end pe-0">
                                    <p className="text-secondary small mb-2">Total Team</p>
                                    <div style={{marginTop: '23px'}}>
                                        <div className="text-center">
                                            <i className="bi bi-bezier"
                                               style={{
                                                   background: 'rgb(236 232 246)',
                                                   borderRadius: '50%',
                                                   height: '70px',
                                                   color: '#664604',
                                                   width: '70px',
                                                   display: 'block',
                                                   fontSize: '30px',
                                                   textAlign: 'center',
                                                   position: 'absolute',
                                                   left: '-10px',
                                                   bottom: '-12px',
                                                   paddingTop: '5px'
                                               }}></i>
                                        </div>
                                        <h4 className="m-0">{dashboardStats?.team?.total_team}</h4>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div className="col-6 col-sm-4 col-lg-3 col-xl-2 mb-4">
                            <div className="card adminuiux-card" style={{overflow: 'hidden'}}>
                                <div className="card-body mx-auto text-end pe-0">
                                    <p className="text-secondary small mb-2">Team Business</p>
                                    <div style={{marginTop: '23px'}}>
                                        <div className="text-center">
                                            <i className="bi bi-bezier"
                                               style={{
                                                   background: 'rgb(236 232 246)',
                                                   borderRadius: '50%',
                                                   height: '70px',
                                                   color: '#664604',
                                                   width: '70px',
                                                   display: 'block',
                                                   fontSize: '30px',
                                                   textAlign: 'center',
                                                   position: 'absolute',
                                                   left: '-10px',
                                                   bottom: '-12px',
                                                   paddingTop: '5px'
                                               }}></i>
                                        </div>
                                        <h4 className="m-0">{dashboardStats?.team?.team_business}</h4>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div className="row">

                        <div className="col-12 col-lg-6 col-xl-4 mb-4">
                            <div className="card adminuiux-card position-relative overflow-hidden bg-theme-1 h-100"
                                 style={{
                                     background: 'url(backend/assets/img/modern-ai-image/investment-3.jpg)',
                                     backgroundSize: 'cover'
                                 }}>
                                <div
                                    className="position-absolute top-0 start-0 h-100 w-100 z-index-0 coverimg opacity-50"
                                    style={{background: 'rgba(179, 113, 0, 0.9)'}}>

                                </div>
                                <div className="card-body z-index-1">
                                    <div className="row align-items-center justify-content-center h-100 py-4">
                                        <div className="col-11">
                                            <h2 className="fw-normal">Your remaining limit is</h2>
                                            <h1 className="mb-3">{userDetails?.formatted_remaining_limit}</h1>
                                            <p>Income Multiplier: <strong>{userDetails?.income_x}x</strong></p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>


                        <div className="col-12 col-lg-6 col-xl-8 mb-4">
                            <div className="card adminuiux-card">
                                <div className="row gx-0">
                                    <div className="col-md-12">
                                        <div className="card-header pb-0">
                                            <h6>Wallet</h6>
                                        </div>
                                    </div>

                                    <div className="col-12 col-xl-4">
                                        <div className="card-body pb-0">
                                            {walletBalances &&
                                            Object.entries(walletBalances)
                                                .map(([key, item]) => (
                                                    <div className="card adminuiux-card bg-theme-1 mb-3" key={key}>
                                                        <div className="card-body">
                                                            <p className="mb-0 pb-0">{item?.name} Wallet </p>
                                                            <strong>{item?.formattedBalance} USD</strong>
                                                        </div>
                                                    </div>
                                                ))}
                                        </div>
                                    </div>


                                    <div className="col-12 col-xl-8">

                                        <div className="card  px-1 adminuiux-card bg-theme-1 custom-dashboard-box" >
                                            <div className="card-body">
                                                <h2 className="mb-3 fw-medium">Overview</h2>
                                                <div className={'d-flex justify-content-between'}>
                                                    <div>
                                                        <h4 className="h4 mb-1">Drixe</h4>
                                                        <p className="opacity-75 mb-3">Price in USD</p>
                                                    </div>
                                                    <div>
                                                        <h4 className="h4 mb-1">${dxeUsdPrice}</h4>

                                                    </div>
                                                </div>
                                                <div className="row" style={{ borderTop: '1px solid rgba(255,255,255,0.2)', paddingTop: '15px'}}>
                                                    <div className="col-6 mb-3">
                                                        <h5 className="mb-1">5%</h5>
                                                        <p className="small opacity-75">Stacking dividend per month</p>
                                                    </div>
                                                    <div className="col-6 mb-3">
                                                        <h5 className="mb-1">18 Months</h5>
                                                        <p className="small opacity-75">Staking duration</p>
                                                    </div>


                                                </div>
                                                <div className="row">
                                                    <div className="col">
                                                        <Link href={route('user.investNow')}>
                                                            <button className="btn btn-light me-2 w-100">Invest Now</button>
                                                        </Link>
                                                    </div>

                                                </div>

                                                <div className="row mt-3">
                                                    <div className="col text-center">
                                                        <p className="small opacity-75"><strong>Note:</strong> The minimum stake amount is $50, with a maximum of $3,000. A maximum of 666 tokens can be staked per day.</p>
                                                        <p className="small opacity-75">When earnings become <strong>{userDetails?.income_x}X</strong>, Staking will be disabled.</p>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>


                                    <div className="row align-items-center gx-3 mx-auto">
                                        <div className="col-12 col-md-4 col-lg-4 mb-3">
                                            <div className="card adminuiux-card">
                                                <div className="card-body">
                                                    <div className="row gx-3 mb-3">

                                                        <div className="col">
                                                            <h4 className="mb-0">Business Leg A</h4>
                                                            <p className="small opacity-75">Total: {(userPowerLeg && userPowerLeg?.team_business ? '$'+userPowerLeg?.team_business : 'N/A')}</p>
                                                        </div>
                                                    </div>
                                                    <div className="row">
                                                        <div className="col-auto">{(userPowerLeg && userPowerLeg?.username ? userPowerLeg?.username : 'N/A')}</div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                        <div className="col-12 col-md-4 col-lg-4 mb-3">
                                            <div className="card adminuiux-card">
                                                <div className="card-body">
                                                    <div className="row gx-3 mb-3">

                                                        <div className="col">
                                                            <h4 className="mb-0">Business Leg B</h4>
                                                            <p className="small opacity-75">Total: {(userSecondPowerLeg && userSecondPowerLeg?.team_business ? '$'+userSecondPowerLeg?.team_business : 'N/A')}</p>
                                                        </div>
                                                    </div>
                                                    <div className="row">
                                                        <div className="col-auto">{(userSecondPowerLeg && userSecondPowerLeg?.username ? userSecondPowerLeg?.username : 'N/A')}</div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div className="col-12 col-md-4 col-lg-4  mb-3">
                                            <div className="card adminuiux-card">
                                                <div className="card-body">
                                                    <div className="row gx-3 mb-3">

                                                        <div className="col">
                                                            <h4 className="mb-0">Other Business</h4>
                                                            <p className="small opacity-75">Total: {(userOtherLeg && userOtherLeg?.team_business ? '$'+userOtherLeg?.team_business : 'N/A')}</p>
                                                        </div>
                                                    </div>
                                                    <div className="row">
                                                        <div className="col-auto">&nbsp;</div>

                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                            </div>
                        </div>

                        <div className="col-12 mb-4 dashboard-wallets">
                            <div className="row">
                                <div className="col-12 col-md m-mb-15">
                                    <div className="card adminuiux-card">
                                        <div className="card-body">
                                            <div className="row gx-3">
                                                <div className="col-auto">
                                                    <i className="fs-4 avatar avatar-50 bg-theme-1 text-white rounded">
                                                        <img src="/backend/assets/img/NEONIX.png"
                                                             className={'coin-logo'}/>
                                                    </i>
                                                </div>
                                                <div className="col">
                                                    <h4 className="mb-0">{neonixBalance}</h4>
                                                    <p className="small opacity-75">NeoNix Balance</p>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div className="col-12 col-md m-mb-15">
                                    <div className="card adminuiux-card">
                                        <div className="card-body">
                                            <div className="row gx-3">
                                                <div className="col-auto">
                                                    <i className="fs-4 avatar avatar-50 bg-theme-1 text-white rounded">
                                                        <img src="/backend/assets/img/usdt.png"
                                                             className={'coin-logo'}/>
                                                    </i>
                                                </div>
                                                <div className="col">
                                                    <h4 className="mb-0">{usdtBalance}</h4>
                                                    <p className="small opacity-75">USDT Balance</p>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div className="col-12 col-md m-mb-15">
                                    <div className="card adminuiux-card">
                                        <div className="card-body">
                                            <div className="row gx-3">
                                                <div className="col-auto">
                                                    <i className="fs-4 avatar avatar-50 bg-theme-1 text-white rounded">
                                                        <img src="/backend/assets/img/logo-512.png"
                                                             className={'coin-logo'}/>
                                                    </i>
                                                </div>
                                                <div className="col">
                                                    <h4 className="mb-0">{dxeBalance}</h4>
                                                    <p className="small opacity-75">DRIXE Balance</p>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div className="col-12 col-md m-mb-15" onClick={openModal}>
                                    <div className="card adminuiux-card">
                                        <div className="card-body">
                                            <div className="row gx-3">
                                                <div className="col-auto">
                                                    <i className="fs-4 avatar avatar-50 bg-theme-1 text-white rounded">
                                                        <img src="/backend/assets/img/logo-512.png"
                                                             className={'coin-logo'}/>
                                                    </i>
                                                </div>
                                                <div className="col">
                                                    <h4 className="mb-0">{userDetails?.staked_tokens}</h4>
                                                    <p className="small opacity-75">
                                                        <span style={{borderBottom:'1px dashed #fff',cursor:'pointer'}}>Staked DRIXE Tokens <i style={{color:'#bb8f44'}} className={"bi bi-eye-fill"}></i></span>
                                                    </p>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div className="col-12 col-md" onClick={openModal2}>
                                    <div className="card adminuiux-card">
                                        <div className="card-body">
                                            <div className="row gx-3">
                                                <div className="col-auto">
                                                    <i className="fs-4 avatar avatar-50 bg-theme-1 text-white rounded">
                                                        <img src="/backend/assets/img/logo-512.png"
                                                             className={'coin-logo'}/>
                                                    </i>
                                                </div>
                                                <div className="col">
                                                    <h4 className="mb-0">{userDetails?.pending_direct_referral_tokens}</h4>
                                                    <p className="small opacity-75">
                                                        <span style={{borderBottom:'1px dashed #fff',cursor:'pointer'}}>Pending Referral Tokens <i style={{color:'#bb8f44'}} className={"bi bi-eye-fill"}></i></span>
                                                    </p>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>


                        <div className="col-12 col-lg-12 col-xl-8 mb-4">
                            <div className="card adminuiux-card">
                                {(dashboardStats?.stakes && dashboardStats?.stakes.length > 0) ? (
                                    <div className="row align-items-center">
                                        <div className="col-12 col-md-6 col-lg-5 col-xl-5">
                                            <div className="card-header">
                                                <h6 className="my-1">Investments</h6>
                                            </div>
                                            <div className="card-body" style={{paddingTop: '0px'}}>
                                                <div
                                                    className="position-relative d-flex align-items-center justify-content-center text-center mb-3">

                                                    <PieChart
                                                        height={250}
                                                        width={250}
                                                        series={[
                                                            {
                                                                data: investmentsChartStats,
                                                                innerRadius: 50,
                                                                // arcLabel: (params) => params.label ?? '',
                                                                arcLabel: (params) => '',
                                                                arcLabelMinAngle: 20,

                                                            },
                                                        ]}
                                                        skipAnimation={false} // boolean, not string
                                                        legend={{enabled: false}}
                                                    />

                                                </div>

                                            </div>
                                        </div>
                                        <div className="col-12 col-sm">
                                            <div className="card-body">
                                                <div className="row mb-2">
                                                    {dashboardStats?.stakes &&
                                                    Object.entries(dashboardStats?.stakes)
                                                        .map(([key, item]) => (
                                                            <div className="col-6 col-md-6 mb-4" key={key}>
                                                                <p className="text-secondary small mb-2">
                                                                    <span className={"me-1 avatar avatar-10 rounded "}
                                                                          style={{background: item?.color_code}}></span>
                                                                    {item?.ref_no}
                                                                </p>
                                                                <h4 className="ps-3 fw-medium">{item?.formattedAmount}</h4>
                                                            </div>
                                                        ))}
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                ) : (
                                    <>
                                        <div className="row align-items-center">
                                            <div className="col-12 col-md-12">
                                                <div className="card-header">
                                                    <h6 className="my-1">Investments</h6>
                                                </div>
                                                <div className="card-body" style={{paddingTop: '0px'}}>
                                                    <div
                                                        className="position-relative d-flex align-items-center justify-content-center text-center mb-3">
                                                        <div style={{padding: '73px 0px'}}>
                                                            <h3 className="card-title mb-4">You don't have any active
                                                                investment yet.</h3>

                                                            <Link href={route('user.investNow')} className={'mt-4'}>
                                                                <button className="btn btn btn-theme">Invest Now
                                                                </button>
                                                            </Link>
                                                        </div>

                                                    </div>

                                                </div>
                                            </div>
                                        </div>
                                    </>
                                )}

                            </div>
                        </div>


                        <div className="col-12 col-md-12 col-xl-4 mb-4">
                            <div className="card adminuiux-card overflow-hidden">

                                <div className="card-body">


                                    <div className="summarychart height-110 w-100 mb-3 d-none">
                                        <canvas id="areachartblue1"></canvas>
                                    </div>

                                    <div className="card adminuiux-card bg-theme-1-subtle">
                                        <div className="card-body">
                                            <p className="text-secondary small mb-2">My Total Earnings</p>
                                            <h4 className="fw-medium">{dashboardStats?.user?.formatted_total_earnings} USD </h4>
                                        </div>
                                    </div>
                                    <div className="card adminuiux-card bg-theme-1-subtle mt-3">
                                        <div className="card-body">
                                            <p className="text-secondary small mb-2">Total Investment</p>
                                            <h4 className="fw-medium">{dashboardStats?.user?.formatted_lifetime_investment} USD </h4>
                                        </div>
                                    </div>
                                    <div className="card adminuiux-card bg-theme-1-subtle mt-3">
                                        <div className="card-body">
                                            <p className="text-secondary small mb-2">Active Investment</p>
                                            <h4 className="fw-medium">{dashboardStats?.user?.formatted_active_investment} USD</h4>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>


                        <div className="col-12">
                            <div className="row mb-2">
                                <div className="col-6 col-md-3 col-lg-3 col-xl-3 col-xxl mb-3">
                                    <Link href={route('user.investNow')}
                                          className="card adminuiux-card style-none text-center h-100">
                                        <div className="card-body">
                                            <i className="avatar avatar-40 text-theme-1 bi bi-piggy-bank h3 mb-3"></i>
                                            <p className="text-secondary small">Investments</p>
                                        </div>
                                    </Link>
                                </div>
                                <div className="col-6 col-md-3 col-lg-3 col-xl-3 col-xxl mb-3">
                                    <a target={"_blank"} download={''} href={'/BusinessPlan.pdf'}
                                          className="card adminuiux-card style-none text-center h-100">
                                        <div className="card-body">
                                            <i className="avatar avatar-40 text-theme-1 bi bi-file-earmark-pdf h3 mb-3"></i>
                                            <p className="text-secondary small">Download PDF</p>
                                        </div>
                                    </a>
                                </div>
                                <div className="col-6 col-md-3 col-lg-3 col-xl-3 col-xxl mb-3">
                                    <Link href={route('user.tickets')}
                                          className="card adminuiux-card style-none text-center h-100">
                                        <div className="card-body">
                                            <i className="avatar avatar-40 text-theme-1 bi bi-chat-dots h3 mb-3"></i>
                                            <p className="text-secondary small">Support</p>
                                        </div>
                                    </Link>
                                </div>

                                <div className="col-6 col-md-3 col-lg-3 col-xl-3 col-xxl mb-3">
                                    <Link href={route('user.royalty')}
                                          className="card adminuiux-card style-none text-center h-100">
                                        <div className="card-body">
                                            <i className="avatar avatar-40 text-theme-1 h3 bi bi-gift mb-3"></i>
                                            <p className="text-secondary small">Rewards</p>
                                        </div>
                                    </Link>
                                </div>

                            </div>
                        </div>


                    </div>


                </div>
            </main>

        </UserLayout>
    );
}
