import { type ReactNode } from 'react';
import {AppKitProvider} from "@/components/AppKitProvider";
import {UserContextProvider} from "@/context/UserContext";
import { ToastContainer, toast } from 'react-toastify';

interface CommonLayoutProps {
    children: ReactNode;
}

export default function CommonLayout({ children, ...props }: CommonLayoutProps) {
    return (
        <>
            <AppKitProvider>
                <ToastContainer />
                <UserContextProvider>
                    {children}
                </UserContextProvider>
            </AppKitProvider>
        </>
    );
}
