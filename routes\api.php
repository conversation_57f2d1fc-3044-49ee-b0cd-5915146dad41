<?php
use App\Http\Controllers\API\AuthController;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;

Route::post('/login', [AuthController::class, 'login']);
Route::post('/validate-sponsor', [AuthController::class, 'validateSponsor']);
Route::post('/save-hash', [AuthController::class, 'saveHash']);

Route::middleware('auth:sanctum')->group(function () {
    Route::post('/user', [AuthController::class, 'user']);
    Route::post('/user-db-logs', [AuthController::class, 'userDbLogs']);
    Route::post('/user-wallets', [AuthController::class, 'userWallets']);
    Route::post('/user-stakes', [AuthController::class, 'userStakes']);
    Route::post('/user-withdrawals', [AuthController::class, 'userWithdrawals']);
    Route::post('/user-directs', [AuthController::class, 'userDirects']);
    Route::post('/user-tree', [AuthController::class, 'userTree']);
    Route::post('/dashboard-stats', [AuthController::class, 'dashboardStats']);
    Route::post('/staked-schedule', [AuthController::class, 'stakedSchedule']);
    Route::post('/referral-schedule', [AuthController::class, 'referralSchedule']);
    Route::post('/level-wise-business', [AuthController::class, 'levelWiseBusiness']);
    Route::post('/opened-levels', [AuthController::class, 'openedLevels']);
    Route::post('/user-rank', [AuthController::class, 'userRank']);
    Route::post('/monthly-business', [AuthController::class, 'monthlyBusiness']);
    Route::post('/transaction-types', [AuthController::class, 'transactionTypes']);
    Route::post('/validate-investment-request', [AuthController::class, 'validateInvestmentRequest']);
    Route::post('/validate-withdrawal-request', [AuthController::class, 'validateWithdrawalRequest']);
    Route::post('/create-ticket', [AuthController::class, 'createTicket']);
    Route::post('/user-tickets', [AuthController::class, 'userTickets']);
    Route::post('/close-ticket', [AuthController::class, 'closeTicket']);
    Route::post('/add-ticket-message', [AuthController::class, 'addTicketMessage']);
    Route::resource('transaction',\App\Http\Controllers\UserTransactionController::class);
    Route::post('/logout', [AuthController::class, 'logout']);
});
