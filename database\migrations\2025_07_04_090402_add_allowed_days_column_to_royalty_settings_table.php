<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('royalty_settings', function (Blueprint $table) {
            $table->string('allowed_days')->index();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('royalty_settings', function (Blueprint $table) {
            $table->dropColumn('allowed_days');
        });
    }
};
