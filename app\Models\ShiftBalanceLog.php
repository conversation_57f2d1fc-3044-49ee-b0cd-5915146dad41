<?php

namespace App\Models;

use App\Base\Model\BaseModel;
use App\Constants\CommonConstants;

class ShiftBalanceLog extends BaseModel
{
    protected $fillable = [
        'ref_no',
        'user_id',
        'wallet_id',
        'amount',
        'transaction_date',
        'created_at',
        'updated_at',
    ];

    public function user()
    {
        return $this->hasOne(User::class, 'id', 'user_id');
    }

    public function wallet()
    {
        return $this->hasOne(Wallet::class, 'id', 'wallet_id');
    }

    public function onCreated()
    {
        if($this->wallet_id==Wallet::WALLET_DIVIDEND) {
            $this->user->addTransaction(
                'Dividend wallet closing debit #' . $this->ref_no,
                TransactionType::TRANSACTION_TYPE_EARNING_CLOSING,
                CommonConstants::DEBIT,
                $this->amount,
                $this->wallet_id,
                json_encode(['shift_balance_log_id' => $this->id, 'type' => 'debit']),
                $this->id
            );

            $this->user->addTransaction(
                'Dividend wallet closing credit #' . $this->ref_no,
                TransactionType::TRANSACTION_TYPE_EARNING_CLOSING,
                CommonConstants::CREDIT,
                $this->amount,
                Wallet::WALLET_WITHDRAWAL,
                json_encode(['shift_balance_log_id' => $this->id, 'type' => 'credit']),
                $this->id
            );
        } else {
            $this->user->addTransaction(
                'Earning wallet closing debit #' . $this->ref_no,
                TransactionType::TRANSACTION_TYPE_EARNING_CLOSING,
                CommonConstants::DEBIT,
                $this->amount,
                $this->wallet_id,
                json_encode(['shift_balance_log_id' => $this->id, 'type' => 'debit']),
                $this->id
            );

            $this->user->addTransaction(
                'Earning wallet closing credit #' . $this->ref_no,
                TransactionType::TRANSACTION_TYPE_EARNING_CLOSING,
                CommonConstants::CREDIT,
                $this->amount,
                Wallet::WALLET_WITHDRAWAL,
                json_encode(['shift_balance_log_id' => $this->id, 'type' => 'credit']),
                $this->id
            );
        }
        parent::onCreated(); // TODO: Change the autogenerated stub
    }

    public static function startEarningProcess() {
        try {
            if(strtolower(date("D"))=="mon") {
                $userWallets=UserWallet::query()
                    ->where('wallet_id','=',Wallet::WALLET_EARNINGS)
                    ->where('balance','>',0)
                    ->get();
                if(count($userWallets)>0) {
                    foreach ($userWallets as $userWallet) {
                        $model=new ShiftBalanceLog();
                        $model->user_id=$userWallet->user_id;
                        $model->wallet_id=$userWallet->wallet_id;
                        $model->amount=$userWallet->balance;
                        $model->transaction_date=date(CommonConstants::PHP_DATE_FORMAT_SHORT);
                        if(!$model->save()) {
                            Log::insertLog([
                                'user_id'=>$model->user_id,
                                'particulars' => 'Unable to shift earning wallet balance',
                                'type' => 'earning_closing_error',
                                'data' => json_encode([])
                            ]);
                        }
                    }
                } else {
                    throw new \Exception('No wallet balance found');
                }
            } else {
                throw new \Exception('Today is '.date("d").", not in allowed day");
            }
        } catch (\Exception $e) {
            Log::insertLog([
                'particulars' => 'Earning closing error',
                'type' => 'earning_closing_error',
                'data' => json_encode([
                    'error' => $e->getMessage(),
                ])
            ]);
        }
        die("success");
    }

    public static function startDividendProcess() {
        try {
            if(strtolower(date("d"))=="01") {
                $userWallets=UserWallet::query()
                    ->where('wallet_id','=',Wallet::WALLET_DIVIDEND)
                    ->where('balance','>',0)
                    ->get();
                if(count($userWallets)>0) {
                    foreach ($userWallets as $userWallet) {
                        $model=new ShiftBalanceLog();
                        $model->user_id=$userWallet->user_id;
                        $model->wallet_id=$userWallet->wallet_id;
                        $model->amount=$userWallet->balance;
                        $model->transaction_date=date(CommonConstants::PHP_DATE_FORMAT_SHORT);
                        if(!$model->save()) {
                            Log::insertLog([
                                'user_id'=>$model->user_id,
                                'particulars' => 'Unable to shift dividend wallet balance',
                                'type' => 'dividend_closing_error',
                                'data' => json_encode([])
                            ]);
                        }
                    }
                } else {
                    throw new \Exception('No wallet balance found');
                }
            } else {
                throw new \Exception('Today is '.date("d").", allowed day is 1st");
            }
        } catch (\Exception $e) {
            Log::insertLog([
                'particulars' => 'Dividend closing error',
                'type' => 'dividend_closing_error',
                'data' => json_encode([
                    'error' => $e->getMessage(),
                ])
            ]);
        }
        die("success");
    }
}
