import {PlaceholderPattern} from '@/components/ui/placeholder-pattern';
import AppLayout from '@/layouts/app-layout';
import {type BreadcrumbItem} from '@/types';
import {Head, Link} from '@inertiajs/react';
import UserLayout from "@/layouts/user/user-layout";
import {<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>} from "@mui/x-charts";
import BreadcrumbsButtons from "@/components/user/breadcrumbButtons";
import {useUserContext} from "@/context/UserContext";
import {useEffect, useState} from "react";
import UserService from "@/services/UserService";


export default function LevelWiseBusiness() {
    const {
        httpServiceObject
    } = useUserContext();

    const [levels, setLevels] = useState<any>([
        {level:1, business:null},
        {level:2, business:null},
        {level:3, business:null},
        {level:4, business:null},
        {level:5, business:null},
        {level:6, business:null},
        {level:7, business:null},
        {level:8, business:null},
        {level:9, business:null},
        {level:10, business:null},
        {level:11, business:null},
        {level:12, business:null}
    ]);



    const calculate = async (key, level) => {
        let newLevels = [...levels];

        newLevels[key].business = "Loading...";
        setLevels(newLevels);

        try {
            let stats = await UserService.getLevelBusiness(httpServiceObject, level);
            newLevels = [...levels];
            newLevels[key].business = stats.formattedBusiness;
            setLevels(newLevels);

        } catch (error) {
            console.error("Error fetching stats:", error);
        }

    }

    return (
        <UserLayout>

            <main className="adminuiux-content has-sidebar">

                <div className="container mt-4">
                    <div className="row gx-3 align-items-center">
                        <div className="col-12 col-sm">
                            <nav aria-label="breadcrumb" className="mb-2">
                                <ol className="breadcrumb mb-0">
                                    <li className="breadcrumb-item bi"><Link href={route('user.dashboard')}><i
                                        className="bi bi-house-door me-1 fs-14"></i> Dashboard</Link></li>
                                    <li className="breadcrumb-item active bi" aria-current="page">Level Wise Business
                                    </li>
                                </ol>
                            </nav>
                            <h5>Level Wise Business</h5>
                        </div>
                        {/*<div className="col-12 col-sm-auto text-end py-3 py-sm-0">
                            <BreadcrumbsButtons/>
                        </div>*/}
                    </div>
                </div>


                <div className="container" id="main-content">


                    <div className="card adminuiux-card mt-4 mb-0">
                        <div className="card-body">
                            <div className="table-responsive">
                                <table
                                    className="table">
                                    <thead>
                                    <tr>
                                        <th className="text-center sorting">Level
                                        </th>
                                        <th className="text-center sorting">Business
                                        </th>
                                    </tr>
                                    </thead>
                                    <tbody>
                                    {(levels && levels.length > 0 ) ? (
                                        levels &&
                                        Object.entries(levels)
                                            .map(([key, item]) => (
                                                <tr className="odd" key={key}>
                                                    <td className="text-center">{item?.level}</td>
                                                    <td className="text-center">
                                                        {(item?.business!=null) ?  <span className="badge badge-light text-bg-theme-1">{item?.business}</span> : <button className="btn btn-outline-theme btn-sm" onClick={()=>calculate(key,item?.level)}>Calculate Now </button>}

                                                    </td>
                                                </tr>
                                                )
                                            )
                                        )
                                        :
                                        <>
                                        <tr>
                                        <td colSpan={2} className={'text-center'}>No Level Found</td>
                                        </tr>
                                        </>
                                    }
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>


                </div>


            </main>


        </UserLayout>
    );
}
