<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends \App\Base\Database\MigrationBase
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        $this->schema->create('user_stake_reset_logs', function (\App\Base\Database\BlueprintBase $table) {
            $table->id();
            $table->integer('user_id')->nullable();
            $table->integer('user_stake_id')->nullable();
            $table->text('comments')->nullable();
            $table->longText('data')->nullable();
            $table->createdAtTime();

            $table->unique(['user_id','user_stake_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('user_stake_reset_logs');
    }
};
