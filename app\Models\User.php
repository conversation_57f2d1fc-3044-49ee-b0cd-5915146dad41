<?php

namespace App\Models;

// use Illuminate\Contracts\Auth\MustVerifyEmail;
use App\Base\Traits\BaseModelTrait;
use App\Components\Helper;
use App\Constants\CommonConstants;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Str;
use Laravel\Sanctum\HasApiTokens;

class User extends Authenticatable
{
    const ROLE_ADMIN = 1;
    const ROLE_USER = 2;

    const STATUS_ACTIVE = 'active';
    const STATUS_INACTIVE = 'inactive';

    const STATUS_PROPERTIES = [
        self::STATUS_ACTIVE => ['class' => 'badge badge-success', 'text' => 'Active'],
        self::STATUS_INACTIVE => ['class' => 'badge badge-danger', 'text' => 'In-Active']
    ];

    const USER_ROLE_PROPERTIES = [
        self::ROLE_USER => ['class' => 'badge badge-success', 'text' => 'User'],
    ];

    /** @use HasFactory<\Database\Factories\UserFactory> */
    use HasFactory, Notifiable, HasApiTokens;

    use BaseModelTrait {
        BaseModelTrait::onCreating as modelOnCreating;
    }

    /**
     * The attributes that are mass assignable.
     *
     * @var list<string>
     */
    protected $fillable = [
        'name',
        'email',
        'password',
        'username',
        'referral_code',
        'referred_by',
        'direct_id',
        'joining_date','parent_ids','rank_id','left_leg','right_leg',
        'status','wallet_address','is_binary_created',
        'user_agent',
        'created_ip',
        'created_at',
        'updated_at',
    ];

    /**
     * Set the name attribute to title case (ucwords).
     */
    public function setNameAttribute($value)
    {
        $this->attributes['name'] = ucwords(strtolower(trim($value)));
    }

    public function onCreating()
    {
        if($this->username=="" || $this->username==null) {
            $this->username = self::generateUsername();
        }
        if(!$this->referral_code) {
            $this->referral_code = $this->username;
        }

        if ($this->status == null) {
            $this->status = self::STATUS_ACTIVE;
        }

        if($this->username!="backoffice") {
            if ($this->referred_by == "" || $this->referred_by == null) {
                $this->referred_by = CommonConstants::DEFAULT_REFERRAL_CODE;
            }

            if ($this->referred_by!="") {
                $sponsor = self::findBySponsorCode($this->referred_by);
                if ($sponsor) {
                    $this->referred_by = $sponsor->id;
                }
            }

            // Set direct_id from referred_by if direct_id is blank
            if ($this->direct_id == "" || $this->direct_id == null) {
                $this->direct_id = $this->referred_by;
            }
        }

        $this->income_x = CommonConstants::DEFAULT_INCOME_X;
        $this->remember_token = Str::random(50);
        $this->password = Hash::make($this->password);
        $this->email_verified_at = date(CommonConstants::PHP_DATE_FORMAT);

        $this->modelOnCreating();
    }

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var list<string>
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'email_verified_at' => 'datetime',
            'password' => 'hashed',
        ];
    }

    public static function fetchUsernameByID($id) {
        $model=self::query()->where('id','=',$id)->first();
        if($model) {
            return $model->username;
        }
        return '';
    }

    public function referred()
    {
        return $this->hasOne(User::class, 'id', 'referred_by');
    }

    public function direct()
    {
        return $this->hasOne(User::class, 'id', 'direct_id');
    }

    public function rank()
    {
        return $this->hasOne(RankSetting::class, 'id', 'rank_id');
    }

    public function getParentIds($user=null,$ids=null,$fromCache=true) {
        if($ids===null) {
            $ids=[];
        }
        if($user==null) {
            $user=$this;
        }
        if($user) {
            if($fromCache) {
                if($this->parent_ids=="" || $this->parent_ids==null) {
                    $fromCache=false;
                } else {
                    try {
                        $findAdmin=false;
                        $decodeParentIDs=json_decode($this->parent_ids,true);
                        if(is_array($decodeParentIDs) && count($decodeParentIDs)>0) {
                            foreach ($decodeParentIDs as $id) {
                                if($id===CommonConstants::ADMINISTRATIVE) {
                                    $findAdmin=true;
                                    break;
                                }
                            }
                        }

                        if(!$findAdmin) {
                            $fromCache=false;
                        } else {
                            if(is_array($decodeParentIDs) && count($decodeParentIDs)>0) {
                                foreach ($decodeParentIDs as $id) {
                                    if($id && $id!="") {
                                        $ids[] = $id;
                                    } else {
                                        break;
                                    }
                                }
                            }
                        }
                    } catch (\Exception $e) {

                    }
                }
            }

            if($fromCache && count($ids)>0) {
                return $ids;
            }

            if ($user->referred) {
                $parentId=(int)$user->referred->id;
                if(!in_array($parentId,$ids)) {
                    $ids[] = $parentId;
                }
                return $this->getParentIds($user->referred, $ids,$fromCache);
            }
        }
        return $ids;
    }

    public function getParentList($parent,$level=1,$levels=null) {
        if (!$levels) {
            $levels = [];
        }
        if ($parent) {
            $levels[$level] = $parent->id;
        } else {
            $levels[$level] = 0;
        }
        if ($level >= 50) {
            return $levels;
        } else {
            if ($parent) {
                if($parent->id==CommonConstants::ADMINISTRATIVE) {
                    $parent=null;
                } else {
                    $parent = $parent->referred;
                    if (!$parent) {
                        $parent = null;
                    }
                }
            }
            $level=$level+1;
            return $this->getParentList($parent, $level, $levels);
        }
    }

    public function generateBinaryData()
    {
        if ($this->is_binary_created == CommonConstants::YES) {
            return true;
        }

        $parent = $this->referred;
        if($parent) {
            if($parent->right_leg<=0) {
                die("Invalid Parent ID ".$parent->id);
            }

            DB::beginTransaction();
            try {
                $levels=$this->getParentList($parent);
                $this->parent_ids=json_encode($levels);
                $this->left_leg = $parent->right_leg;
                $this->right_leg = $this->left_leg + 1;
                $this->updated_at = date(CommonConstants::PHP_DATE_FORMAT);
                if ($this->update(['left_leg', 'right_leg','parent_ids', 'updated_at'])) {
                    DB::statement("update `users` set `total_directs`=`total_directs`+1 where `id`='" . $parent->id . "';");

                    DB::statement("update `users` set `left_leg`=`left_leg`+2 where `left_leg`>='" . $this->left_leg . "' and `id`!='" . $this->id . "';");
                    DB::statement("update `users` set `right_leg`=`right_leg`+2 where `right_leg`>='" . $this->left_leg . "' and `id`!='" . $this->id . "';");

                    DB::statement("update `users` set `total_team`=`total_team`+1 where `left_leg`<'" . $this->left_leg . "' and `right_leg`>'" . $this->left_leg . "';");
                    DB::statement("update `users` set `is_binary_created`=1 where `id`='" . $this->id . "';");
                }
                DB::commit();
            } catch (\Exception $e) {
                DB::rollBack();

                Log::insertLog([
                    'user_id' => CommonConstants::ADMINISTRATIVE,
                    'particulars' => 'Unable to create user binary tree #'.$this->id,
                    'type' => 'binary_tree_generation_error',
                    'data' => json_encode([
                        'user_id' => $this->id,
                        'message' => $e->getMessage(),
                        'trace' => $e->getTraceAsString(),
                    ])
                ]);
                throw new \Exception('Generate binary tree process stopped');
            }
        }
        return true;
    }

    public static function generateBinaryTree($endTime=null,$recursive=false) {
        if($endTime==null) {
            $endTime=strtotime("+1 minute")-5;
        }
        if(time()>=$endTime) {
            return true;
        }

        print "Start checking generateBinaryTree".PHP_EOL;
        $users=self::query()
            ->where('is_binary_created','=',CommonConstants::NO)
            ->orderBy("id")->limit(5)->get();
        if(count($users)>0) {
            /**
             * @var $user User
             */
            foreach ($users as $user) {
                if(time()>=$endTime) {
                    return true;
                }
                print "Start generateBinaryTree #".$user->id.PHP_EOL;
                $user->generateDefaultWallets();
                $user->generateBinaryData();
                sleep(rand(1,2));
            }
        } else {
            print "No pending user found for generateBinaryTree".PHP_EOL;
        }
        if($recursive) {
            sleep(rand(2,5));
            return self::generateBinaryTree($endTime,$recursive);
        } else {
            return true;
        }
    }

    public static function generateUsername()
    {
        $code=CommonConstants::TOKEN_CODE_USERNAME.rand(100000,999999);
        $checkUser=self::query()->where('username','=',$code)
            ->limit(1)->first();
        if($checkUser) {
            return self::generateUsername();
        }
        return $code;
    }
    public static function findByAddress($address)
    {
        return self::query()->where('wallet_address','=',$address)->limit(1)->first();
    }
    public static function findByUsername($username)
    {
        return self::query()->where('username','=',$username)->limit(1)->first();
    }
    public static function findBySponsorCode($sponsor_code)
    {
        return self::query()->where('referral_code','=',$sponsor_code)->limit(1)->first();
    }

    /**
     * Check if this user is a descendant (child) of the given parent user
     * @param User $parentUser
     * @return bool
     */
    public function isDescendantOf($parentUser)
    {
        if (!$parentUser) {
            return false;
        }

        $parentIds = $this->getParentIds($this, null, false);
        return in_array($parentUser->id, $parentIds);
    }

    /**
     * Get the last children of any first child of this user
     * @return array
     */
    public function getLastChildrenOfFirstChildren()
    {
        $result = null;

        try {
            // Get all first children (direct children) of this user
            $firstChild = self::query()
                ->where('referred_by', '=', $this->id)
                ->where('status', '=', User::STATUS_ACTIVE)
                ->orderBy('id')
                ->first();
            if($firstChild) {
                $lastChildren = $this->findLastChildren($firstChild);
                if (!empty($lastChildren)) {
                    $result = $lastChildren[0];
                }
            }
        } catch (\Exception $e) {
            /* \Log::error('Error in getLastChildrenOfFirstChildren', [
                'user_id' => $this->id,
                'username' => $this->username,
                'error' => $e->getMessage()
            ]); */
        }

        if(!$result) {
            $result=$this;
        }

        return $result;
    }

    /**
     * Recursively find the last children (leaf nodes) of a user
     * @param User $user
     * @return array
     */
    private function findLastChildren($user)
    {
        $children = self::query()
            ->where('referred_by', '=', $user->id)
            ->orderBy('id')
            ->get();

        // If no children, this user is a leaf node
        if ($children->count() == 0) {
            return [$user];
        }

        // If has children, recursively find their last children
        $lastChildren = [];
        foreach ($children as $child) {
            $childLastChildren = $this->findLastChildren($child);
            $lastChildren = array_merge($lastChildren, $childLastChildren);
        }

        return $lastChildren;
    }

    public function fetchOpenedLevelLogs() {
        $logs=[];
        if($this->active_investment>0) {
            if($this->active_directs>0) {
                if($this->opened_levels>=12) {
                    $logs[]='Congrats!! You have achieved the highest level';
                } else {
                    try {
                        $log=UserOpenLevelLog::query()
                            ->where('user_id','=',$this->id)
                            ->limit(1)->first();
                        if($log) {
                            $decode=json_decode($log->data,true);
                            if(is_array($decode) && array_key_exists('directs',$decode)) {
                                $logs[]='You have <strong>'.count($decode['directs']).'</strong> active directs';
                            }
                            if(is_array($decode) && array_key_exists('directs_business',$decode)) {
                                $logs[]='You have achieved <strong>'.Helper::printAmount($decode['directs_business']).'</strong> direct business';
                            }
                        }
                    } catch (\Exception $e) {

                    }
                }
            } else {
                $logs[]='You do not have any active direct';
            }
        } else {
            $logs[]='You do not have any active investment';
        }
        return $logs;
    }

    public function fetchRankLogs() {
        $logs=[];
        if($this->active_investment>0) {
            if($this->total_directs>=3) {
                if($this->rank_id && $this->rank_id>=12) {
                    $logs[]='Congrats!! You have achieved the highest rank';
                } else {
                    try {
                        $log=UserRankLog::query()
                            ->where('user_id','=',$this->id)
                            ->limit(1)->first();
                        if($log) {
                            $decode=json_decode($log->data,true);
                            if(is_array($decode) && array_key_exists('stats',$decode)) {
                                if(array_key_exists('user_directs',$decode['stats'])) {
                                    $logs[] = 'You have <strong>' . $decode['stats']['user_directs'] . '</strong> active directs';
                                }
                                if(array_key_exists('power_leg_user_id',$decode['stats'])) {
                                    if($decode['stats']['power_leg_user_id']) {
                                        $powerUser = User::query()->where('id', '=', $decode['stats']['power_leg_user_id'])->first();
                                        if($powerUser) {
                                            $logs[] = 'Power leg user is <strong>' . $powerUser->username . '</strong>';
                                            if(array_key_exists('rank_need_power_leg_business',$decode['stats']) && array_key_exists('user_power_leg_business',$decode['stats'])) {
                                                $logs[] = 'In power leg you need <strong>' . Helper::printAmount($decode['stats']['rank_need_power_leg_business']) . '</strong>, but you have achieved <strong>' . Helper::printAmount($decode['stats']['user_power_leg_business']) . '</strong>';
                                            }
                                        }
                                    }
                                }

                                if(array_key_exists('second_power_leg_user_id',$decode['stats'])) {
                                    if($decode['stats']['second_power_leg_user_id']) {
                                        $secondPowerUser = User::query()->where('id', '=', $decode['stats']['second_power_leg_user_id'])->first();
                                        if($secondPowerUser) {
                                            $logs[] = 'Second power leg user is <strong>' . $secondPowerUser->username . '</strong>';
                                            if(array_key_exists('rank_need_second_power_leg_business',$decode['stats']) && array_key_exists('user_second_power_leg_business',$decode['stats'])) {
                                                $logs[] = 'In second power leg you need <strong>' . Helper::printAmount($decode['stats']['rank_need_second_power_leg_business']) . '</strong>, but you have achieved <strong>' . Helper::printAmount($decode['stats']['user_second_power_leg_business']) . '</strong>';
                                            }
                                        }
                                    }
                                }

                                if(array_key_exists('rank_need_other_leg_business',$decode['stats']) && array_key_exists('user_other_leg_business',$decode['stats'])) {
                                    $logs[] = 'In the rest leg\'s you need <strong>' . Helper::printAmount($decode['stats']['rank_need_other_leg_business']) . '</strong>, but you have achieved <strong>' . Helper::printAmount($decode['stats']['user_other_leg_business']) . '</strong>';
                                }
                            }
                        }
                    } catch (\Exception $e) {

                    }
                }
            } else {
                $logs[]='You have to add at least 3 active directs';
            }
        } else {
            $logs[]='You do not have any active investment';
        }
        return $logs;
    }

    public function getLevelWiseBusiness($level) {
        $fetchAmount=User::query()
            ->whereRaw("parent_ids like '%\"".$level."\":".$this->id.",%'")
            ->sum("active_investment");
        if(!$fetchAmount || $fetchAmount=="") {
            $fetchAmount=0;
        }
        return round($fetchAmount,CommonConstants::USD_PRECISION);
    }

    public static function shortAddress($address)
    {
        $charEachSide = 5;
        $shortAddress = substr($address, 0, $charEachSide);
        $shortAddress .= "...";
        $shortAddress .= substr($address, -$charEachSide);

        return $shortAddress;
    }

    public function generateReferralLink() {
        if($this->active_investment>0 || true) {
            return env('APP_URL') . "/?sponsor=" . $this->referral_code;
        }
        return '';
    }

    public function getActiveTeamBusinessAttribute() {
        $count=0;
        if($this->total_team>0) {
            $count=self::query()
                ->where('active_investment','>',0)
                ->where('left_leg','>',$this->left_leg)
                ->where('right_leg','<',$this->right_leg)
                ->where('is_zero_pin','=',CommonConstants::NO)
                ->sum("active_investment");
            if(!$count || $count=="") {
                $count=0;
            }
        }
        return $count;
    }


    public function treeData() {
        if($this->is_zero_pin) {
            $totalBusiness=round($this->active_investment+$this->team_business,CommonConstants::USD_PRECISION);
        } else {
            $totalBusiness=round($this->lifetime_investment+$this->team_business,CommonConstants::USD_PRECISION);
        }

        $response = [
            'parentId'=>($this->referred ? $this->referred->referral_code : null),
            'id'=>$this->referral_code,
            'referral_code'=>$this->referral_code,
            'name'=>$this->name,
            'username'=>$this->username,
            "imgUrl"=>"https://ui-avatars.com/api/?size=450&background=e34a42&color=fff&name=John",
            "_pagingStep"=>2000,
            "data"=>'Username: '.$this->username.'<br /><br />Address: '.$this->wallet_address.'<br />Self Investment: '.Helper::printAmount($this->active_investment).'<br />Team Business: '.Helper::printAmount($this->team_business).'<br />Total Business: '.Helper::printAmount($totalBusiness),
            "html_data"=>'<div style="font-size: 11px; line-height: 1.4;">
                                       <div style="display: flex; justify-content: space-between; margin-bottom: 8px; background: rgba(255,255,255,0.1); padding: 5px 8px; border-radius: 5px;">
                                           <span>Username</span>
                                           <span style="font-weight: bold;">'.$this->username.'</span>
                                       </div>
                                       <div style="display: flex; justify-content: space-between; margin-bottom: 8px; background: rgba(255,255,255,0.1); padding: 5px 8px; border-radius: 5px;">
                                           <span>Address</span>
                                           <span style="font-weight: bold;">'.self::shortAddress($this->wallet_address).'</span>
                                       </div>
                                       <div style="display: flex; justify-content: space-between; margin-bottom: 8px; background: rgba(255,255,255,0.1); padding: 5px 8px; border-radius: 5px;">
                                           <span>Self Investment</span>
                                           <span style="font-weight: bold;">'.Helper::printAmount($this->active_investment).'</span>
                                       </div>
                                       <div style="display: flex; justify-content: space-between; margin-bottom: 8px; background: rgba(255,255,255,0.1); padding: 5px 8px; border-radius: 5px;">
                                           <span>Team Business</span>
                                           <span style="font-weight: bold;">'.Helper::printAmount($this->team_business).'</span>
                                       </div>
                                       <div style="display: flex; justify-content: space-between; margin-bottom: 8px; background: rgba(255,255,255,0.1); padding: 5px 8px; border-radius: 5px;">
                                           <span>Total Business</span>
                                           <span style="font-weight: bold;">'.Helper::printAmount($totalBusiness).'</span>
                                       </div>
                                   </div>',
            "link"=>$this->generateReferralLink(),
            "isTopup"=>($this->active_investment>0 ? true : false),
            "isActivate"=>($this->active_investment>0 && $this->status==self::STATUS_ACTIVE ? true : false),
        ];
        return $response;
    }



    public function createTreeData() {
        $response=[];
        $response[]=$this->treeData();
        $response[0]['parentId']=null;
        $response[0]['level_id']=1;

        $directs=self::query()->where('referred_by','=',$this->id)->orderBy("id")->get();
        if(count($directs)>0) {
            foreach ($directs as $direct) {
                $data=$direct->treeData();
                $data['level_id']=2;
                $response[]=$data;

                $children=self::query()->where('referred_by','=',$direct->id)->orderBy("id")->get();
                if(count($children)>0) {
                    foreach ($children as $child) {
                        $data=$child->treeData();
                        $data['level_id']=3;
                        $response[] = $data;
                    }
                }
            }
        }
        return $response;
    }

    public function apiData()
    {
        if($this->is_zero_pin) {
            $totalBusiness=round($this->active_investment+$this->team_business,CommonConstants::USD_PRECISION);
        } else {
            $totalBusiness=round($this->lifetime_investment+$this->team_business,CommonConstants::USD_PRECISION);
        }

        $remainingAmount=$this->getRemainingAmount();

        $stakedTokens=UserStakeReleaseChart::query()
            ->where('user_id','=',$this->id)
            ->where('is_released','=',CommonConstants::NO)
            ->sum("tokens");
        if(!$stakedTokens) {
            $stakedTokens=0;
        }
        $pendingDirectReferralTokens=UserStakeReferralChart::query()
            ->where('user_id','=',$this->id)
            ->where('is_released','=',CommonConstants::NO)
            ->sum("tokens");
        if(!$pendingDirectReferralTokens) {
            $pendingDirectReferralTokens=0;
        }
        return [
            'name'=>$this->name,
            'username'=>$this->username,
            'referral_code'=>$this->referral_code,
            'address'=>$this->wallet_address,
            'email'=>($this->email==null ? '' : $this->email),
            'short_address'=>self::shortAddress($this->wallet_address),
            'referral_link'=>$this->generateReferralLink(),
            'joining_date'=>($this->joining_date==null ? '' : $this->joining_date),
            'first_activation'=>($this->first_activation==null ? '' : $this->first_activation),
            'formatted_first_activation'=>($this->first_activation==null ? '' : Helper::printNumber($this->first_activation)),
            'active_investment'=>$this->active_investment,
            'lifetime_investment'=>$this->lifetime_investment,
            'total_earnings'=>$this->total_earnings,
            'lifetime_earnings'=>$this->lifetime_earnings,
            'total_working_earnings'=>$this->total_working_earnings,
            'lifetime_working_earnings'=>$this->lifetime_working_earnings,
            'formatted_active_investment'=>Helper::printAmount($this->active_investment),
            'formatted_lifetime_investment'=>Helper::printAmount($this->lifetime_investment),
            'formatted_total_earnings'=>Helper::printAmount($this->total_earnings),
            'formatted_lifetime_earnings'=>Helper::printAmount($this->lifetime_earnings),
            'formatted_total_working_earnings'=>Helper::printAmount($this->total_working_earnings),
            'formatted_lifetime_working_earnings'=>Helper::printAmount($this->lifetime_working_earnings),
            'rank'=>($this->rank_id==null ? '' : $this->rank->name),
            'rank_id'=>$this->rank_id,
            'income_x'=>$this->income_x,
            'opened_levels'=>$this->opened_levels,
            'total_team'=>$this->total_team,
            'total_directs'=>$this->total_directs,
            'active_directs'=>$this->active_directs,
            'team_business'=>$this->team_business,
            'total_business'=>$totalBusiness,
            'current_month_business'=>$this->current_month_business,
            'formatted_team_business'=>Helper::printAmount($this->team_business),
            'formatted_total_business'=>Helper::printAmount($totalBusiness),
            'formatted_current_month_business'=>Helper::printAmount($this->current_month_business),
            'remaining_limit'=>$remainingAmount,
            'formatted_remaining_limit'=>Helper::printAmount($remainingAmount),
            'staked_tokens'=>Helper::printNumber($stakedTokens,CommonConstants::TOKEN_PRECISION),
            'pending_direct_referral_tokens'=>Helper::printNumber($pendingDirectReferralTokens,CommonConstants::TOKEN_PRECISION),
        ];
    }

    public function getInvestmentRemainingLimit() {
        $maxLimit=Setting::getValue(Setting::SETTING_MAXIMUM_INVESTMENT);
        return round(($maxLimit-$this->lifetime_investment),CommonConstants::USD_PRECISION);
    }

    public function generateDefaultWallets()
    {
        $wallets=Wallet::query()->orderBy("id")->get();
        if(count($wallets)>0) {
            foreach ($wallets as $wallet) {
                UserWallet::fetchUserWallet($this->id,$wallet->id);
            }
        }
        return true;
    }

    public function getUserWallet($wallet_id)
    {
        return UserWallet::fetchUserWallet($this->id,$wallet_id);
    }

    public function addTransaction(
        $particulars,
        $type_id,
        $is_debit,
        $amount,
        $wallet_id,
        $data = null,
        $reference_id=null,
        $time=null,
        $allow_duplicate = false
    )
    {
        $userWallet = $this->getUserWallet($wallet_id);
        $ut = new Transaction();
        $ut->user_id = $this->id;
        $ut->type_id = $type_id;
        $ut->wallet_id = $wallet_id;
        $ut->user_wallet_id = $userWallet->id;
        $ut->is_debit = $is_debit;
        $ut->particulars = $particulars;
        $ut->amount = $amount;
        $ut->time = date(CommonConstants::PHP_DATE_FORMAT);
        $ut->data=null;
        $ut->reference_id=null;
        if($data!=null) {
            if(is_array($data)) {
                $data=json_encode($data);
            }
            $ut->data=$data;
        }
        if($time) {
            $ut->time = $time;
        }
        if($reference_id) {
            $ut->reference_id = $reference_id;
        }
        $ut->before_balance=bcadd($userWallet->balance,0,CommonConstants::ETH_PRECISION);
        if($ut->is_debit) {
            $ut->after_balance=bcsub($ut->before_balance,$ut->amount,CommonConstants::ETH_PRECISION);
        } else {
            $ut->after_balance=bcadd($ut->before_balance,$ut->amount,CommonConstants::ETH_PRECISION);
        }

        if(!$allow_duplicate) {
            $query=Transaction::query()
                ->where('user_id','=',$ut->user_id)
                ->where('type_id','=',$ut->type_id)
                ->where('wallet_id','=',$ut->wallet_id)
                ->where('is_debit','=',$ut->is_debit)
                ->where('amount','=',$ut->amount);
            if($reference_id) {
                $query->where('reference_id','=',$ut->reference_id);
            }
            $checkDuplicate=$query->first();
            if($checkDuplicate) {
                Log::insertLog([
                    'user_id' => $ut->user_id,
                    'particulars' => 'Duplicate request prevented',
                    'type' => 'duplicate_request',
                    'data' => json_encode([
                        'user_id' => $ut->user_id,
                        'type_id' => $ut->type_id,
                        'wallet_id' => $ut->wallet_id,
                        'is_debit' => $ut->is_debit,
                        'amount' => $ut->amount,
                        'reference_id' => $ut->reference_id,
                        'particulars' => $ut->particulars,
                        'data' => $ut->data,
                    ])
                ]);
                return false;
            }
        }

        if($ut->save()) {
            $userWallet->balance=($ut->after_balance*1);
            $userWallet->update(['balance']);

            switch ($ut->type_id) {
                case TransactionType::TRANSACTION_TYPE_DIVIDEND:
                case TransactionType::TRANSACTION_TYPE_DIVIDEND_COMMISSION:
                case TransactionType::TRANSACTION_TYPE_RANK_REWARD:
                case TransactionType::TRANSACTION_TYPE_ROYALTY:
                    if($ut->is_debit) {
                        DB::statement("update `users` set `total_earnings`=`total_earnings`-'".$ut->amount."',`lifetime_earnings`=`lifetime_earnings`-'".$ut->amount."' where `id`='" . $ut->user_id . "';");
                    } else {
                        DB::statement("update `users` set `total_earnings`=`total_earnings`+'".$ut->amount."',`lifetime_earnings`=`lifetime_earnings`+'".$ut->amount."' where `id`='" . $ut->user_id . "';");
                    }
                    break;
            }

            switch ($ut->type_id) {
                case TransactionType::TRANSACTION_TYPE_DIVIDEND_COMMISSION:
                case TransactionType::TRANSACTION_TYPE_RANK_REWARD:
                case TransactionType::TRANSACTION_TYPE_ROYALTY:
                    if($ut->is_debit) {
                        DB::statement("update `users` set `total_working_earnings`=`total_working_earnings`-'".$ut->amount."',`lifetime_working_earnings`=`lifetime_working_earnings`-'".$ut->amount."' where `id`='" . $ut->user_id . "';");
                    } else {
                        DB::statement("update `users` set `total_working_earnings`=`total_working_earnings`+'".$ut->amount."',`lifetime_working_earnings`=`lifetime_working_earnings`+'".$ut->amount."' where `id`='" . $ut->user_id . "';");
                    }
                    break;
            }
            return $ut;
        }
        return false;
    }

    public function getRemainingAmount($amount=null) {
        if($this->active_investment>0) {
            if (!$amount) {
                $amount = $this->total_earnings;
            }
            $activeAmount=round($this->active_investment,CommonConstants::USD_PRECISION);
            $multiplier=$this->income_x;
            if($multiplier!="" && $multiplier>0) {
                $activeAmount=round($activeAmount*$multiplier,CommonConstants::USD_PRECISION);
            }
            return round(($activeAmount-$amount),CommonConstants::USD_PRECISION);
        }
        return 0;
    }

    public function getMonthWiseTeamBusiness() {
        $stats=[];
        $totalTeamBusiness=0;
        $startMonth="2025-06-01";

        UserDbLog::saveDailyLogs($this->id);

        for ($i=1;$i<=50; $i++) {
            $monthEndDate=date(CommonConstants::PHP_DATE_FORMAT_SHORT,strtotime("+".$i." month",strtotime($startMonth))-1);
            if(strtotime($monthEndDate)>strtotime(date("Y-m-t"))) {
                break;
            }
            $logData=UserDbLog::query()->where('user_id','=',$this->id)
                ->where('log_date','<=',$monthEndDate)
                ->orderByDesc("log_date")->limit(1)->first();
            if($logData) {
                $finalStats=['month_end_date' => $monthEndDate,'month'=>date("M Y",strtotime($monthEndDate))];
                try {
                    $decodeStats=json_decode($logData->log_data,true);
                    if(is_array($decodeStats) && count($decodeStats)>0){
                        //print_r($decodeStats);die;
                        if(array_key_exists('team_business',$decodeStats)) {
                            $finalStats['team_business']=$decodeStats['team_business'];
                            if($totalTeamBusiness>0) {
                                $finalStats['team_business_diff']=round($finalStats['team_business']-$totalTeamBusiness,4);
                            } else {
                                $finalStats['team_business_diff']=$finalStats['team_business'];
                            }

                            $finalStats['formatted_team_business']=Helper::printAmount($finalStats['team_business']);
                            $finalStats['formatted_team_business_diff']=Helper::printAmount($finalStats['team_business_diff']);
                            $totalTeamBusiness=$finalStats['team_business'];
                        }
                    }
                } catch (\Exception $ee) {

                }
                $stats[] = $finalStats;
            }
        }
        return $stats;
    }
}
