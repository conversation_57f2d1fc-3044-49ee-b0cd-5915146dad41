<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends \App\Base\Database\MigrationBase
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        $this->schema->create('rank_settings', function (\App\Base\Database\BlueprintBase $table) {
            $table->id();
            $table->string('name')->nullable()->index();
            $table->double('business')->comment('in USD');
            $table->double('total_business')->comment('in USD');
            $table->double('incentive')->comment('in USD');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('rank_settings');
    }
};
