<?php

namespace App\Models;

use App\Base\Model\BaseModel;
use App\Constants\CommonConstants;
use Illuminate\Database\Eloquent\Model;

class UserTicketMessage extends BaseModel
{
    protected $fillable = [
        'user_id',
        'user_ticket_id',
        'message',
        'created_at',
    ];

    public function onCreating()
    {
        if($this->message!="") {
            $this->message=trim(addslashes(strip_tags($this->message)));
        }
        parent::onCreating(); // TODO: Change the autogenerated stub
    }

    public function user()
    {
        return $this->hasOne(User::class, 'id', 'user_id');
    }

    public function ticket()
    {
        return $this->hasOne(UserTicket::class, 'id', 'user_ticket_id');
    }

    public function apiData($userID=null) {
        return [
            'type'=>($userID==$this->user_id ? 'user' : 'support'),
            'message'=>$this->message,
            'created_at'=>date(CommonConstants::PHP_DATE_FORMAT,strtotime($this->created_at)),
        ];
    }
}
