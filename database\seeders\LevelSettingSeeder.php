<?php

namespace Database\Seeders;

use App\Constants\CommonConstants;
use App\Models\Country;
use App\Models\LevelSetting;
use App\Models\Setting;
use App\Models\TopupSetting;
use App\Models\User;
use App\Models\UserWallet;
use App\Models\Wallet;
use Faker\Provider\UserAgent;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;

class LevelSettingSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $rows=[
            ['id'=>1,'incentive'=>20,'directs'=>1,'opened_levels'=>1,'direct_business'=>100],
            ['id'=>2,'incentive'=>15,'directs'=>2,'opened_levels'=>2,'direct_business'=>200],
            ['id'=>3,'incentive'=>10,'directs'=>3,'opened_levels'=>3,'direct_business'=>300],
            ['id'=>4,'incentive'=>5,'directs'=>4,'opened_levels'=>4,'direct_business'=>400],
            ['id'=>5,'incentive'=>3,'directs'=>5,'opened_levels'=>5,'direct_business'=>500],
            ['id'=>6,'incentive'=>3,'directs'=>6,'opened_levels'=>6,'direct_business'=>600],
            ['id'=>7,'incentive'=>3,'directs'=>7,'opened_levels'=>7,'direct_business'=>700],
            ['id'=>8,'incentive'=>3,'directs'=>8,'opened_levels'=>8,'direct_business'=>800],
            ['id'=>9,'incentive'=>2,'directs'=>9,'opened_levels'=>9,'direct_business'=>900],
            ['id'=>10,'incentive'=>2,'directs'=>10,'opened_levels'=>10,'direct_business'=>1000],
            ['id'=>11,'incentive'=>2,'directs'=>11,'opened_levels'=>11,'direct_business'=>1100],
            ['id'=>12,'incentive'=>2,'directs'=>12,'opened_levels'=>12,'direct_business'=>1200],
        ];

        foreach ($rows as $row) {
            if(!LevelSetting::query()->where('id','=',$row['id'])->first()) {
                LevelSetting::create($row);
            }
        }
    }
}
