<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends \App\Base\Database\MigrationBase
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        $this->schema->create('blockchain_transactions', function (\App\Base\Database\BlueprintBase $table) {
            $table->id();
            $table->bigInteger('ref_no')->unique();
            $table->integer('user_id')->nullable()->index();
            $table->string('hash')->unique()->index();
            $table->integer('is_processed')->default(0)->index();
            $table->integer('is_correct')->default(0)->index();
            $table->string('method')->nullable()->comment('contract method')->index();
            $table->bigInteger('expiry_time')->nullable()->index();
            $table->text('comments')->nullable();
            $table->text('raw_data')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('blockchain_transactions');
    }
};
