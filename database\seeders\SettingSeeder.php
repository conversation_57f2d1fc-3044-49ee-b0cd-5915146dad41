<?php

namespace Database\Seeders;

use App\Constants\CommonConstants;
use App\Models\Country;
use App\Models\Setting;
use App\Models\User;
use App\Models\UserWallet;
use App\Models\Wallet;
use Faker\Provider\UserAgent;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;

class SettingSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $rows=[
            ['id'=>Setting::SETTING_MINIMUM_INVESTMENT,'name'=>'Minimum Investment (in USD)','value'=>'50','type'=>CommonConstants::ENUM_SETTINGS_TYPE_INPUT],
            ['id'=>Setting::SETTING_MAXIMUM_INVESTMENT,'name'=>'Maximum Investment (in USD)','value'=>'3000','type'=>CommonConstants::ENUM_SETTINGS_TYPE_INPUT],
            ['id'=>Setting::SETTING_MONTHLY_DIVIDEND,'name'=>'Monthly Dividend (in %)','value'=>'5','type'=>CommonConstants::ENUM_SETTINGS_TYPE_INPUT],
            ['id'=>Setting::SETTING_STAKE_DURATION,'name'=>'Stake Duration (in Months)','value'=>'18','type'=>CommonConstants::ENUM_SETTINGS_TYPE_INPUT],
            ['id'=>Setting::SETTING_STAKE_MATURITY_DURATION,'name'=>'Stake Maturity Duration (in Months)','value'=>'10','type'=>CommonConstants::ENUM_SETTINGS_TYPE_INPUT],
            ['id'=>Setting::SETTING_REFERRAL_BONUS_RELEASE_DURATION,'name'=>'Referral Bonus Release Duration (in Months)','value'=>'20','type'=>CommonConstants::ENUM_SETTINGS_TYPE_INPUT],
            ['id'=>Setting::SETTING_MINIMUM_WITHDRAWAL,'name'=>'Minimum Withdrawal (in USD)','value'=>'10','type'=>CommonConstants::ENUM_SETTINGS_TYPE_INPUT],
            ['id'=>Setting::SETTING_WITHDRAWAL_MULTIPLE,'name'=>'Withdrawal Multiple (in USD)','value'=>'10','type'=>CommonConstants::ENUM_SETTINGS_TYPE_INPUT],
            ['id'=>Setting::SETTING_DAILY_SUPPLY,'name'=>'Daily Supply (in Tokens)','value'=>'666','type'=>CommonConstants::ENUM_SETTINGS_TYPE_INPUT],
        ];

        foreach ($rows as $row) {
            if(!Setting::query()->where('id','=',$row['id'])->first()) {
                Setting::create($row);
            }
        }
    }
}
