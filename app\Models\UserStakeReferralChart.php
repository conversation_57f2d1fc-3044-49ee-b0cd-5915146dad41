<?php

namespace App\Models;

use App\Base\Model\BaseModel;
use App\Constants\CommonConstants;

class UserStakeReferralChart extends BaseModel
{
    protected $fillable = [
        'ref_no',
        'user_id','user_stake_id','tokens','txn_hash',
        'release_date','is_released','created_at','updated_at',
    ];

    public function user()
    {
        return $this->hasOne(User::class, 'id', 'user_id');
    }

    public function userStake()
    {
        return $this->hasOne(UserStake::class, 'id', 'user_stake_id');
    }

    public static function makeChart($user,$userStake,$tokens) {
        try {
            $alreadyExist=self::query()
                ->where('user_id','=',$user->id)
                ->where('user_stake_id','=',$userStake->id)
                ->first();
            if($alreadyExist) {
                throw new \Exception('Stake #'.$userStake->id.' chart already exist');
            }

            if(!$tokens || $tokens<=0) {
                throw new \Exception('Invalid tokens ('.$tokens.') for stake #'.$userStake->id.' chart');
            }

            $duration=Setting::getValue(Setting::SETTING_REFERRAL_BONUS_RELEASE_DURATION);
            if($duration>0) {
                $tokenAmount=round(($tokens/$duration),CommonConstants::TOKEN_PRECISION);
                $startDate=date("Y-m")."-01";
                $startDate=date(CommonConstants::PHP_DATE_FORMAT_SHORT,strtotime("+1 month",strtotime($startDate)));
                for ($i=0;$i<$duration; $i++) {
                    $model=new UserStakeReferralChart();
                    $model->user_id=$user->id;
                    $model->user_stake_id=$userStake->id;
                    $model->tokens=$tokenAmount;
                    //$model->release_date=date(CommonConstants::PHP_DATE_FORMAT_SHORT,strtotime("+".$i." months",strtotime($userStake->time)));
                    $model->release_date=date(CommonConstants::PHP_DATE_FORMAT_SHORT,strtotime("+".$i." months",strtotime($startDate)));
                    $model->save();
                }
            }
            return true;
        } catch (\Exception $e) {
            Log::insertLog([
                'user_id' => CommonConstants::ADMINISTRATIVE,
                'particulars' => 'Unable to make stake release chart #'.$userStake->id,
                'type' => 'user_stake_referral_chart_error',
                'data' => json_encode([
                    'user_stake_id' => $userStake->id,
                    'error' => $e->getMessage(),
                    'trace' => $e->getTraceAsString(),
                ])
            ]);
        }
        return false;
    }
}
