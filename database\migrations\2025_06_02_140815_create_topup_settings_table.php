<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends \App\Base\Database\MigrationBase
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        $this->schema->create('topup_settings', function (\App\Base\Database\BlueprintBase $table) {
            $table->id();
            $table->integer('counter')->unique();
            $table->integer('income_x');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('topup_settings');
    }
};
