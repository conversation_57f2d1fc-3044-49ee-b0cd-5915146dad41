import { createAppKit } from '@reown/appkit/react'

import { WagmiProvider } from 'wagmi'
import { arbitrum, mainnet } from '@reown/appkit/networks'
import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
import { WagmiAdapter } from '@reown/appkit-adapter-wagmi'

// 0. Setup queryClient
const queryClient = new QueryClient()

// 1. Get projectId from https://cloud.reown.com
const projectId = import.meta.env.VITE_REOWN_PROJECT_ID;

// 2. Create a metadata object - optional
const metadata = {
    name: 'Drixe',
    description: 'Drixe App',
    url: ''+import.meta.env.VITE_REOWN_APP_URL,
    icons: ['https://avatars.githubusercontent.com/u/179229932']
}
const bscMainnet = {
    id: 56,
    caipNetworkId: 'eip155:56',
    chainNamespace: 'eip155',
    name: 'Binance Smart Chain',
    nativeCurrency: {
        decimals: 18,
        name: '<PERSON><PERSON><PERSON>',
        symbol: 'BNB',
    },
    rpcUrls: {
        default: {
            http: ['https://bsc-dataseed.binance.org'],
            webSocket: ['wss://bsc-ws-node.nariox.org:443'],
        },
    },
    blockExplorers: {
        default: {
            name: 'BscScan',
            url: 'https://bscscan.com',
        },
    },
    contracts: {
    },
}
const bscTestnet = {
    id: 97,
    caipNetworkId: 'eip155:97',
    chainNamespace: 'eip155',
    name: 'Binance Smart Chain - Testnet',
    nativeCurrency: {
        decimals: 18,
        name: 'BNB',
        symbol: 'BNB',
    },
    rpcUrls: {
        default: {
            http: ['https://bsc-testnet-dataseed.bnbchain.org'],
            webSocket: ['wss://bsc-testnet-dataseed.bnbchain.org'],
        },
    },
    blockExplorers: {
        default: {
            name: 'BscScan',
            url: 'https://testnet.bscscan.com',
        },
    },
    contracts: {
    },
}

// 3. Set the networks
// const networks = [mainnet, arbitrum]
// const networks = [bscMainnet]
const networks = [bscTestnet]

// 4. Create Wagmi Adapter
const wagmiAdapter = new WagmiAdapter({
    networks,
    projectId,
    ssr: true
})

// 5. Create modal
createAppKit({
    adapters: [wagmiAdapter],
    networks,
    projectId,
    metadata,
    features: {
        connectMethodsOrder: ["wallet"],
        analytics: true // Optional - defaults to your Cloud configuration
    }
})

export function AppKitProvider({ children }) {
    return (
        <WagmiProvider config={wagmiAdapter.wagmiConfig}>
            <QueryClientProvider client={queryClient}>{children}</QueryClientProvider>
        </WagmiProvider>
    )
}
