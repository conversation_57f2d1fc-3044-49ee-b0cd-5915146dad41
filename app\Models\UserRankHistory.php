<?php

namespace App\Models;

use App\Base\Model\BaseModel;
use App\Components\Helper;
use App\Constants\CommonConstants;
use Illuminate\Support\Facades\DB;

class UserRankHistory extends BaseModel
{
    protected $fillable = [
        'ref_no',
        'user_id',
        'rank_id',
        'is_admin',
        'created_at',
        'updated_at',
    ];

    public function user()
    {
        return $this->hasOne(User::class, 'id', 'user_id');
    }

    public function rank()
    {
        return $this->hasOne(RankSetting::class, 'id', 'rank_id');
    }

    public static function addRankHistory($user_id,$rank_id,$isAdmin=false) {
        $model=self::query()->where('user_id','=',$user_id)
            ->where('rank_id','=',$rank_id)->limit(1)->first();
        if($model) {
            return false;
        } else {
            $model=new UserRankHistory();
            $model->user_id=$user_id;
            $model->rank_id=$rank_id;
            $model->is_admin=0;
            if($isAdmin) {
                $model->is_admin=1;
            }
            if($model->save()) {
                DB::statement("update `users` set `rank_id`='".$rank_id."' where `id`='".$user_id."'");

                if(!$model->is_admin) {
                    $user=$model->user;
                    $amount=$model->rank->incentive;
                    $amount = Helper::fetchUpdatedAmount($amount,$user->income_x,$user->active_investment,$user->total_earnings);
                    $user->addTransaction(
                        'Rank #' . $model->rank_id . ' achieved reward',
                        TransactionType::TRANSACTION_TYPE_RANK_REWARD,
                        CommonConstants::CREDIT,
                        $amount,
                        Wallet::WALLET_EARNINGS,
                        json_encode(['rank_id' => $model->rank_id]),
                        $model->rank_id
                    );
                }
                return true;
            }
        }
        return false;
    }
}
