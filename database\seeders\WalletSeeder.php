<?php

namespace Database\Seeders;

use App\Constants\CommonConstants;
use App\Models\Country;
use App\Models\Setting;
use App\Models\User;
use App\Models\UserWallet;
use App\Models\Wallet;
use Faker\Provider\UserAgent;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;

class WalletSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $rows=[
            ['id'=>Wallet::WALLET_USD,'name'=>'USD'],
            ['id'=>Wallet::WALLET_EARNINGS,'name'=>'Earnings'],
            ['id'=>Wallet::WALLET_WITHDRAWAL,'name'=>'Withdrawal'],
            ['id'=>Wallet::WALLET_DIVIDEND,'name'=>'Dividend'],
        ];

        foreach ($rows as $row) {
            if(!Wallet::query()->where('id','=',$row['id'])->first()) {
                Wallet::create($row);
            }
        }
    }
}
