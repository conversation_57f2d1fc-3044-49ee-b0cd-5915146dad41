<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends \App\Base\Database\MigrationBase
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        $this->schema->create('users', function (Blueprint $table) {
            $table->id();
            $table->string('name')->nullable();
            $table->string('wallet_address')->nullable()->unique();
            $table->string('username')->unique();
            $table->string('referral_code')->nullable()->unique();
            $table->string('email')->index();
            $table->timestamp('email_verified_at')->nullable();
            $table->string('password');
            $table->date('joining_date')->nullable();
            $table->text('parent_ids')->nullable();
            $table->integer('left_leg')->default(-1)->index();
            $table->integer('right_leg')->default(-1)->index();
            $table->integer('total_team')->default(0);
            $table->integer('total_directs')->default(0);
            $table->integer('active_directs')->default(0);
            $table->integer('is_binary_created')->default(0);
            $table->integer('opened_levels')->default(0);
            $table->integer('income_x')->default(\App\Constants\CommonConstants::DEFAULT_INCOME_X);
            $table->integer('is_zero_pin')->default(0);
            $table->integer('is_power_leg')->default(0);
            $table->decimal('active_investment',48,8)->default(0);
            $table->decimal('lifetime_investment',48,8)->default(0);
            $table->decimal('total_earnings',48,8)->default(0);
            $table->decimal('lifetime_earnings',48,8)->default(0);
            $table->decimal('total_working_earnings',48,8)->default(0);
            $table->decimal('lifetime_working_earnings',48,8)->default(0);
            $table->decimal('total_withdrawals',48,8)->default(0);
            $table->decimal('team_business',48,8)->default(0);
            $table->decimal('current_month_business',48,8)->default(0);
            $table->string('status')->default(\App\Models\User::STATUS_ACTIVE)->index();
            $table->foreignId('referred_by')->nullable()->references('id')->on('users')->onDelete('cascade');
            $table->foreignId('user_role_id')->default(\App\Constants\CommonConstants::DEFAULT_USER_ROLE)->references('id')->on('user_roles')->onDelete('cascade');
            $table->text('user_agent')->nullable();
            $table->string('created_ip')->nullable();
            $table->rememberToken();
            $table->timestamps();
        });

        $this->schema->create('password_reset_tokens', function (Blueprint $table) {
            $table->string('email')->primary();
            $table->string('token');
            $table->timestamp('created_at')->nullable();
        });

        $this->schema->create('sessions', function (Blueprint $table) {
            $table->string('id')->primary();
            $table->foreignId('user_id')->nullable()->index();
            $table->string('ip_address', 45)->nullable();
            $table->text('user_agent')->nullable();
            $table->longText('payload');
            $table->integer('last_activity')->index();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('users');
        Schema::dropIfExists('password_reset_tokens');
        Schema::dropIfExists('sessions');
    }
};
