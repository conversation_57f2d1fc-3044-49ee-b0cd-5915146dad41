<?php

namespace App\Models;

use App\Base\Model\BaseModel;
use App\Components\Helper;
use App\Constants\CommonConstants;
use App\Constants\UserConstants;


class UserDbLog extends BaseModel
{
    protected $fillable = [
        'user_id',
        'log_date',
        'log_data',
        'created_at',
        'updated_at',
    ];

    public function user()
    {
        return $this->hasOne(User::class, 'id', 'user_id');
    }

    public static function saveLog($user_id,$logs,$date=null) {
        if(!$date) {
            $date=date(CommonConstants::PHP_DATE_FORMAT_SHORT);
        }

        $model=self::query()->where('user_id','=',$user_id)
            ->where('log_date','=',$date)
            ->limit(1)->first();
        if(!$model) {
            $model=new UserDbLog();
            $model->user_id=$user_id;
            $model->log_date=$date;
            $model->log_data=json_encode($logs);
            if($model->save()) {
                return true;
            }
        } else {
            $model->log_data=json_encode($logs);
            $model->updated_at=date(CommonConstants::PHP_DATE_FORMAT);
            if($model->update(['updated_at','log_data'])) {
                return true;
            }
        }
        return false;
    }

    public static function saveDailyLogs($user_id=null) {
        try {
            if($user_id) {
                $logUsers = User::query()
                    ->where('id', '=', $user_id)
                    ->get();
            } else {
                $logUsers = User::query()
                    ->where('user_role_id', '=', User::ROLE_USER)
                    ->where('status', '=', User::STATUS_ACTIVE)
                    ->orderBy("id")
                    ->get();
            }
            if(count($logUsers)>0) {
                $logColumns=[
                    'username','wallet_address','email','joining_date','rank_id',
                    'total_team','total_directs','active_directs','is_binary_created','opened_levels','is_zero_pin','is_power_leg','active_investment',
                    'lifetime_investment','total_earnings','lifetime_earnings','total_working_earnings','lifetime_working_earnings','total_withdrawals',
                    'team_business','current_month_business','income_x'];
                foreach ($logUsers as $logUser) {
                    $logContent=[];
                    if(count($logColumns)>0) {
                        foreach ($logColumns as $column) {
                            $logContent[$column]=$logUser->$column;
                        }
                    }

                    if($logUser->is_zero_pin) {
                        $totalBusiness=round($logUser->active_investment+$logUser->team_business,CommonConstants::USD_PRECISION);
                    } else {
                        $totalBusiness=round($logUser->lifetime_investment+$logUser->team_business,CommonConstants::USD_PRECISION);
                    }

                    $activeTeamBusiness=$logUser->activeTeamBusiness;

                    $logContent['total_business']=($totalBusiness>0 ? $totalBusiness : 0);
                    $logContent['business_percentage']=($logUser->team_business>0 ? Helper::getPercentage($logUser->team_business, $activeTeamBusiness) : 0);
                    $logContent['investment_percentage']=($logUser->active_investment>0 ? Helper::getPercentage($logUser->lifetime_investment, $logUser->active_investment) : 0);
                    $logContent['remaining_limit']=$logUser->getRemainingAmount($logUser->total_earnings);

                    $logContent['team']=[];
                    $directs = User::query()
                        ->where('referred_by', '=', $logUser->id)
                        ->where('status','=',User::STATUS_ACTIVE)
                        ->where('active_investment','>',0)
                        ->orderBy('team_business')->get();
                    if (count($directs) > 0) {
                        $logContent['team']['directs'] = [];
                        $logContent['team']['directs_business'] = 0;
                        $virtual_power_leg_id = null;
                        $virtual_power_leg_business = null;
                        foreach ($directs as $direct) {
                            if ($direct->active_investment > 0 && !$direct->is_zero_pin) {
                                $logContent['team']['directs'][] = ['id' => $direct->id,'is_power_leg'=>$direct->is_power_leg, 'team_business' => round(($direct->lifetime_investment + $direct->team_business), 2)];
                                $logContent['team']['directs_business']= round(($logContent['team']['directs_business']+$direct->lifetime_investment),CommonConstants::USD_PRECISION);

                                if($direct->is_power_leg) {
                                    $virtual_power_leg_id=$direct->id;
                                    $virtual_power_leg_business=round(($direct->lifetime_investment + $direct->team_business), 2);
                                }
                            }
                            if ($direct->active_investment > 0 && $direct->is_zero_pin) {
                                $logContent['team']['directs'][] = ['id' => $direct->id,'is_power_leg'=>$direct->is_power_leg, 'team_business' => round(($direct->active_investment + $direct->team_business), 2)];

                                if($direct->is_power_leg) {
                                    $virtual_power_leg_id=$direct->id;
                                    $virtual_power_leg_business=round(($direct->active_investment + $direct->team_business), 2);
                                }
                            }
                        }

                        if (count($logContent['team']['directs']) >= 1) {
                            $power_leg_id = null;
                            $power_leg_business = null;
                            $second_power_leg_id = null;
                            $second_power_leg_business = null;


                            foreach ($logContent['team']['directs'] as $logDirect) {
                                if ($power_leg_business === null) {
                                    $power_leg_business = $logDirect['team_business'];
                                    $power_leg_id = $logDirect['id'];
                                } else if ($second_power_leg_business === null) {
                                    $second_power_leg_business = $logDirect['team_business'];
                                    $second_power_leg_id = $logDirect['id'];
                                } else {
                                    $business = $logDirect['team_business'];
                                    $business_id = $logDirect['id'];
                                    if ($business > $power_leg_business) {
                                        $second_power_leg_id=$power_leg_id;
                                        $second_power_leg_business=$power_leg_business;
                                        $power_leg_business = $business;
                                        $power_leg_id = $business_id;
                                    } else if ($business > $second_power_leg_business) {
                                        $second_power_leg_business = $business;
                                        $second_power_leg_id = $business_id;
                                    }
                                }
                            }

                            if($virtual_power_leg_id) {
                                $second_power_leg_id=$power_leg_id;
                                $second_power_leg_business=$power_leg_business;

                                $power_leg_id=$virtual_power_leg_id;
                                $power_leg_business=$virtual_power_leg_business;
                            }


                            $legStats = ['power' => [],'second_power' => [], 'other' => ['team_business' => 0, 'directs' => []]];
                            foreach ($logContent['team']['directs'] as $logDirect) {
                                if ($logDirect['id'] == $power_leg_id) {
                                    $legStats['power'] = $logDirect;
                                    $legStats['power']['username']=User::fetchUsernameByID($power_leg_id);
                                } else if ($logDirect['id'] == $second_power_leg_id) {
                                    $legStats['second_power'] = $logDirect;
                                    $legStats['second_power']['username']=User::fetchUsernameByID($second_power_leg_id);
                                } else {
                                    $legStats['other']['team_business'] = round(($legStats['other']['team_business'] + $logDirect['team_business']), CommonConstants::USD_PRECISION);
                                    $legStats['other']['directs'][] = $logDirect;
                                }
                            }
                            $logContent['team']['calculations'] = $legStats;
                        }
                    }

                    self::saveLog($logUser->id,$logContent);
                }
            }

            return true;
        } catch (\Exception $e) {
            return false;
        }
    }
}
