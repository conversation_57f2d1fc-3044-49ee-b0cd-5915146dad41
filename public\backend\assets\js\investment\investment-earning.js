/*! For license information please see investment-earning.js.LICENSE.txt */
"use strict";document.addEventListener("DOMContentLoaded",(function(){window.randomScalingFactor=function(){return Math.round(20*Math.random())};var a=document.getElementById("earningbyday").getContext("2d"),o=a.createLinearGradient(0,0,0,280);o.addColorStop(0,"rgba(8, 160, 70, 0.85)"),o.addColorStop(1,"rgba(8, 160, 70, 0)");var r=a.createLinearGradient(0,0,0,280);r.addColorStop(0,"rgba(71, 28, 168, 0.5)"),r.addColorStop(1,"rgba(71, 28, 168, 0)");var n={type:"line",data:{labels:["1/9","2/9","3/9","4/9","5/9","6/9","7/9","8/9","9/9","10/9"],datasets:[{label:"# of Votes",data:[randomScalingFactor(),randomScalingFactor(),randomScalingFactor(),randomScalingFactor(),randomScalingFactor(),randomScalingFactor(),randomScalingFactor(),randomScalingFactor(),randomScalingFactor(),randomScalingFactor(),randomScalingFactor(),randomScalingFactor()],radius:0,backgroundColor:o,borderColor:"#08a046",borderWidth:1,fill:!0},{label:"earning of Votes",data:[randomScalingFactor(),randomScalingFactor(),randomScalingFactor(),randomScalingFactor(),randomScalingFactor(),randomScalingFactor(),randomScalingFactor(),randomScalingFactor(),randomScalingFactor(),randomScalingFactor(),randomScalingFactor(),randomScalingFactor()],radius:0,backgroundColor:r,borderColor:"#471ca8",borderWidth:1,fill:!0}]},options:{maintainAspectRatio:!1,barThickness:16,borderRadius:2,plugins:{legend:{display:!1}},scales:{y:{display:!1,beginAtZero:!0},x:{grid:{display:!1},display:!0,beginAtZero:!0}}}},t=(new Chart(a,n),document.getElementById("semidoughnutchart").getContext("2d"));new Chart(t,{type:"doughnut",data:{labels:["Daily Vages","Cancelled Bookings","Oxygen","Manpower","Medical Facilities"],datasets:[{label:"Expense categories",data:[40,35,15,25,20],backgroundColor:["#6faa00","#ffc107","#fd7e14","#5840ef","#becede"],borderWidth:0}]},options:{circumference:180,rotation:-90,responsive:!0,cutout:80,tooltips:{position:"nearest",yAlign:"bottom"},plugins:{legend:{display:!1,position:"top"},title:{display:!1,text:"Chart.js Doughnut Chart"}},layout:{padding:0}}})}));