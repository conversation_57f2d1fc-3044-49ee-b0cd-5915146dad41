
import {Head, <PERSON>} from '@inertiajs/react';
import UserLayout from "@/layouts/user/user-layout";
import {<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>} from "@mui/x-charts";
import BreadcrumbsButtons from "@/components/user/breadcrumbButtons";
import {useUserContext} from "@/context/UserContext";
import {useEffect, useState} from "react";
import UserService from "@/services/UserService";


export default function InvestmentHistory() {
    const {
        httpServiceObject,
        copyButton
    } = useUserContext();

    const [stakes, setStakes] = useState<any>(null);

    const fetchStats = async () => {

        try {
            let stats = await UserService.getStakes(httpServiceObject);
            setStakes(stats);

        } catch (error) {
            console.error("Error fetching stats:", error);
        }
    };
    useEffect(() => {
        fetchStats();
    }, [])

    return (
        <UserLayout>

            <main className="adminuiux-content has-sidebar">

                <div className="container mt-4">
                    <div className="row gx-3 align-items-center">
                        <div className="col-12 col-sm">
                            <nav aria-label="breadcrumb" className="mb-2">
                                <ol className="breadcrumb mb-0">
                                    <li className="breadcrumb-item bi"><Link href={route('user.dashboard')}><i
                                        className="bi bi-house-door me-1 fs-14"></i> Dashboard</Link></li>
                                    <li className="breadcrumb-item active bi" aria-current="page">Investment History</li>
                                </ol>
                            </nav>
                            <h5>Investment History</h5>
                        </div>
                        <div className="col-12 col-sm-auto text-end py-3 py-sm-0">
                            <BreadcrumbsButtons/>
                        </div>
                    </div>
                </div>

                <div className="container mt-4" id="main-content" data-bs-spy="scroll" data-bs-target="#list-example"
                     data-bs-smooth-scroll="true">

                    <div className="row" id="list-item-2">
                        {(stakes && stakes.length > 0 ) ? (
                            stakes &&
                            Object.entries(stakes)
                            .map(([key, item]) => (
                                <div className="col-12 mb-2" key={key}>
                                    <div className="card adminuiux-card mb-3">
                                        <div className="card-body">
                                            <div className="row align-items-center">
                                                <div className="col-12 col-sm-9 col-xxl mb-3 mb-xxl-0">
                                                    <div className="row align-items-center">
                                                        <div className="col">
                                                            <h5>Ref. #{item?.ref_no}</h5>
                                                            <span className="badge badge-light text-bg-theme-1">Start Date: <strong>{item?.start_date}</strong>
                                                                {(item?.end_date!="") && <>End Date <strong>{item?.end_date}</strong></>}
                                                            </span>
                                                            {(item?.short_hash!="") && <span
                                                                className="badge badge-light text-bg-theme-2 mx-1">Hash: {item?.short_hash} <i onClick={()=>copyButton(item?.hash)} className="bi bi-copy cursor-pointer"></i></span>}
                                                            <span className="badge badge-light text-bg-theme-2 mx-1">ROI End Date: <strong>{item?.roi_end_date}</strong></span>
                                                            {
                                                                (item?.is_active==1) && <span className="badge badge-light text-bg-success mx-1">Active</span>
                                                            }

                                                        </div>
                                                    </div>
                                                </div>
                                                <div className="col-12 col-sm-3 col-xxl-auto mb-3 mb-sm-0">
                                                    <h5>{item?.tokens} Tokens</h5>
                                                    <p className="text-secondary small">You get</p>
                                                </div>
                                                <div className="col-12 col-md-9 col-xxl-4 mb-3 mb-md-0">
                                                    <div className="card">
                                                        <div className="card-body">
                                                            <div className="row align-items-center justify-content-between">
                                                                <div className="col-auto text-start">
                                                                    <h5 className="mb-1">{item?.monthly_dividend}

                                                                    </h5>
                                                                    <p className="text-secondary small">Monthly Dividend</p>
                                                                </div>
                                                                <div className="col-auto">
                                                                    <h5 className="mb-1">{item?.formattedEarnings}

                                                                    </h5>
                                                                    <p className="text-secondary small">Earned</p>
                                                                </div>
                                                                <div className="col-auto text-end">
                                                                    <h5>{item?.formattedAmount}</h5>
                                                                    <p className="text-secondary small">Amount</p>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>

                                            </div>
                                        </div>
                                    </div>
                                </div>
                            ))



                        ) : (
                            <>
                                <div className="col-12 mb-2">
                                    <div className="card adminuiux-card mb-3">
                                        <div className="card-body">
                                            <div className="row align-items-center">
                                                <div className="col-12 col-sm-9 col-xxl mb-3 mb-xxl-0">
                                                    <div className="row align-items-center">
                                                        <div className="col">
                                                            <h5 className={'text-center pt-5 pb-5'}>You don't have any investment yet.</h5>

                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </>
                        )}


                    </div>

                </div>



            </main>


        </UserLayout>
    );
}
