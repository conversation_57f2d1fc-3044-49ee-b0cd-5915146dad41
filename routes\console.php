<?php

use Illuminate\Foundation\Inspiring;
use Illuminate\Support\Facades\Artisan;

Artisan::command('once-per-minute', function () {
    $startTime=time();
    $endTime=strtotime("+1 minute",$startTime)-5;

    //New Account Generate
    \App\Models\User::generateBinaryTree($endTime);

    //Fetch new transactions
    \App\Models\BlockchainTransaction::processTransactions($endTime);

    //Process new investments
    \App\Models\UserStake::processNewInvestments($endTime);

    if($endTime>time()) {
        //New Account Generate
        \App\Models\User::generateBinaryTree($endTime,true);
    }
    die("completed in ".(time()-$startTime));
})->purpose('Runs after every minute for at least 55 seconds');

Artisan::command('once-per-two-minutes', function () {
    $startTime=time();
    $endTime=strtotime("+2 minute",$startTime)-5;

    //Process pending withdrawal requests recursively
    \App\Models\UserWithdrawal::processTransactions($endTime);

    die("completed in ".(time()-$startTime));
})->purpose('Runs after every two minutes');

Artisan::command('once-per-five-minutes', function () {
    $startTime=time();
    $endTime=strtotime("+5 minute",$startTime)-5;

    //Process pending schedule transaction requests
    \App\Models\ScheduleStaking::processTransactions($endTime);

    die("completed in ".(time()-$startTime));
})->purpose('Runs after every five minutes');

Artisan::command('once-per-day', function () {
    //once a day
    //Mint daily token
    \App\Models\DailyMintLog::mintToken();

    //Save current block number
    \App\Models\DailyBlockRecord::updateRecord();

    //run it after 11 PM to send today ROI
    \App\Models\RoiLog::sendRoi();

    //send royalty
    \App\Models\UserRoyaltyHistory::processRoyalty();

    die("success");
})->purpose('Daily closing');

Artisan::command('send-roi-of-roi', function () {
    //run it after 3 min
    $maxTime=strtotime("+3 minutes")-10;
    \App\Models\Transaction::sendRoiOfRoi($maxTime);
    die("success");
})->purpose('Send dividend roi-of-roi');

Artisan::command('earning-wallet-closing', function () {
    //once per week (Monday)
    //run it after 2 AM to calculate and shift earning wallet into withdrawal wallet
    \App\Models\ShiftBalanceLog::startEarningProcess();
})->purpose('Earning wallet closing');

Artisan::command('dividend-wallet-closing', function () {
    //once per month (Every 1st)
    //run it after 2 AM to calculate and shift earning wallet into withdrawal wallet
    \App\Models\ShiftBalanceLog::startDividendProcess();
})->purpose('Dividend wallet closing');

Artisan::command('store-user-daily-logs', function () {
    //once per hour
    //Save UserDbLogs
    \App\Models\UserDbLog::saveDailyLogs();
    die("completed");
})->purpose('Store daily user logs');

Artisan::command('testing', function () {
    //\App\Models\UserRoyaltyHistory::processRoyalty();die("Completed");
    $startTime=time();
    $endTime=strtotime("+1 minute",$startTime)-5;
    \App\Models\DailyBlockRecord::updateRecord();
    die("Completed");
    $username='DXE514603';
    $username='fateh0103';
    $user=\App\Models\User::findByUsername($username);
    if($user) {
        $lastChildrenOfSponsor = $user->getLastChildrenOfFirstChildren();
        var_dump($lastChildrenOfSponsor);
    } else {
        print "No user details found - ".$username.PHP_EOL;
    }
    die("completed in ".(time()-$startTime));
})->purpose('Code testing command');
