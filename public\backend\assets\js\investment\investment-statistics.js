/*! For license information please see investment-statistics.js.LICENSE.txt */
"use strict";function lastvisibletd(){$(".table tbody tr td").removeClass("lastvisible"),$(".table tbody tr").each((function(){$(this).find("td:visible:last").addClass("lastvisible")}))}document.addEventListener("DOMContentLoaded",(function(){window.randomScalingFactor=function(){return Math.round(20*Math.random())};var a=document.getElementById("areachartblue1").getContext("2d"),o=a.createLinearGradient(0,0,0,65);o.addColorStop(0,"rgba(0, 73, 232, 0.95)"),o.addColorStop(1,"rgba(0, 73, 232, 0)");var t={type:"line",data:{labels:["1","2","3","4","5","7","8"],datasets:[{label:"# of Votes",data:[randomScalingFactor(),randomScalingFactor(),randomScalingFactor(),randomScalingFactor(),randomScalingFactor(),randomScalingFactor(),randomScalingFactor(),randomScalingFactor()],radius:0,backgroundColor:o,borderColor:"#015EC2",borderWidth:0,borderRadius:4,fill:!0,tension:.5}]},options:{maintainAspectRatio:!1,plugins:{legend:{display:!1},tooltip:{enabled:!1}},scales:{y:{display:!1,beginAtZero:!0},x:{display:!1}}}},r=(new Chart(a,t),document.getElementById("areachartred1").getContext("2d")),e=r.createLinearGradient(0,0,0,65);e.addColorStop(0,"rgba(200, 0, 54, 0.95)"),e.addColorStop(1,"rgba(200, 0, 54, 0)");var n={type:"line",data:{labels:["1","2","3","4","5","7","8"],datasets:[{label:"# of Votes",data:[randomScalingFactor(),randomScalingFactor(),randomScalingFactor(),randomScalingFactor(),randomScalingFactor(),randomScalingFactor(),randomScalingFactor(),randomScalingFactor()],radius:0,backgroundColor:e,borderColor:"#f03d4f",borderWidth:0,borderRadius:4,fill:!0,tension:.5}]},options:{maintainAspectRatio:!1,plugins:{legend:{display:!1},tooltip:{enabled:!1}},scales:{y:{display:!1,beginAtZero:!0},x:{display:!1}}}},d=(new Chart(r,n),document.getElementById("areachartgreen1").getContext("2d")),l=d.createLinearGradient(0,0,0,65);l.addColorStop(0,"rgba(8, 160, 70, 0.95)"),l.addColorStop(1,"rgba(8, 160, 70, 0)");var i={type:"line",data:{labels:["1","2","3","4","5","7","8"],datasets:[{label:"# of Votes",data:[randomScalingFactor(),randomScalingFactor(),randomScalingFactor(),randomScalingFactor(),randomScalingFactor(),randomScalingFactor(),randomScalingFactor(),randomScalingFactor()],radius:0,backgroundColor:l,borderColor:"#91C300",borderWidth:0,borderRadius:4,fill:!0,tension:.5}]},options:{maintainAspectRatio:!1,plugins:{legend:{display:!1},tooltip:{enabled:!1}},scales:{y:{display:!1,beginAtZero:!0},x:{display:!1}}}},c=(new Chart(d,i),document.getElementById("areachartyellow1").getContext("2d")),s=c.createLinearGradient(0,0,0,65);s.addColorStop(0,"rgba(252, 122, 30, 0.95)"),s.addColorStop(1,"rgba(252, 122, 30, 0)");var g={type:"line",data:{labels:["1","2","3","4","5","7","8"],datasets:[{label:"# of Votes",data:[randomScalingFactor(),randomScalingFactor(),randomScalingFactor(),randomScalingFactor(),randomScalingFactor(),randomScalingFactor(),randomScalingFactor(),randomScalingFactor()],radius:0,backgroundColor:s,borderColor:"#fdba00",borderWidth:0,borderRadius:4,fill:!0,tension:.5}]},options:{maintainAspectRatio:!1,plugins:{legend:{display:!1},tooltip:{enabled:!1}},scales:{y:{display:!1,beginAtZero:!0},x:{display:!1}}}},b=(new Chart(c,g),document.getElementById("summarychart").getContext("2d")),m=b.createLinearGradient(0,0,0,200);m.addColorStop(0,"rgba(0, 73, 232, 0.95)"),m.addColorStop(1,"rgba(0, 73, 232, 0.25)");var p=b.createLinearGradient(0,0,0,200);p.addColorStop(0,"rgba(0, 73, 232, 0.5)"),p.addColorStop(1,"rgba(0, 73, 232, 0)");var S={type:"bar",data:{labels:["1/9","2/9","3/9","4/9","5/9","6/9","7/9","8/9","9/9","10/9"],datasets:[{label:"# of Votes",data:[randomScalingFactor(),randomScalingFactor(),randomScalingFactor(),randomScalingFactor(),randomScalingFactor(),randomScalingFactor(),randomScalingFactor(),randomScalingFactor(),randomScalingFactor(),randomScalingFactor(),randomScalingFactor(),randomScalingFactor()],radius:0,backgroundColor:m,borderColor:"#0049e8",borderWidth:1,fill:!0},{label:"earning of Votes",data:[randomScalingFactor(),randomScalingFactor(),randomScalingFactor(),randomScalingFactor(),randomScalingFactor(),randomScalingFactor(),randomScalingFactor(),randomScalingFactor(),randomScalingFactor(),randomScalingFactor(),randomScalingFactor(),randomScalingFactor()],radius:0,backgroundColor:p,borderColor:"rgba(0, 73, 232, 0.5)",borderWidth:1,fill:!0}]},options:{maintainAspectRatio:!1,barThickness:16,borderRadius:2,plugins:{legend:{display:!1}},scales:{y:{display:!1,beginAtZero:!0},x:{grid:{display:!1},display:!0,beginAtZero:!0}}}},u=(new Chart(b,S),document.getElementById("doughnutchart").getContext("2d")),F=(new Chart(u,{type:"polarArea",data:{labels:["0-15","16-30","31-45","46-60","60+"],datasets:[{label:"Age Range",data:[40,10,15,25,10],backgroundColor:["#6faa00","#ffc107","#fd7e14","#0049e8","#becede"],borderWidth:0}]},options:{responsive:!0,tooltips:{position:"nearest",yAlign:"bottom"},plugins:{legend:{display:!1,position:"top"},title:{display:!1,text:"Chart.js polarArea Chart"}}}}),document.getElementById("semidoughnutchart").getContext("2d"));new Chart(F,{type:"doughnut",data:{labels:["Daily Vages","Cancelled Bookings","Oxygen","Manpower","Medical Facilities"],datasets:[{label:"Expense categories",data:[40,35,15,25,20],backgroundColor:["#6faa00","#ffc107","#fd7e14","#0049e8","#becede"],borderWidth:0}]},options:{circumference:180,rotation:-90,responsive:!0,cutout:80,tooltips:{position:"nearest",yAlign:"bottom"},plugins:{legend:{display:!1,position:"top"},title:{display:!1,text:"Chart.js Doughnut Chart"}},layout:{padding:0}}});new Swiper(".swipernonav",{slidesPerView:"auto",spaceBetween:16,autoplay:{delay:2e3,disableOnInteraction:!1}});lastvisibletd()})),window.addEventListener("resize",(function(a){window.dataTables=dataTables,$("#dataTable").DataTable().columns.adjust().draw(),lastvisibletd()}));