<?php

namespace App\Models;

use App\Base\Model\BaseModel;
use App\Components\Helper;
use App\Components\Web3NodeBackend;
use App\Constants\CommonConstants;
use Illuminate\Database\Eloquent\Model;

class UserWithdrawal extends BaseModel
{
    protected $fillable = [
        'ref_no','amount','user_id',
        'hash','payment_hash','wallet_id',
        'token_amount','token_price','is_paid',
        'raw_data','comments',
        'created_at','updated_at',
    ];

    public function user()
    {
        return $this->hasOne(User::class, 'id', 'user_id');
    }

    public function wallet()
    {
        return $this->hasOne(Wallet::class, 'id', 'wallet_id');
    }

    public function apiData() {
        return [
            'ref_no'=>$this->ref_no,
            'amount'=>$this->amount,
            'token_amount'=>$this->token_amount,
            'token_price'=>$this->token_price,
            'formatted_amount'=>Helper::printAmount($this->amount),
            'formatted_token_amount'=>Helper::printNumber($this->token_amount,CommonConstants::TOKEN_PRECISION),
            'formatted_token_price'=>Helper::printAmount($this->token_price),
            'hash'=>($this->hash ? $this->hash : ''),
            'payment_hash'=>($this->payment_hash ? $this->payment_hash : ''),
            'short_hash'=>($this->hash ? User::shortAddress($this->hash) : ''),
            'short_payment_hash'=>($this->payment_hash ? User::shortAddress($this->payment_hash) : ''),
            'comments'=>($this->comments ? $this->comments : ''),
            'is_paid'=>$this->is_paid,
            'created_at'=>$this->created_at,
            'updated_at'=>$this->updated_at,
        ];
    }

    public static function saveRequest($user_id,$hash,$amount,$rawData=null,$comments=null,$tokenPrice=null) {
        try {
            $hash=null;
            if (is_array($rawData)) {
                if(array_key_exists('hash',$rawData)) {
                    $hash=$rawData['hash'];
                }
                $rawData = json_encode($rawData);
            }

            $userDetails=User::query()->where('id','=',$user_id)->first();
            if(!$userDetails) {
                throw new \Exception('Invalid user');
            }

            if(!$tokenPrice) {
                $tokenPrice=Web3NodeBackend::getTokenUSDPrice();
                if($tokenPrice===false) {
                    throw new \Exception('Token price not fetched');
                }
            }

            $tokenPrice=round($tokenPrice,CommonConstants::USD_PRECISION);
            $check=self::query()->where(['hash'=>$hash])->limit(1)->first();
            if($check) {
                Log::insertLog([
                    'user_id' => $user_id,
                    'particulars' => 'Duplicate withdraw request prevented',
                    'type' => 'duplicate_withdrawal_request',
                    'data' => json_encode([
                        'user_id' => $user_id,
                        'amount' => $amount,
                        'hash' => $hash,
                    ])
                ]);
                return false;
            }

            $userWallet=$userDetails->getUserWallet(Wallet::WALLET_WITHDRAWAL);
            if($amount>$userWallet->balance) {
                throw new \Exception('Insufficient wallet balance.');
            }

            $model=new UserWithdrawal();
            $model->user_id=$user_id;
            $model->amount=$amount;
            $model->wallet_id=Wallet::WALLET_WITHDRAWAL;

            if($comments) {
                $model->comments=$comments;
            }
            if($rawData) {
                $model->raw_data=$rawData;
            }
            $model->hash=$hash;

            if($tokenPrice) {
                $model->token_price=round($tokenPrice,CommonConstants::USD_PRECISION);
                $model->token_amount=round(($amount/$tokenPrice),CommonConstants::TOKEN_PRECISION);
            }
            if($model->save()) {
                $result = $userDetails->addTransaction(
                    'Withdrawal request #' . $model->ref_no,
                    TransactionType::TRANSACTION_TYPE_WITHDRAWAL,
                    CommonConstants::DEBIT,
                    $amount,
                    $model->wallet_id,
                    json_encode(['user_withdrawal_id' => $model->id]),
                    $model->id
                );
                if($result) {
                    return $model;
                } else {
                    throw new \Exception('Unable to deduct withdrawal funds');
                }
            } else {
                throw new \Exception('Unable to add new withdrawal request');
            }

        } catch (\Exception $e) {
            Log::insertLog([
                'user_id' => CommonConstants::ADMINISTRATIVE,
                'particulars' => 'New withdrawal request error',
                'type' => 'withdrawal_request_error',
                'data' => json_encode([
                    'user_id' => $user_id,
                    'hash' => $hash,
                    'amount' => $amount,
                    'comments' => $comments,
                    'tokenPrice' => $tokenPrice,
                    'rawData' => $rawData,
                    'error' => $e->getMessage(),
                    'trace' => $e->getTraceAsString(),
                ])
            ]);
        }
        return false;
    }

    public static function processTransactions($endTime=null) {
        if($endTime==null) {
            $endTime = strtotime("+2 minute") - 10;
        }
        if(time()>=$endTime) {
            return true;
        }

        $transactions=self::query()
            ->where('is_paid','=',CommonConstants::NO)
            ->orderBy("id")
            ->limit(50)->get();
        if(count($transactions)>0) {
            $tokenPrice = Web3NodeBackend::getTokenUSDPrice();
            if (!$tokenPrice || $tokenPrice === false) {
                $tokenPrice = null;
            }

            if(!$tokenPrice) {
                return false;
            }

            foreach ($transactions as $transaction) {
                if(time()>=$endTime) {
                    return true;
                }
                print "Start processing withdrawal transaction #".$transaction->id.PHP_EOL;

                $transaction->processWithdrawal();
                sleep(rand(3,5));
            }
        }
        sleep(rand(3,5));
        return self::processTransactions($endTime);
    }

    public function processWithdrawal() {
        try {
            if(!$this->is_paid) {
                $this->is_paid=CommonConstants::YES;
                $this->updated_at=date(CommonConstants::PHP_DATE_FORMAT);
                if($this->update(['updated_at','is_processed'])) {
                    $hash=Web3NodeBackend::sendToken($this->user->wallet_address,Helper::toWei($this->token_amount));
                    if($hash) {
                        $this->payment_hash=$hash;
                        $this->updated_at=date(CommonConstants::PHP_DATE_FORMAT);
                        if($this->update(['updated_at','payment_hash'])) {
                            return true;
                        }
                    } else {
                        throw new \Exception('Send token not added. Result is '.json_encode($hash));
                    }
                } else {
                    throw new \Exception('Unable to update record');
                }
            } else {
                throw new \Exception('Already processed');
            }
        } catch (\Exception $e) {
            Log::insertLog([
                'user_id'=>CommonConstants::ADMINISTRATIVE,
                'particulars' => 'Unable to send withdrawal',
                'type' => 'send_withdrawal_error',
                'data' => json_encode([
                    'id'=>$this->id,
                    'message'=>$e->getMessage(),
                    'message_trace'=>$e->getTraceAsString(),
                ])
            ]);
        }
        return true;
    }
}
