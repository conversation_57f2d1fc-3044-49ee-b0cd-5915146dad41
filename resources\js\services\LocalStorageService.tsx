export default class LocalStorageService {
    public setItem(key,val) {
        try {
            if(localStorage.setItem(key,val)) {
                return localStorage.getItem(key);
            }
        } catch (error) {}
        return null;
    }
    public hasItem(key) {
        try {
            return localStorage.getItem(key);;
        } catch (error) {}
        return null;
    }
    public getItem(key) {
        try {
            return localStorage.getItem(key);;
        } catch (error) {}
        return null;
    }
}
