<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends \App\Base\Database\MigrationBase
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        $this->schema->create('user_rank_histories', function (\App\Base\Database\BlueprintBase $table) {
            $table->id();
            $table->bigInteger('ref_no')->unique();
            $table->bigInteger('user_id')->nullable()->index();
            $table->bigInteger('rank_id')->nullable()->index();
            $table->integer('is_admin')->default(0)->index();
            $table->timestamps();

            $table->unique(['user_id','rank_id'],'user_rank');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('user_rank_histories');
    }
};
