<?php

namespace App\Models;

use App\Base\Model\BaseModel;
use App\Constants\CommonConstants;
use Illuminate\Support\Facades\DB;

class UserStakeAdminLog extends BaseModel
{
    protected $fillable = [
        'user_id',
        'user_stake_id',
        'ref_no',
        'comments',
        'amount',
        'created_at',
    ];

    public function user()
    {
        return $this->hasOne(User::class, 'id', 'user_id');
    }
    public function onCreated()
    {
        try {
            $result = $this->user->addTransaction(
                'Funds added by admin #' . $this->ref_no,
                TransactionType::TRANSACTION_TYPE_ADMIN_ADJUSTMENT,
                CommonConstants::CREDIT,
                $this->amount,
                Wallet::WALLET_USD,
                json_encode(['user_stake_admin_log_id' => $this->id]),
                $this->id
            );
            if($result) {
                $stake = UserStake::addNewStake($this->user, $this->amount, $this->ref_no, true);
                if ($stake) {
                    DB::statement("update `user_stake_admin_logs` set `user_stake_id`='" . $stake->id . "' where `id`=" . $this->id);
                } else {
                    throw new \Exception('Something went wrong while adding this stake');
                }
            } else {
                throw new \Exception('Unable to add USD wallet balance');
            }
        } catch (\Exception $e) {
            Log::insertLog([
                'user_id' => CommonConstants::ADMINISTRATIVE,
                'particulars' => 'Unable to create admin stake #'.$this->id,
                'type' => 'admin_stake_addition_issue',
                'data' => json_encode([
                    'id' => $this->id,
                    'user_id' => $this->user_id,
                    'message' => $e->getMessage(),
                    'trace' => $e->getTraceAsString(),
                ])
            ]);
            return false;
        }
        parent::onCreated();
        return true;
    }
}
