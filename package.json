{"private": true, "type": "module", "scripts": {"build": "vite build", "build:ssr": "vite build && vite build --ssr", "dev": "vite", "format": "prettier --write resources/", "format:check": "prettier --check resources/", "lint": "eslint . --fix", "types": "tsc --noEmit"}, "devDependencies": {"@eslint/js": "^9.19.0", "@types/node": "^22.13.5", "eslint": "^9.17.0", "eslint-config-prettier": "^10.0.1", "eslint-plugin-react": "^7.37.3", "eslint-plugin-react-hooks": "^5.1.0", "prettier": "^3.4.2", "prettier-plugin-organize-imports": "^4.1.0", "prettier-plugin-tailwindcss": "^0.6.11", "typescript-eslint": "^8.23.0"}, "dependencies": {"@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@headlessui/react": "^2.2.0", "@inertiajs/react": "^2.0.0", "@mui/lab": "^7.0.0-beta.13", "@mui/material": "^7.1.1", "@mui/x-charts": "^8.5.0", "@mui/x-date-pickers": "^8.5.2", "@radix-ui/react-avatar": "^1.1.3", "@radix-ui/react-checkbox": "^1.1.4", "@radix-ui/react-collapsible": "^1.1.3", "@radix-ui/react-dialog": "^1.1.6", "@radix-ui/react-dropdown-menu": "^2.1.6", "@radix-ui/react-label": "^2.1.2", "@radix-ui/react-navigation-menu": "^1.2.5", "@radix-ui/react-select": "^2.1.6", "@radix-ui/react-separator": "^1.1.2", "@radix-ui/react-slot": "^1.1.2", "@radix-ui/react-toggle": "^1.1.2", "@radix-ui/react-toggle-group": "^1.1.2", "@radix-ui/react-tooltip": "^1.1.8", "@reown/appkit": "^1.7.8", "@reown/appkit-adapter-wagmi": "^1.7.8", "@tailwindcss/vite": "^4.0.6", "@tanstack/react-query": "^5.80.2", "@types/d3-flextree": "^2.1.4", "@types/react": "^19.0.3", "@types/react-dom": "^19.0.2", "@vitejs/plugin-react": "^4.3.4", "antd": "^5.26.0", "basicprimitives": "^6.6.0", "basicprimitivesreact": "^6.6.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "concurrently": "^9.0.1", "d3-flextree": "^2.1.2", "d3-org-chart": "^3.1.1", "date-fns": "^4.1.0", "dayjs": "^1.11.13", "ethers": "^6.14.3", "globals": "^15.14.0", "laravel-vite-plugin": "^1.0", "lucide-react": "^0.475.0", "react": "^19.0.0", "react-date-range": "^2.0.1", "react-dom": "^19.0.0", "react-toastify": "^11.0.5", "swiper": "^11.2.8", "tailwind-merge": "^3.0.1", "tailwindcss": "^4.0.0", "tailwindcss-animate": "^1.0.7", "typescript": "^5.7.2", "viem": "^2.30.6", "vite": "^6.0", "wagmi": "^2.15.5", "web3": "^4.16.0", "zod": "^3.25.51"}, "optionalDependencies": {"@rollup/rollup-linux-x64-gnu": "4.9.5", "@tailwindcss/oxide-linux-x64-gnu": "^4.0.1", "lightningcss-linux-x64-gnu": "^1.29.1"}}