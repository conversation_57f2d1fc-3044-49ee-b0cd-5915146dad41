import { Core } from '@walletconnect/core'
import { WalletKit } from '@reown/walletkit'
import * as React from "react";
import {useEffect, useState} from "react";


const WalletKitProvider = () => {
    const [walletKit, setWalletKit] = useState(null)

    useEffect(() => {
        const initWalletKit = async () => {
            const core = new Core({
                projectId: 'c8b08d6f499a19b4c24f4230164cce0c'
            })

            const metadata = {
                name: 'Drixe',
                description: 'AppKit Example',
                url: 'https://reown.com/appkit',
                icons: ['https://assets.reown.com/reown-profile-pic.png']
            }

            const instance = await WalletKit.init({
                core,
                metadata
            })

            setWalletKit(instance)
        }

        initWalletKit()
    }, [])

    if (!walletKit) {
        return <div>Loading WalletKit...</div>
    }

    const handleLogin = function() {
        if(walletKit) {
            console.log("Connect Button Called");
        }
        return false;
    }
    return (
        <div className="collapse navbar-collapse justify-content-end" id="desktop-login-regs-btns">
            <div className="d-flex header-btns">
                {/*<button className="btn login-btn" data-bs-toggle="modal" data-bs-target="#exampleModal">Connect Wallet</button>*/}
                <button className="btn login-btn" onClick={handleLogin}>Connect Wallet</button>
            </div>
        </div>
    )
}

export default WalletKitProvider
