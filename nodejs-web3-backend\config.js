//don't store any confidential details in this file
module.exports = {
    NETWORKS: {
        bsc: {
            testnet: {
                config: {
                    networkSlug: 'bsc',
                    chainName: 'Binance Testnet',
                    rpcUrlHttp: 'https://data-seed-prebsc-1-s1.binance.org:8545',
                    chainId: 97,
                    token: 'BNB',
                    tokenName: 'Binance Coin',
                    blockExplorer: 'https://testnet.bscscan.com',
                },
                contracts: {
                    stakeContract: {
                        contractAddress: '0x633f6aB33DFA26349aE74a57bC4b91083Fe88b21',
                    }
                }
            },
            mainnet: {
                config: {
                    networkSlug: 'bsc',
                    chainName: 'Binance Smart Chain',
                    rpcUrlHttp: 'https://bsc-dataseed1.binance.org',
                    chainId: 56,
                    token:'BNB',
                    tokenName: 'Binance Coin',
                    blockExplorer: 'https://bscscan.com',
                },
                contracts: {
                    stakeContract: {
                        contractAddress: '0xxxxx',
                    }
                }
            }
        }
    }
}
