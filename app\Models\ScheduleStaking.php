<?php

namespace App\Models;

use App\Base\Model\BaseModel;
use App\Components\Helper;
use App\Components\Web3NodeBackend;
use App\Constants\CommonConstants;
use function Pest\Mutate\result;

class ScheduleStaking extends BaseModel
{
    protected $fillable = [
        'ref_no',
        'user_stake_id',
        'user_id',
        'request_hash',
        'stake_hash',
        'amount',
        'is_processed',
        'created_at',
        'updated_at',
    ];

    public function user()
    {
        return $this->hasOne(User::class, 'id', 'user_id');
    }

    public function userStake()
    {
        return $this->hasOne(UserStake::class, 'id', 'user_stake_id');
    }

    public static function processTransactions($endTime=null) {
        if($endTime==null) {
            $endTime = strtotime("+2 minute") - 10;
        }
        if(time()>=$endTime) {
            return true;
        }

        $transactions=self::query()
            ->where('is_processed','=',CommonConstants::NO)
            ->orderBy("id")
            ->limit(50)->get();
        if(count($transactions)>0) {
            $tokenPrice = Web3NodeBackend::getTokenUSDPrice();
            if (!$tokenPrice || $tokenPrice === false) {
                $tokenPrice = null;
            }

            $tokenLeft = Web3NodeBackend::getTodayTokensLeft();
            if (!$tokenLeft || $tokenLeft === false) {
                $tokenLeft = null;
            }

            if(!$tokenPrice || !$tokenLeft) {
                return false;
            }

            foreach ($transactions as $transaction) {
                if(time()>=$endTime) {
                    return true;
                }

                $estimatedTokens=round(($transaction->amount/$tokenPrice),CommonConstants::TOKEN_PRECISION);
                if($tokenLeft>=$estimatedTokens) {
                    print "Start processing schedule transaction #".$transaction->id.PHP_EOL;
                    print "Estimated tokens needed ".$estimatedTokens." for $".$transaction->amount.PHP_EOL;

                    $transaction->processStaking();
                    sleep(rand(3,5));

                    $tokenPrice = Web3NodeBackend::getTokenUSDPrice();
                    if (!$tokenPrice || $tokenPrice === false) {
                        $tokenPrice = null;
                    }

                    $tokenLeft = Web3NodeBackend::getTodayTokensLeft();
                    if (!$tokenLeft || $tokenLeft === false) {
                        $tokenLeft = null;
                    }
                }
            }
        }
        return true;
    }

    public function processStaking() {
        try {
            if(!$this->is_processed) {
                $this->is_processed=CommonConstants::YES;
                $this->updated_at=date(CommonConstants::PHP_DATE_FORMAT);
                if($this->update(['updated_at','is_processed'])) {
                    $tokenPrice = Web3NodeBackend::getTokenUSDPrice();
                    if (!$tokenPrice || $tokenPrice === false) {
                        $tokenPrice = null;
                    }
                    if($tokenPrice) {
                        $tokenAmount=round(($this->amount/$tokenPrice),CommonConstants::TOKEN_PRECISION);
                        $hash=Web3NodeBackend::registerStake($this->user->wallet_address,Helper::toWei($this->amount),Helper::toWei($tokenAmount));
                        if($hash) {
                            $this->stake_hash=$hash;
                            $this->updated_at=date(CommonConstants::PHP_DATE_FORMAT);
                            if($this->update(['updated_at','stake_hash'])) {

                                UserStake::addNewStake($this->user,$this->amount,'SS-'.$this->id,false,false,null,$tokenPrice,$hash);
                                return true;
                            }
                        } else {
                            throw new \Exception('Register stake not added. Result is '.json_encode($hash));
                        }
                    } else {
                        throw new \Exception('Unable to fetch token price');
                    }
                } else {
                    throw new \Exception('Unable to update record');
                }
            } else {
                throw new \Exception('Already processed');
            }
        } catch (\Exception $e) {
            Log::insertLog([
                'user_id'=>CommonConstants::ADMINISTRATIVE,
                'particulars' => 'Unable to register staking',
                'type' => 'register_staking_error',
                'data' => json_encode([
                    'id'=>$this->id,
                    'message'=>$e->getMessage(),
                    'message_trace'=>$e->getTraceAsString(),
                ])
            ]);
        }
        return true;
    }

    public static function saveRecord($user_id,$amount,$hash) {
        $model=self::query()
            ->where('request_hash','=',$hash)
            ->first();
        if($model) {
            return $model;
        } else {
            $model=new ScheduleStaking();
            $model->user_id=$user_id;
            $model->amount=$amount;
            $model->request_hash=$hash;
            if($model->save()) {
                return self::saveRecord($user_id,$amount,$hash);
            }
        }
        return false;
    }
}
