<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends \App\Base\Database\MigrationBase
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        $this->schema->create('user_db_logs', function (\App\Base\Database\BlueprintBase $table) {
            $table->id();
            $table->bigInteger('user_id')->index();
            $table->date('log_date')->index();
            $table->longText('log_data')->nullable();
            $table->timestamps();

            $table->unique(['user_id','log_date']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('user_db_logs');
    }
};
