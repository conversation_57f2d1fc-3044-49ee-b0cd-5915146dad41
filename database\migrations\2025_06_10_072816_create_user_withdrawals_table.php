<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends \App\Base\Database\MigrationBase
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        $this->schema->create('user_withdrawals', function (\App\Base\Database\BlueprintBase $table) {
            $table->id();
            $table->bigInteger('ref_no')->unique();
            $table->bigInteger('user_id')->nullable()->index();
            $table->bigInteger('wallet_id')->nullable()->index();
            $table->string('hash')->unique();
            $table->string('payment_hash')->nullable()->unique();
            $table->decimal('amount',48,8)->index();
            $table->decimal('token_amount',48,8)->index();
            $table->decimal('token_price',48,8)->comment('in USD')->index();
            $table->integer('is_paid')->default(\App\Constants\CommonConstants::NO)->index();
            $table->text('comments')->nullable();
            $table->text('raw_data')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('user_withdrawals');
    }
};
