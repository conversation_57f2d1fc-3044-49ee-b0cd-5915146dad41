<?php

namespace App\Models;

use App\Base\Model\BaseModel;

class TransactionType extends BaseModel
{
    const TRANSACTION_TYPE_DEPOSIT = 1;
    const TRANSACTION_TYPE_WITHDRAWAL = 2;
    const TRANSACTION_TYPE_STAKE = 3;
    const TRANSACTION_TYPE_DIVIDEND = 4;
    const TRANSACTION_TYPE_DIVIDEND_COMMISSION = 5;
    const TRANSACTION_TYPE_ADMIN_ADJUSTMENT = 6;
    const TRANSACTION_TYPE_EARNING_CLOSING = 7;
    const TRANSACTION_TYPE_TRANSFER = 8;
    const TRANSACTION_TYPE_RANK_REWARD = 9;
    const TRANSACTION_TYPE_ROYALTY = 10;

    protected $fillable = [
        'name',
    ];
}
