<?php

use Illuminate\Support\Facades\Route;
use Inertia\Inertia;

/*Route::get('/', function () {
    return Inertia::render('welcome');
})->name('home');*/
Route::get('/', [App\Http\Controllers\SiteController::class, 'index'])->name('home');

Route::get('/user-dashboard', [App\Http\Controllers\AppController::class, 'dashboard'])->name('user.dashboard');
Route::get('/invest-now', [App\Http\Controllers\AppController::class, 'investNow'])->name('user.investNow');
Route::get('/trading/deposit', [App\Http\Controllers\AppController::class, 'depositHistory'])->name('user.depositHistory');
Route::get('/trading/history', [App\Http\Controllers\AppController::class, 'investmentHistory'])->name('user.investmentHistory');
Route::get('/directs', [App\Http\Controllers\AppController::class, 'directs'])->name('user.directs');
Route::get('/level-wise-business', [App\Http\Controllers\AppController::class, 'levelWiseBusiness'])->name('user.levelWiseBusiness');
Route::get('/rank', [App\Http\Controllers\AppController::class, 'rank'])->name('user.rank');
Route::get('/openLevels', [App\Http\Controllers\AppController::class, 'openLevels'])->name('user.openLevels');
Route::get('/team-business', [App\Http\Controllers\AppController::class, 'teamBusiness'])->name('user.teamBusiness');
Route::get('/tree', [App\Http\Controllers\AppController::class, 'tree'])->name('user.tree');
Route::get('/port-reward', [App\Http\Controllers\AppController::class, 'portReward'])->name('user.portReward');
Route::get('/royalty', [App\Http\Controllers\AppController::class, 'royalty'])->name('user.royalty');
Route::get('/transactions', [App\Http\Controllers\AppController::class, 'transactions'])->name('user.transactions');
Route::get('/withdrawal/now', [App\Http\Controllers\AppController::class, 'withdrawNow'])->name('user.withdrawNow');
Route::get('/withdrawal/history', [App\Http\Controllers\AppController::class, 'withdrawalHistory'])->name('user.withdrawalHistory');
Route::get('/tickets', [App\Http\Controllers\AppController::class, 'tickets'])->name('user.tickets');
Route::get('/admin/add-stake', [App\Http\Controllers\SiteController::class, 'addAdminStake'])->name('addAdminStake');
Route::get('/register-user', [App\Http\Controllers\SiteController::class, 'registerUser'])->name('registerUser');

Route::middleware(['auth', 'verified'])->group(function () {
    Route::get('dashboard', function () {
        return Inertia::render('dashboard');
    })->name('dashboard');
});

require __DIR__.'/settings.php';
require __DIR__.'/auth.php';
