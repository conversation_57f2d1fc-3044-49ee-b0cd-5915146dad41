<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends \App\Base\Database\MigrationBase
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        $this->schema->create('user_wallets', function (\App\Base\Database\BlueprintBase $table) {
            $table->id();
            $table->integer('user_id')->nullable()->index();
            $table->integer('wallet_id')->nullable()->index();
            $table->decimal('balance',48,8)->default(0)->index();

            $table->unique(['user_id','wallet_id'],'user_wallet');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('user_wallets');
    }
};
