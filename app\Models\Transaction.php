<?php

namespace App\Models;

use App\Base\Model\BaseModel;
use App\Components\Helper;
use App\Constants\CommonConstants;
use App\Constants\UserConstants;
use Illuminate\Support\Facades\DB;

class Transaction extends BaseModel
{
    protected $fillable = [
        'ref_no',
        'type_id',
        'user_id',
        'wallet_id',
        'user_wallet_id',
        'particulars',
        'is_debit',
        'amount',
        'before_balance',
        'after_balance',
        'data',
        'reference_id',
        'release_date',
        'time',
        'created_at',
        'updated_at',
    ];

    public function user()
    {
        return $this->hasOne(User::class, 'id', 'user_id');
    }

    public function type()
    {
        return $this->hasOne(TransactionType::class, 'id', 'type_id');
    }

    public function wallet()
    {
        return $this->hasOne(Wallet::class, 'id', 'wallet_id');
    }

    public function userWallet()
    {
        return $this->hasOne(UserWallet::class, 'id', 'user_wallet_id');
    }

    public static function sendRoiOfRoi($maxTime=null) {
        if(!$maxTime) {
            $maxTime=strtotime("+3 minutes")-10;
        }

        if(time()>=$maxTime) {
            return true;
        }

        $transactions=self::query()
            ->where('type_id','=',TransactionType::TRANSACTION_TYPE_DIVIDEND)
            ->where('is_distributed','=',CommonConstants::NO)
            ->limit(50)->orderBy("id")->get();
        if(count($transactions)>0) {
            foreach ($transactions as $transaction) {
                print "Start with #".$transaction->id.PHP_EOL;
                DB::statement("update `transactions` set `is_distributed`=1 where `id`=".$transaction->id.";");
                DB::beginTransaction();
                try {
                    $decodeData=json_decode($transaction->data,true);
                    $userEarnings=[];

                    $user=\App\Models\User::query()->where('id','=',$transaction->user_id)->first();
                    if($user) {
                        if ($user->status != User::STATUS_ACTIVE) {
                            throw new \Exception('User login status is not active');
                        }
                    } else {
                        throw new \Exception('User details not found');
                    }
                    $tree=\App\Components\Helper::getParentTree($user);
                    if(is_array($tree) && count($tree)>0) {
                        $levelPercentage=0;
                        $levelSetting=LevelSetting::query()
                            ->where('id','=',1)->first();
                        if($levelSetting) {
                            $levelPercentage=$levelSetting->incentive;
                        }
                        $lastUserId=null;
                        foreach ($tree as $level=>$treeUser) {
                            if($treeUser['rank_percentage']>$levelPercentage) {
                                $levelPercentage=$treeUser['rank_percentage'];
                                $lastUserId=$treeUser['id'];
                            }
                            if($level===1) {
                                if($treeUser['opened_levels']>=1) {
                                    $userEarnings[$level]=$treeUser;
                                    $userEarnings[$level]['percentage']=$levelPercentage;
                                }
                            } else {
                                if($treeUser['opened_levels']>=1) {
                                    if($lastUserId==$treeUser['id'] || $treeUser['rank_percentage']>$levelPercentage) {
                                        $userEarnings[$level]=$treeUser;
                                        $userEarnings[$level]['percentage']=$levelPercentage;
                                    }
                                }
                            }
                        }
                    } else {
                        throw new \Exception('No tree structure found');
                    }

                    $finalReturn=[];

                    //print_r($tree);print_r($userEarnings); //die;

                    if(count($userEarnings)>0) {
                        $lastPercentage=0;
                        foreach ($userEarnings as $level_id=>$userEarning) {
                            $diff=round($userEarnings[$level_id]['percentage']-$lastPercentage,2);
                            if($diff>0) {
                                $finalAmount=round((($transaction->amount*$diff)/100),CommonConstants::USD_PRECISION);
                                if($finalAmount>0) {
                                    $finalAmount = Helper::fetchUpdatedAmount($finalAmount,$userEarning['income_x'],$userEarning['active_investment'],$userEarning['total_earnings']);
                                    if($finalAmount>0) {
                                        $finalReturn[$level_id]=[
                                            'user_id'=>$userEarning['id'],
                                            'amount'=>$finalAmount,
                                        ];
                                    }
                                }
                            }
                            $lastPercentage=$userEarnings[$level_id]['percentage'];
                        }
                    }

                    //print_r($finalReturn);print_r($levelPercentage);die;

                    if(count($finalReturn)>0) {
                        $transactionQuery="INSERT INTO `transactions` (`ref_no`, `type_id`, `user_id`, `wallet_id`, `user_wallet_id`, `particulars`, `is_debit`, `amount`, `before_balance`, `after_balance`, `data`, `reference_id`, `time`, `created_at`, `updated_at`) VALUES ";
                        $txnTime=Transaction::generateReferenceNumber(10);
                        $txnCounter=1;
                        $userWallet=[];
                        foreach ($finalReturn as $level_id=>$finalUser) {
                            $wallet=UserWallet::fetchUserWallet($finalUser['user_id'],Wallet::WALLET_EARNINGS);
                            if($wallet) {
                                $finalUser['before_balance']=$wallet->balance;
                                $userWallet[$wallet->id]=$finalUser['amount'];
                                $finalUser['after_balance']=round(($finalUser['amount']+$wallet->balance),CommonConstants::USD_PRECISION);
                                $finalReturn[$level_id]['wallet_id']=$wallet->id;
                                $finalReturn[$level_id]['balance']=$finalUser['amount'];
                                if($level_id>1) {
                                    $transactionQuery .= "(" . ($txnTime . $txnCounter) . ", " . TransactionType::TRANSACTION_TYPE_DIVIDEND_COMMISSION . ", " . $finalUser['user_id'] . ", " . Wallet::WALLET_EARNINGS . ", " . $wallet->id . ", 'Dividend level ".$level_id." commission against #" . $decodeData['ref_no'] . "', 0, '" . $finalUser['amount'] . "', '" . $finalUser['before_balance'] . "', '" . $finalUser['after_balance'] . "', '" . json_encode(['transaction_id' => $transaction->id]) . "', '" . $transaction->id . "', '" . date(CommonConstants::PHP_DATE_FORMAT) . "', '" . date(CommonConstants::PHP_DATE_FORMAT) . "', '" . date(CommonConstants::PHP_DATE_FORMAT) . "'),";
                                } else {
                                    $transactionQuery .= "(" . ($txnTime . $txnCounter) . ", " . TransactionType::TRANSACTION_TYPE_DIVIDEND_COMMISSION . ", " . $finalUser['user_id'] . ", " . Wallet::WALLET_EARNINGS . ", " . $wallet->id . ", 'Dividend level ".$level_id." commission against #" . $decodeData['ref_no'] . "', 0, '" . $finalUser['amount'] . "', '" . $finalUser['before_balance'] . "', '" . $finalUser['after_balance'] . "', '" . json_encode(['transaction_id' => $transaction->id]) . "', '" . $transaction->id . "', '" . date(CommonConstants::PHP_DATE_FORMAT) . "', '" . date(CommonConstants::PHP_DATE_FORMAT) . "', '" . date(CommonConstants::PHP_DATE_FORMAT) . "'),";
                                }
                            }
                            $txnCounter++;
                        }
                        $transactionQuery=rtrim($transactionQuery,",");
                        $transactionQuery.=";";
                        DB::statement($transactionQuery);

                        Log::insertLog([
                            'user_id' => null,
                            'particulars' => "batch query transaction #".$transaction->id." sql",
                            'type' => 'daily_roi_of_roi_transaction_query',
                            'data' => json_encode([
                                'id' => $transaction->id,
                                'message' => $transactionQuery,
                            ])
                        ]);

                        $updateQuery="UPDATE `user_wallets` SET `balance` = `balance` + CASE ";
                        foreach ($finalReturn as $finalUser) {
                            $updateQuery .= " WHEN `id` = '" . $finalUser['wallet_id'] . "' THEN " . $finalUser['balance'];
                        }
                        $updateQuery .= " ELSE 0 END;";
                        DB::statement($updateQuery);

                        Log::insertLog([
                            'user_id' => null,
                            'particulars' => "batch query transaction #".$transaction->id." wallet sql",
                            'type' => 'daily_roi_wallet_query',
                            'data' => json_encode([
                                'id' => $transaction->id,
                                'message' => $updateQuery,
                            ])
                        ]);

                        $updateUserEarningQuery="UPDATE `users` SET `total_earnings` = `total_earnings` + CASE ";
                        foreach ($finalReturn as $finalUser) {
                            $updateUserEarningQuery .= " WHEN `id` = '" . $finalUser['user_id'] . "' THEN " . $finalUser['balance'];
                        }
                        $updateUserEarningQuery .= " ELSE 0 END;";
                        DB::statement($updateUserEarningQuery);

                        $updateUserEarningQuery="UPDATE `users` SET `lifetime_earnings` = `lifetime_earnings` + CASE ";
                        foreach ($finalReturn as $finalUser) {
                            $updateUserEarningQuery .= " WHEN `id` = '" . $finalUser['user_id'] . "' THEN " . $finalUser['balance'];
                        }
                        $updateUserEarningQuery .= " ELSE 0 END;";
                        DB::statement($updateUserEarningQuery);

                        Log::insertLog([
                            'user_id' => null,
                            'particulars' => "batch query transaction #".$transaction->id." user total_earnings sql",
                            'type' => 'daily_roi_user_earning_query',
                            'data' => json_encode([
                                'id' => $transaction->id,
                                'message' => $updateUserEarningQuery,
                            ])
                        ]);

                        $updateUserEarningQuery1="UPDATE `users` SET `total_working_earnings` = `total_working_earnings` + CASE ";
                        foreach ($finalReturn as $finalUser) {
                            $updateUserEarningQuery1 .= " WHEN `id` = '" . $finalUser['user_id'] . "' THEN " . $finalUser['balance'];
                        }
                        $updateUserEarningQuery1 .= " ELSE 0 END;";
                        DB::statement($updateUserEarningQuery1);

                        $updateUserEarningQuery1="UPDATE `users` SET `lifetime_working_earnings` = `lifetime_working_earnings` + CASE ";
                        foreach ($finalReturn as $finalUser) {
                            $updateUserEarningQuery1 .= " WHEN `id` = '" . $finalUser['user_id'] . "' THEN " . $finalUser['balance'];
                        }
                        $updateUserEarningQuery1 .= " ELSE 0 END;";
                        DB::statement($updateUserEarningQuery1);

                        Log::insertLog([
                            'user_id' => null,
                            'particulars' => "batch query transaction #".$transaction->id." user total_working_earnings sql",
                            'type' => 'daily_roi_user_earning_query',
                            'data' => json_encode([
                                'id' => $transaction->id,
                                'message' => $updateUserEarningQuery1,
                            ])
                        ]);

                    } else {
                        Log::insertLog([
                            'user_id' => null,
                            'particulars' => 'Unable to process daily roi of roi#'.$transaction->id,
                            'type' => 'daily_roi_of_roi_log',
                            'data' => json_encode([
                                'logs' => $userEarnings,
                                'final' => $finalReturn
                            ])
                        ]);
                    }
                    DB::commit();
                } catch (\Exception $e) {
                    DB::rollBack();
                    Log::insertLog([
                        'user_id' => null,
                        'particulars' => 'Unable to process daily roi of roi #'.$transaction->id,
                        'type' => 'daily_roi_of_roi_error',
                        'data' => json_encode([
                            'message' => $e->getMessage(),
                            'trace' => $e->getTraceAsString(),
                        ])
                    ]);
                }
            }
        } else {
            print "No pending requests found".PHP_EOL;
            return true;
        }

        sleep(rand(1,3));
        return self::sendRoiOfRoi($maxTime);
    }
}
