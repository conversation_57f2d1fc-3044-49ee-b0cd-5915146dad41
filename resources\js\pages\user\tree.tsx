import {Head, <PERSON>} from '@inertiajs/react';
import UserLayout from "@/layouts/user/user-layout";
import {useEffect, useState} from "react";
import {UnderConstruction} from "@/components/under-construction";
import {useUserContext} from "@/context/UserContext";
import UserService from "@/services/UserService";



export default function Rank() {

    const [tree, setTree] = useState<any>(null);
    const [pageLoaded, setPageLoaded] = useState(false);
    const [btnLoading, setBtnLoading] = useState(false);
    const [searchFilter, setSearchFilter] = useState({ sponsor: '' });
    const [expandedNodes, setExpandedNodes] = useState<Set<string>>(new Set());
    const [hoveredNode, setHoveredNode] = useState<string | null>(null);
    const [pinnedNodes, setPinnedNodes] = useState<Set<string>>(new Set());
    const [isAnimating, setIsAnimating] = useState(false);
    const [hoverTimeout, setHoverTimeout] = useState<NodeJS.Timeout | null>(null);
    const [chartInstance, setChartInstance] = useState<any>(null);
    const [isChartReady, setIsChartReady] = useState(false);

    const {
        userDetails,
        httpServiceObject
    } = useUserContext();

    // Check if node should be expanded (either pinned or hovered)
    const isNodeExpanded = (nodeId: string) => {
        return pinnedNodes.has(nodeId) || hoveredNode === nodeId;
    };

    // Toggle node pinning (for click)
    const toggleNodePin = (nodeId: string) => {
        setPinnedNodes(prev => {
            const newSet = new Set(prev);
            if (newSet.has(nodeId)) {
                newSet.delete(nodeId);
            } else {
                newSet.add(nodeId);
            }
            return newSet;
        });
    };

    // Handle mouse enter with debouncing and animation control
    const handleMouseEnter = (nodeId: string) => {
        // Clear any existing timeout
        if (hoverTimeout) {
            clearTimeout(hoverTimeout);
        }

        // Don't allow hover expansion during animation
        if (isAnimating) return;

        // Set a small delay to prevent accidental hovers during animations
        const timeout = setTimeout(() => {
            setHoveredNode(nodeId);
        }, 150); // Small delay to prevent cascading

        setHoverTimeout(timeout);
    };

    // Handle mouse leave with debouncing
    const handleMouseLeave = (nodeId: string) => {
        // Clear any pending hover timeout
        if (hoverTimeout) {
            clearTimeout(hoverTimeout);
            setHoverTimeout(null);
        }

        // Only clear hover if it's the same node
        if (hoveredNode === nodeId) {
            setHoveredNode(null);
        }
    };



    // Update expanded nodes whenever hover or pin state changes
    useEffect(() => {
        const newExpandedNodes = new Set(pinnedNodes);
        if (hoveredNode) {
            newExpandedNodes.add(hoveredNode);
        }

        // Only update if there's actually a change
        const currentExpanded = Array.from(expandedNodes).sort();
        const newExpanded = Array.from(newExpandedNodes).sort();

        if (JSON.stringify(currentExpanded) !== JSON.stringify(newExpanded)) {
            setIsAnimating(true);
            setExpandedNodes(newExpandedNodes);

            // Clear animation flag after animation completes
            setTimeout(() => {
                setIsAnimating(false);
            }, 300); // Match animation duration
        }
    }, [hoveredNode, pinnedNodes]);

    // Parse data from the tree node data string
    const parseNodeData = (dataString: string) => {
        const lines = dataString.split('<br />');
        const data: any = {};

        lines.forEach(line => {
            if (line.includes('Username:')) {
                data.username = line.replace('Username: ', '').trim();
            } else if (line.includes('Address:')) {
                data.address = line.replace('Address: ', '').trim();
            } else if (line.includes('Self Investment:')) {
                data.selfInvestment = line.replace('Self Investment: ', '').trim();
            } else if (line.includes('Team Business:')) {
                data.teamBusiness = line.replace('Team Business: ', '').trim();
            } else if (line.includes('Total Business:')) {
                data.totalBusiness = line.replace('Total Business: ', '').trim();
            }
        });

        return data;
    };


    const fetchStats = async () => {

        try {
            let stats = await UserService.getTree(httpServiceObject, searchFilter);
            setTree(stats);

        } catch (error) {
            console.error("Error fetching stats:", error);
        }
    };

    useEffect(()=>{
        setPageLoaded(true);
    },[]);

    useEffect(()=>{
        if(pageLoaded) {
            fetchStats();
        }
    },[pageLoaded, searchFilter]);



    const submitForm = async (e: React.FormEvent<HTMLFormElement>) => {
        e.preventDefault();
        setBtnLoading(true);
        const myformdata = new FormData(e.currentTarget);
        let formData = Object.fromEntries(myformdata.entries());

        try {
            setSearchFilter(formData);
        } catch (err) {
            console.error("Unexpected error:", err);
        } finally {
            setBtnLoading(false);
        }
    };



    // Render chart only when tree data changes
    useEffect(() => {
        if (!tree) return;

        setTimeout(() => {
            // Clear existing chart
            const container = document.querySelector('.chart-container');
            if (container) {
                container.innerHTML = '';
            }

            // @ts-ignore
            const chart = new d3.OrgChart()
                .compact(false)
                .nodeHeight((_d: any) => 120) // Fixed height initially
                .nodeWidth((_d: any) => 120)  // Fixed width initially
                .childrenMargin((_d: any) => 50)
                .compactMarginBetween((_d: any) => 35)
                .compactMarginPair((_d: any) => 30)
                .neighbourMargin((_a: any, _b: any) => 20)
                .nodeContent(function (d: any, _i: any, _arr: any, _state: any) {
                    const isExpanded = expandedNodes.has(d.data.id);
                    const nodeData = parseNodeData(d.data.data);

                    if (isExpanded) {
                        let html='';
                        if(d.data.level_id>=3) {
                            html = '<div style="margin-top: 10px; text-align: center;">' +
                                '<button onclick="event.stopPropagation(); window.handleViewClick(\'' + d.data.referral_code + '\')" class="btn btn-square btn-theme d-inline-block view-btn">View</button>'+
                                '</div>';
                        } else {
                            if (d.data.level_id == 1) {
                                if(d.data.referral_code!=userDetails.referral_code) {
                                    html = '<div style="margin-top: 10px; text-align: center;">' +
                                        '<button onclick="event.stopPropagation(); window.handleViewClick(\'\')" class="btn btn-square btn-theme d-inline-block view-btn">Go to root</button>' +
                                        '</div>';
                                }
                            }
                        }
                       // Expanded view - detailed card like in screenshot
                       return `
                           <div class="tree-node-expanded" style="width:${d.width}px;height:${d.height}px;cursor:pointer;"
                                data-node-id="${d.data.id}"
                                onclick="window.toggleNode('${d.data.id}')">
                               <div class="expanded-card" style="
                                   ${ (d.data.isActivate) ? 'background: linear-gradient(135deg, #925f1c,rgb(107, 86, 40), #ba842e);' : 'background: linear-gradient(135deg,rgb(241, 168, 168),rgb(151, 27, 27));' }

                                   border-radius: 15px;
                                   padding: 15px;
                                   color: white;
                                   width: 100%;
                                   height: 100%;
                                   box-shadow: 0 8px 25px rgba(0,0,0,0.3);
                                   position: relative;
                               ">
                                   <div style="text-align: center; margin-bottom: 15px;">
                                       <div style="
                                           background: rgba(255,255,255,1);
                                           border-radius: 50%;
                                           width: 60px;
                                           height: 60px;
                                           margin: 0 auto 10px;
                                           display: flex;
                                           align-items: center;
                                           justify-content: center;
                                           font-size: 24px;
                                           font-weight: bold;
                                       ">
                                            <img src="/backend/assets/img/logo-512.png" style="height: 40px; width: 40px;"/>
                                       </div>
                                       <div style="font-weight: bold; font-size: 14px;">${d.data.name || 'Unknown'}</div>
                                       <div style="font-size: 11px; opacity: 0.8;">${d.data.username}</div>
                                   </div>
                                   ${d.data.html_data}
                                   ${html}
                               </div>
                           </div>
                       `;
                   } else {
                       // Compact view - small card with avatar and name
                        let html='';
                        if(d.data.level_id>=3) {
                            html = '<div style="margin-top: 10px; text-align: center;">' +
                                '<button onclick="event.stopPropagation(); window.handleViewClick(\'' + d.data.referral_code + '\')" class="btn btn-square btn-theme d-inline-block view-btn">View</button>'+
                                '</div>';
                        } else {
                            if (d.data.level_id == 1) {
                                if(d.data.referral_code!=userDetails.referral_code) {
                                    html = '<div style="margin-top: 10px; text-align: center;">' +
                                        '<button onclick="event.stopPropagation(); window.handleViewClick(\'\')" class="btn btn-square btn-theme d-inline-block view-btn">Go to root</button>' +
                                        '</div>';
                                }
                            }
                        }
                       return `
                           <div class="tree-node-compact" style="width:${d.width}px;height:${d.height}px;cursor:pointer;"
                                data-node-id="${d.data.id}"
                                onclick="window.toggleNode('${d.data.id}')">
                               <div class="compact-card" style="
                                   ${ (d.data.isActivate) ? 'background: linear-gradient(135deg, #925f1c,rgb(107, 86, 40), #ba842e);' : 'background: linear-gradient(135deg,rgb(241, 168, 168),rgb(151, 27, 27));' }
                                   border-radius: 15px;
                                   padding: 10px;
                                   color: white;
                                   width: 100%;
                                   height: 100%;
                                   display: flex;
                                   flex-direction: column;
                                   align-items: center;
                                   justify-content: center;
                                   box-shadow: 0 4px 15px rgba(0,0,0,0.2);
                                   transition: transform 0.2s ease;
                               " onmouseover="this.style.transform='scale(1.05)'" onmouseout="this.style.transform='scale(1)'">
                                   <div style="
                                       background: rgba(255,255,255,1);
                                       border-radius: 50%;
                                       width: 40px;
                                       height: 40px;
                                       display: flex;
                                       align-items: center;
                                       justify-content: center;
                                       font-size: 16px;
                                       font-weight: bold;
                                       margin-bottom: 8px;
                                   ">
                                       <img src="/backend/assets/img/logo-512.png" style="height: 30px; width: 30px;"/>
                                   </div>
                                   <div style="font-size: 10px; font-weight: bold; text-align: center; line-height: 1.2;">
                                       ${d.data.name || 'Unknown'}
                                   </div>
                                   <div style="font-size: 8px; opacity: 0.8; text-align: center;">
                                       ${d.data.username}
                                   </div>
                                   ${html}
                               </div>
                           </div>
                       `;
                   }
               })
               .container('.chart-container')
               .data(tree)
               .render();

            setChartInstance(chart);
            setIsChartReady(true);

            // Make functions globally available
            (window as any).toggleNode = (nodeId: string) => {
                toggleNodePin(nodeId);
            };

            (window as any).handleViewClick = (referralCode: string) => {
                console.log("referralCode is ",referralCode);
                setSearchFilter({ code: referralCode });
            };

            // Add event listeners for hover effects after render
            setTimeout(() => {
                const treeNodes = document.querySelectorAll('.tree-node-compact, .tree-node-expanded');

                treeNodes.forEach((node) => {
                    const nodeId = node.getAttribute('data-node-id');
                    if (nodeId) {
                        // Remove existing listeners to prevent duplicates
                        (node as HTMLElement).onmouseenter = null;
                        (node as HTMLElement).onmouseleave = null;

                        // Add hover listeners with improved event handling
                        (node as HTMLElement).onmouseenter = (e) => {
                            e.stopPropagation();
                            handleMouseEnter(nodeId);
                        };
                        (node as HTMLElement).onmouseleave = (e) => {
                            e.stopPropagation();
                            handleMouseLeave(nodeId);
                        };
                    }
                });
            }, 200);

        }, 1000);

    }, [tree]);

    // Update chart layout when expanded nodes change (optimized re-render)
    useEffect(() => {
        if (!tree || !isChartReady) return;

        // Use a shorter timeout for better responsiveness
        const updateTimeout = setTimeout(() => {
            // Clear existing chart
            const container = document.querySelector('.chart-container');
            if (container) {
                container.innerHTML = '';
            }

            // @ts-ignore
            const chart = new d3.OrgChart()
                .compact(false)
                .nodeHeight((d: any) => expandedNodes.has(d.data.id) ? 350 : 120)
                .nodeWidth((d: any) => expandedNodes.has(d.data.id) ? 250 : 120)
                .childrenMargin((_d: any) => 50)
                .compactMarginBetween((_d: any) => 35)
                .compactMarginPair((_d: any) => 30)
                .neighbourMargin((_a: any, _b: any) => 20)
                .nodeContent(function (d: any, _i: any, _arr: any, _state: any) {
                    const isExpanded = expandedNodes.has(d.data.id);

                    if (isExpanded) {
                        let html='';
                        if(d.data.level_id>=3) {
                            html = '<div style="margin-top: 10px; text-align: center;">' +
                                '<button onclick="event.stopPropagation(); window.handleViewClick(\'' + d.data.referral_code + '\')" class="btn btn-square btn-theme d-inline-block view-btn">View</button>'+
                                '</div>';
                        } else {
                            if (d.data.level_id == 1) {
                                if(d.data.referral_code!=userDetails.referral_code) {
                                    html = '<div style="margin-top: 10px; text-align: center;">' +
                                        '<button onclick="event.stopPropagation(); window.handleViewClick(\'\')" class="btn btn-square btn-theme d-inline-block view-btn">Go to root</button>' +
                                        '</div>';
                                }
                            }
                        }
                        // Expanded view - detailed card like in screenshot
                        return `
                            <div class="tree-node-expanded" style="width:${d.width}px;height:${d.height}px;cursor:pointer;"
                                 data-node-id="${d.data.id}"
                                 onclick="window.toggleNode('${d.data.id}')">
                                <div class="expanded-card" style="
                                    ${ (d.data.isActivate) ? 'background: linear-gradient(135deg, #925f1c,rgb(107, 86, 40), #ba842e);' : 'background: linear-gradient(135deg,rgb(241, 168, 168),rgb(151, 27, 27));' }

                                    border-radius: 15px;
                                    padding: 15px;
                                    color: white;
                                    width: 100%;
                                    height: 100%;
                                    box-shadow: 0 8px 25px rgba(0,0,0,0.3);
                                    position: relative;
                                ">
                                    <div style="text-align: center; margin-bottom: 15px;">
                                        <div style="
                                            background: rgba(255,255,255,1);
                                            border-radius: 50%;
                                            width: 60px;
                                            height: 60px;
                                            margin: 0 auto 10px;
                                            display: flex;
                                            align-items: center;
                                            justify-content: center;
                                            font-size: 24px;
                                            font-weight: bold;
                                        ">
                                             <img src="/backend/assets/img/logo-512.png" style="height: 40px; width: 40px;"/>
                                        </div>
                                        <div style="font-weight: bold; font-size: 14px;">${d.data.name || 'Unknown'}</div>
                                        <div style="font-size: 11px; opacity: 0.8;">${d.data.username}</div>
                                    </div>
                                    ${d.data.html_data}
                                    ${html}
                                </div>
                            </div>
                        `;
                    } else {
                        // Compact view - small card with avatar and name

                        let html='';
                        if(d.data.level_id>=3) {
                            html = '<div style="margin-top: 10px; text-align: center;">' +
                                '<button onclick="event.stopPropagation(); window.handleViewClick(\'' + d.data.referral_code + '\')" class="btn btn-square btn-theme d-inline-block view-btn">View</button>'+
                                '</div>';
                        } else {
                            if (d.data.level_id == 1) {
                                if(d.data.referral_code!=userDetails.referral_code) {
                                    html = '<div style="margin-top: 10px; text-align: center;">' +
                                        '<button onclick="event.stopPropagation(); window.handleViewClick(\'\')" class="btn btn-square btn-theme d-inline-block view-btn">Go to root</button>' +
                                        '</div>';
                                }
                            }
                        }
                        return `
                            <div class="tree-node-compact" style="width:${d.width}px;height:${d.height}px;cursor:pointer;"
                                 data-node-id="${d.data.id}"
                                 onclick="window.toggleNode('${d.data.id}')">
                                <div class="compact-card" style="
                                    ${ (d.data.isActivate) ? 'background: linear-gradient(135deg, #925f1c,rgb(107, 86, 40), #ba842e);' : 'background: linear-gradient(135deg,rgb(241, 168, 168),rgb(151, 27, 27));' }
                                    border-radius: 15px;
                                    padding: 10px;
                                    color: white;
                                    width: 100%;
                                    height: 100%;
                                    display: flex;
                                    flex-direction: column;
                                    align-items: center;
                                    justify-content: center;
                                    box-shadow: 0 4px 15px rgba(0,0,0,0.2);
                                    transition: transform 0.2s ease;
                                " onmouseover="this.style.transform='scale(1.05)'" onmouseout="this.style.transform='scale(1)'">
                                    <div style="
                                        background: rgba(255,255,255,1);
                                        border-radius: 50%;
                                        width: 40px;
                                        height: 40px;
                                        display: flex;
                                        align-items: center;
                                        justify-content: center;
                                        font-size: 16px;
                                        font-weight: bold;
                                        margin-bottom: 8px;
                                    ">
                                        <img src="/backend/assets/img/logo-512.png" style="height: 30px; width: 30px;"/>
                                    </div>
                                    <div style="font-size: 10px; font-weight: bold; text-align: center; line-height: 1.2;">
                                        ${d.data.name || 'Unknown'}
                                    </div>
                                    <div style="font-size: 8px; opacity: 0.8; text-align: center;">
                                        ${d.data.username}
                                    </div>
                                    ${html}

                                </div>
                            </div>
                        `;
                    }
                })
                .container('.chart-container')
                .data(tree)
                .render();

            // Re-assign global functions to ensure they're available
            /* (window as any).toggleNode = (nodeId: string) => {
                toggleNodePin(nodeId);
            };

            (window as any).handleViewClick = (referralCode: string) => {
                setSearchFilter({ code: referralCode });
            }; */

            // Re-add event listeners after render
            setTimeout(() => {
                const treeNodes = document.querySelectorAll('.tree-node-compact, .tree-node-expanded');

                treeNodes.forEach((node) => {
                    const nodeId = node.getAttribute('data-node-id');
                    if (nodeId) {
                        (node as HTMLElement).onmouseenter = (e) => {
                            e.stopPropagation();
                            handleMouseEnter(nodeId);
                        };
                        (node as HTMLElement).onmouseleave = (e) => {
                            e.stopPropagation();
                            handleMouseLeave(nodeId);
                        };
                    }
                });
            }, 50);

        }, 100); // Reduced timeout for better responsiveness

        return () => clearTimeout(updateTimeout);

    }, [expandedNodes, tree, isChartReady]);

    // Cleanup effect
    useEffect(() => {
        return () => {
            if (hoverTimeout) {
                clearTimeout(hoverTimeout);
            }
        };
    }, [hoverTimeout]);

    return (
        <UserLayout>
            <Head>
                <style>{`
                    .tree-node-compact, .tree-node-expanded {
                        // transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
                    }

                    .compact-card {
                        // transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
                    }

                    .compact-card:hover {
                        transform: scale(1.02) !important;
                        box-shadow: 0 6px 20px rgba(0,0,0,0.3) !important;
                    }

                    .expanded-card {
                        // transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
                    }

                    .expanded-card:hover {
                        box-shadow: 0 10px 30px rgba(0,0,0,0.4) !important;
                    }

                    /* Smooth hover expansion effect */
                    .tree-node-compact:hover {
                        z-index: 100;
                    }

                    .tree-node-expanded {
                        z-index: 100;
                    }

                    .chart-container {
                        overflow: visible !important;
                    }

                    .chart-container svg {
                        overflow: visible !important;
                    }

                    /* Custom scrollbar for better UX */
                    .chart-container::-webkit-scrollbar {
                        width: 8px;
                        height: 8px;
                    }

                    .chart-container::-webkit-scrollbar-track {
                        background: #f1f1f1;
                        border-radius: 4px;
                    }

                    .chart-container::-webkit-scrollbar-thumb {
                        background: #888;
                        border-radius: 4px;
                    }

                    .chart-container::-webkit-scrollbar-thumb:hover {
                        background: #555;
                    }

                    /* Animation for node transitions */
                    @keyframes nodeExpand {
                        from {
                            transform: scale(0.8);
                            opacity: 0.8;
                        }
                        to {
                            transform: scale(1);
                            opacity: 1;
                        }
                    }

                    .tree-node-expanded {
                        animation: nodeExpand 0.3s ease-out;
                    }

                    /* Responsive adjustments */
                    @media (max-width: 768px) {
                        .expanded-card {
                            font-size: 10px !important;
                        }

                        .compact-card {
                            font-size: 9px !important;
                        }
                    }
                `}</style>
            </Head>

            <main className="adminuiux-content has-sidebar">

                <div className="container mt-4">
                    <div className="row gx-3 align-items-center">
                        <div className="col-12 col-sm">
                            <nav aria-label="breadcrumb" className="mb-2">
                                <ol className="breadcrumb mb-0">
                                    <li className="breadcrumb-item bi"><Link href={route('user.dashboard')}><i
                                        className="bi bi-house-door me-1 fs-14"></i> Dashboard</Link></li>
                                    <li className="breadcrumb-item active bi" aria-current="page">Genealogy</li>
                                </ol>
                            </nav>
                            <h5>Genealogy</h5>
                        </div>
                        <div className="col-12 col-sm-auto text-end py-3 py-sm-0">
                            <form onSubmit={submitForm}>


                                <input type="text" name="code" className="form-control d-inline-block w-auto align-middle mx-3" placeholder="Sponsor Code" />

                                <button type="submit" className="btn btn-square btn-theme d-inline-block" disabled={btnLoading}>
                                    {btnLoading ? "Loading..." : "Search"}
                                </button>

                            </form>
                        </div>
                    </div>
                </div>

                {/*<div className="container" id="main-content">


                    <div className="row">
                        <div className="col-12">
                            <div className="card adminuiux-card mt-4 mb-0">
                                <div className="card-body h-500">
                                    <OrgDiagram centerOnCursor={true} config={config} />
                                </div>
                            </div>
                        </div>
                    </div>

                </div>*/}
                {/*<UnderConstruction/>*/}

                <div className="container" id="main-content">


                    <div className="row">
                        <div className="col-12">
                            <div className="card adminuiux-card mt-4 mb-0">
                                <div className="card-body h-650">
                                    <div className="chart-container"></div>
                                </div>
                            </div>
                        </div>
                    </div>

                </div>



            </main>


        </UserLayout>
    );
}
