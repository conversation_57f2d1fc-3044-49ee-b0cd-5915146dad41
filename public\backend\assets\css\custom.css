.coverimg > img {
    display: block !important;
}
.copyButton {
    position: absolute;
    right: 15px;
    top: 10px;
    cursor: pointer;
}
.nav-item.position-relative{
    min-width: 400px;
}
.adminuiux-header .navbar .navbar-brand img {
    height: 36px;
    margin-right: 3px !important;
     width: auto !important;
}
.MuiChartsLegend-root.MuiChartsLegend-vertical{
    display: none !important;
}
.invest-stat-boxes .card-body{
    padding: 21px 15px;
}
.container, .container-lg, .container-md, .container-sm, .container-xl, .container-xxl {
    max-width: 100%;
}
table.table{
    border-radius: 25px;
    overflow: hidden;
}
.navbar.navbar-expand-lg.fixed-top{
    background: #000;
}
div#app{
    overflow-x: hidden !important;
}
.sidebar-close .adminuiux-wrap .adminuiux-sidebar {
    left: 0 !important;
}
.h-500{
    height: 500px;
}
.custom-select-dropdown select{
    height: 58px !important;
}
.invest-stat-boxes-2 .card-body{
    padding: 14px 15px !important;
}
.coin-logo {
    height: 30px !important;
    filter: brightness(0) invert(1);
}
.custom-calc-height{
    height: calc(100vh - 250px) !important;
}
.w-160{
    width: 160px !important;
}
.MuiPagination-ul *{
    color: #fff !important;
}
.MuiPagination-ul .Mui-selected{
    background: #eea616 !important;
}
.dropdown-menu.show {
    display: block;
    left: auto;
    right: 0;
}
.inner-sidebar-wrap .inner-sidebar{
    width: 400px !important;
}
.card-body.overflow-y-auto.chat-list{
    max-height: 550px;
    overflow: auto;
}
.custom-height-area{
    max-height: 600px;
    overflow: auto;
}
.h-650{
    height: 650px !important;
}
.card-body.h-650{
    overflow: hidden !important;
}
.svg-chart-container{
    height: 600px !important;
}
.ant-picker.ant-picker-range{
    display: flex;
    height: 58px;
}
.ant-picker.ant-picker-range input::placeholder, .ant-picker.ant-picker-range svg{
    color: #8a8986 !important;
}
.ant-picker.ant-picker-range:hover{
    background: transparent !important;
}

.custom-dashboard-box{
    margin-top: 20px;
    height: calc(100% - 36px);
}

@media(max-width: 767px){
    #header-navbar{
        display: none !important;
    }
    .topbar-user-address{
        display: none !important;
    }

    .adminuiux-header .navbar .sm-mi-45px {
        right: 0 !important;
    }
    .adminuiux-header .navbar .navbar-brand > div {
        display: block !important;
    }
    .height-300{
        height: auto !important;
    }
    .dashboard-wallets .col-md-3{
        margin-bottom: 15px !important;
    }
    .dashboard-wallets .col-md-3:last-child{
        margin-bottom: 0px !important;
    }
    .m-order-2{
        order: 2 !important;
    }
    .m-order-1{
        order: 1 !important;
    }
    .invest-box .badge.badge-light {
        border-radius: 15px;
        font-size: 12px;
        font-weight: 400;
        padding: 3px 4px;
    }
    .m-d-flex{
        display: flex !important;
        flex-direction: column !important;
    }
    .invest-stat-boxes{
        margin-bottom: 0px !important;
    }
    nav[aria-label="breadcrumb"]{
        display: flex;
        justify-content: center !important;
        text-align: center;
    }
    nav[aria-label="breadcrumb"] + h5{
        text-align: center;
    }
    .col-12.col-sm-auto.text-end.py-3.py-sm-0{
        text-align: center !important;
    }
    .col .badge{
        margin:0px 5px 5px !important;
    }
    .m-mb-15{
        margin-bottom: 15px !important;
    }
    .m-direction-row{
        flex-direction: row !important;
        gap: 10px !important;
    }

    .tickets-area .inner-sidebar-wrap{
        display: block !important;
        min-height: auto !important;

    }
    .inner-sidebar-wrap .inner-sidebar {
        width: 100% !important;
        max-height: 250px !important;
        overflow: hidden;
        height: 250px !important;
        min-height: auto !important;
        margin-left: 0 !important;
        position: relative;
        margin-bottom: 20px;
    }
    .card-body.overflow-y-auto.chat-list {
        max-height: 150px !important;
    }
    .custom-dashboard-box{
        margin: 0 15px 15px;
        height: auto;

    }

}
