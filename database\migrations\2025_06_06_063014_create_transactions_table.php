<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends \App\Base\Database\MigrationBase
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        $this->schema->create('transactions', function (\App\Base\Database\BlueprintBase $table) {
            $table->id();
            $table->bigInteger('ref_no')->unique();
            $table->integer('type_id')->nullable()->index();
            $table->integer('user_id')->nullable()->index();
            $table->integer('wallet_id')->nullable()->index();
            $table->integer('user_wallet_id')->nullable();
            $table->string('particulars',800)->nullable();
            $table->integer('is_debit')->default(\App\Constants\CommonConstants::YES)->index();
            $table->integer('is_distributed')->default(\App\Constants\CommonConstants::NO)->comment('for roi-of-roi')->index();
            $table->decimal('amount',48,8)->default(\App\Constants\CommonConstants::NO)->index();
            $table->decimal('before_balance',48,8)->default(\App\Constants\CommonConstants::NO);
            $table->decimal('after_balance',48,8)->default(\App\Constants\CommonConstants::NO);
            $table->string('data')->nullable()->index();
            $table->string('reference_id')->nullable()->index();
            $table->timestamp('time')->nullable()->index();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('transactions');
    }
};
