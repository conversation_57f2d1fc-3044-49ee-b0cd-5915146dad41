import {<PERSON>, <PERSON>} from '@inertiajs/react';
import UserLayout from "@/layouts/user/user-layout";
import {<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>} from "@mui/x-charts";
import {useUserContext} from "@/context/UserContext";
import {useEffect, useState} from "react";
import UserService from "@/services/UserService";
import {WITHDRAWAL_WALLET} from "@/constants/Constants";
import {z} from "zod";
import {SendWriteCall} from "@/components/SendWriteCall";


export default function InvestNow({minStakeValue,maxStakeValue}) {

    const {
        userDetails,
        address,
        showNotification,
        httpServiceObject,
        walletBalances,
        fetchBalanceFromApi,
        updateWalletBalances,
        neonixBalance,
        usdtBalance,
        dxeBalance,
        dxeUsdPrice,
        DXEAddress,DXEStakeAddress,USDTAddress,
        USDTContractInstance,DXEContractInstance,DXEStakeContractInstance,
        convertWeiToEth,convertEthToWei,
        DXEContractABI,USDTContractABI,DXEStakeContractABI,
        handleSignMsg
    } = useUserContext();

    const [dashboardStats, setDashboardStats] = useState<any>(null);
    const [btnLoading, setBtnLoading] = useState(false);
    const [minimumInvestAmount, setMinimumInvestAmount] = useState(minStakeValue);
    const [amount, setAmount] = useState<any>(minimumInvestAmount);
    const [approxTokens, setApproxTokens] = useState<any>(0);
    const [dailyTokensLeft, setDailyTokensLeft] = useState<any>(0);
    const [wallet, setWallet] = useState<any>('usdt');
    const [needUserAllowance, setNeedUserAllowance] = useState<any>(null);
    const [sendTransferRequest, setSendTransferRequest] = useState<any>(null);
    const [userHaveAllowance, setUserHaveAllowance] = useState(false);

    const investAmount=(val)=>{
        // setAmount(null);
        setAmount(val);
    }

    const fetchStakingStats = () =>{
        try {
            if(!DXEStakeContractInstance) {
                console.log("Unable to fetchStakingStats");
                return;
            }
            DXEStakeContractInstance.getDailyStakesLeft().then(res=>{
                console.log("Fetch getDailyStakesLeft res",res);
                let leftTokens = convertWeiToEth(res);
                console.log("leftTokens is ",leftTokens);
                setDailyTokensLeft(leftTokens);
            }).catch(err=>{
                console.log("Unable to fetch daily pending stakes left ",err);
            });
        } catch (e) {
            console.log("fetchStakingStats error ",e);
        }
    }

    const fetchStats = async () => {

        try {
            await fetchBalanceFromApi();

            let stats = await UserService.dashboardStats(httpServiceObject);
            setDashboardStats(stats);
            fetchStakingStats();
            updateWalletBalances();
        } catch (error) {
            console.error("Error fetching stats:", error);
        }
    };
    useEffect(() => {
        fetchStats();
    }, [])

    useEffect(() => {
        setApproxTokens(0);
        if(amount>0) {
            if (wallet == 'usdt') {
                setApproxTokens((parseFloat(amount / dxeUsdPrice).toFixed(6))*1);
            } else {
                setApproxTokens((parseFloat(amount / dxeUsdPrice).toFixed(6))*1);
            }
        }
    }, [amount,wallet])

    const checkAllowance= () => {
        setNeedUserAllowance(null);
        setSendTransferRequest(null);
        return new Promise(async (resolve, reject) => {
            try {
                if (wallet == 'usdt') {
                    USDTContractInstance.allowance(address, DXEStakeAddress).then(res => {
                        res = convertWeiToEth(res);
                        console.log("USDTContractInstance allowance res ",res," amount ",amount);
                        if (res <= 0 || amount > res) {
                            try {
                                setNeedUserAllowance({
                                    address: USDTAddress, // Contract address
                                    abi: USDTContractABI,
                                    functionName: "approve",
                                    args: [
                                        DXEStakeAddress, // spender
                                        convertEthToWei(amount) // amount
                                    ],
                                });
                                showNotification('Approve allowance request for continue','info');
                                return resolve(false);
                            } catch (err) {
                                showNotification('Unable to get USDT token allowance');
                                return resolve(false);
                            }
                        } else {
                            setUserHaveAllowance(true);
                            return resolve(true);
                        }
                    }).catch(err => {
                        console.log("USDT allowance err is ", err);
                        return resolve(false);
                    });
                } else {
                    DXEContractInstance.allowance(address, DXEStakeAddress).then(res => {
                        res = convertWeiToEth(res);
                        console.log("DXEContractInstance allowance res ",res," approxTokens ",approxTokens);
                        if (res <= 0 || approxTokens > res) {
                            try {
                                setNeedUserAllowance({
                                    address: DXEAddress, // Contract address
                                    abi: DXEContractABI,
                                    functionName: "approve",
                                    args: [
                                        DXEStakeAddress, // spender
                                        convertEthToWei(approxTokens) // amount
                                    ],
                                });
                                showNotification('Approve allowance request for continue','info');
                                return resolve(false);
                            } catch (err) {
                                showNotification('Unable to get DRIXE token allowance');
                                return resolve(false);
                            }
                        } else {
                            setUserHaveAllowance(true);
                            return resolve(true);
                        }
                    }).catch(err => {
                        console.log("DRIXE allowance err is ", err);
                        showNotification('Unable to fetch DRIXE token allowance');
                        return resolve(false);
                    });
                }
            } catch (err1) {
                console.log("DRIXE allowance err1 is ", err1);
                showNotification('Unable to fetch DRIXE token allowance');
                return resolve(false);
            }
        });
    }

    const sendRequest= () => {
        setNeedUserAllowance(null);
        setSendTransferRequest(null);
        return new Promise(async (resolve, reject) => {
            try {
                if (wallet == 'usdt') {
                    if(!DXEStakeContractInstance) {
                        showNotification('Unable to process your request at this moment. Try after sometime.');
                        return resolve(false);
                    }
                    DXEStakeContractInstance.getDailyStakesLeft().then(res=>{
                        console.log("Fetch getDailyStakesLeft res",res);
                        let leftTokens = convertWeiToEth(res);
                        console.log("leftTokens is ",leftTokens);

                        if(approxTokens>leftTokens) {
                            showNotification('Daily tokens limit crossed','error');
                            return resolve(true);

                            /* setSendTransferRequest({
                                address: DXEStakeAddress, // Contract address
                                abi: DXEStakeContractABI,
                                functionName: "scheduleDepositUSDT",
                                args: [
                                    convertEthToWei(amount) // amount
                                ],
                            });
                            showNotification('Approve investment request','info');
                            return resolve(true); */

                        } else {
                            setSendTransferRequest({
                                address: DXEStakeAddress, // Contract address
                                abi: DXEStakeContractABI,
                                functionName: "depositUSDT",
                                args: [
                                    convertEthToWei(amount) // amount
                                ],
                            });
                            showNotification('Approve investment request','info');
                            return resolve(true);
                        }
                    }).catch(err=>{
                        console.log("Unable to fetch daily pending stakes left ",err);
                        showNotification('Daily staking limit is crossed');
                        return resolve(false);
                    });
                } else {
                    setSendTransferRequest({
                        address: DXEStakeAddress, // Contract address
                        abi: DXEStakeContractABI,
                        functionName: "depositToken",
                        args: [
                            convertEthToWei(approxTokens) // amount
                        ],
                    });
                    showNotification('Approve investment request','info');
                    return resolve(true);
                }
            } catch (err1) {
                console.log("DRIXE allowance err1 is ", err1);
                showNotification('Unable to fetch DRIXE token allowance');
                return resolve(false);
            }
        });
    }
    const handleDepositRequest= () => {
        return new Promise(async (resolve, reject) => {
            let haveAllowance = await checkAllowance();
            console.log("haveAllowance ",haveAllowance);
            console.log("needUserAllowance ",needUserAllowance);
            console.log("userHaveAllowance ",userHaveAllowance);
            //if((haveAllowance && !needUserAllowance) || userHaveAllowance) {
            if(userHaveAllowance) {
                let sendRequestResult = await sendRequest();
                if(sendRequestResult) {
                    return resolve(true);
                } else {
                    return resolve(false);
                }
            } else {
                return resolve(true);
            }
        });
    };
    const investRequest = () => {
        if(dxeUsdPrice<=0) {
            showNotification('Unable to fetch token price');
        } else {
            handleDepositRequest().then(res => {

            }).catch(err => {
                console.log(err.message, err)
                showNotification(err);
            });
            /* handleSignMsg().then(async (res) => {
                if (!res) {
                    showNotification('Unable to Connect Wallet');
                } else {
                    const response = await UserService.saveHash(httpServiceObject, res + Math.random());
                    if (response) {
                        showNotification('Your request has been submitted', 'success');
                        investAmount(minimumInvestAmount);
                    }
                }
            }).catch(err => {
                console.log(err.message, err)
                showNotification(err);
            }); */
        }
    }

    const submitForm = async (e: React.FormEvent<HTMLFormElement>) => {
        e.preventDefault();
        setBtnLoading(true);

        const myformdata = new FormData(e.currentTarget);
        let formData = Object.fromEntries(myformdata.entries());

        const userInfoSchema = z.object({
            amount: z.number()
                .min(minimumInvestAmount, "Minimum amount should be $"+minimumInvestAmount)
                .max((wallet=='usdt' ? usdtBalance : (dxeBalance*dxeUsdPrice)), 'Insufficient Balance.'),
        });

        try {
            formData.amount = (formData?.amount * 1);
            userInfoSchema.parse(formData);
            console.log(formData.amount, 'amount is', formData);
            console.log(formData.wallet, 'wallet is', formData);

            // const response = await UserService.InvestRequest(formData);
            const response = await UserService.InvestRequest(httpServiceObject, formData);
            if(response){
                investRequest();
            }

        } catch (err) {
            if(err instanceof z.ZodError) {
                showNotification(err.issues[0].message);
            } else {
                console.log(err.message, 'asdfasdf', err);
                showNotification(err);
            }
        } finally {
            setBtnLoading(false);
        }
    };

    const handleAllowance = (params) => {
        setNeedUserAllowance(null);
        if(params) {
            showNotification('Allowance request sent. Please wait for investment request','info');
            setTimeout(function (){
                investRequest();
            },3000);
        } else {
            showNotification('Allowance request cancelled');
        }
        console.log("handleAllowance params is ",params);
    }

    useEffect(()=>{
        if(userHaveAllowance) {
            investRequest();
        }
    },[userHaveAllowance]);

    const handleTransfer = (params) => {
        setSendTransferRequest(null);
        setNeedUserAllowance(null);
        setUserHaveAllowance(false);
        if(params) {
            showNotification('Request sent successfully.','success');
            UserService.saveHash(httpServiceObject, params).then(res=>{
                investAmount(minimumInvestAmount);
                showNotification('Your transaction will auto approve after getting confirmations.', 'success');
            }).catch(err=>{
                showNotification('Something went wrong while adding request.');
            });
        } else {
            showNotification('Investment request cancelled');
        }
        console.log("handleTransfer params is ",params);
    }
    return (
        <UserLayout>
            {(needUserAllowance ? (<>
                <SendWriteCall onCompleted={handleAllowance} txData={needUserAllowance}/>
            </>) : (<></>))}

            {(sendTransferRequest ? (<>
                <SendWriteCall onCompleted={handleTransfer} txData={sendTransferRequest}/>
            </>) : (<></>))}
            <main className="adminuiux-content has-sidebar">

                <div className="container mt-4">
                    <div className="row gx-3 align-items-center">
                        <div className="col-12 col-sm">
                            <nav aria-label="breadcrumb" className="mb-2">
                                <ol className="breadcrumb mb-0">
                                    <li className="breadcrumb-item bi"><Link href={route('user.dashboard')}><i
                                        className="bi bi-house-door me-1 fs-14"></i> Dashboard</Link></li>
                                    <li className="breadcrumb-item active bi" aria-current="page">Invest Now</li>
                                </ol>
                            </nav>
                            <h5>Invest Now</h5>
                        </div>
                        {/*<div className="col-12 col-sm-auto text-end py-3 py-sm-0">

                        </div>*/}
                    </div>
                </div>


                <div className="container mt-4 m-d-flex" id="main-content" data-bs-spy="scroll" data-bs-target="#list-example"
                     data-bs-smooth-scroll="true">

                    <div className="row m-order-2" id="list-item-1">


                        <div className="col-12 col-xl-6 col-xxl-4">
                            <div className="card adminuiux-card mb-4">
                                <div className="card-body">

                                    <h5 className="fw-medium">Active Investment</h5>
                                    <h1 className="fw-medium text-white">{dashboardStats?.user?.formatted_active_investment} <small>USD</small></h1>


                                </div>
                            </div>
                        </div>
                        <div className="col-12 col-xl-6 col-xxl-4">
                            <div className="card adminuiux-card mb-4">
                                <div className="card-body">

                                    <h5 className="fw-medium">USDT Balance</h5>
                                    <h1 className="fw-medium text-white">{usdtBalance} <small>USDT</small></h1>


                                </div>
                            </div>
                        </div>
                        <div className="col-12 col-xl-6 col-xxl-4">
                            <div className="card adminuiux-card mb-4">
                                <div className="card-body">

                                    <h5 className="fw-medium">DXE Balance</h5>
                                    <h1 className="fw-medium text-white">{dxeBalance} <small>DXE</small></h1>

                                </div>
                            </div>
                        </div>

                    </div>
                    <div className="row m-order-1">
                        <div className="col-12">
                            <div className="row">
                                <div className="col-12 invest-box col-lg-8 mb-4">

                                    <div className="card adminuiux-card">
                                        <div className="card-header">
                                            <h5>Invest Now </h5>
                                            <p className="text-secondary">Start your money growing with smart
                                                investment</p>
                                        </div>
                                        <form onSubmit={submitForm}>
                                            <div className="card-body">
                                                <div className="row mb-2">
                                                    <div className="col-6 col-md-6 col-xl-4 mb-3">

                                                        <input className="form-check-input me-1" type="radio"
                                                               name="wallet" onChange={() => setWallet('usdt')} id="usdt-deposit" checked={wallet === 'usdt'}
                                                        />
                                                        <label className="form-check-label"
                                                               htmlFor="usdt-deposit">USDT</label>

                                                    </div>
                                                    <div className="col-6 col-md-6 col-xl-4 mb-3">
                                                        <input className="form-check-input me-1" onChange={() => setWallet('dxe')} type="radio" checked={wallet === 'dxe'}
                                                               name="wallet" id="dxe-deposit"
                                                        />
                                                        <label className="form-check-label"
                                                               htmlFor="dxe-deposit">DXE</label>
                                                    </div>

                                                    <div className="col-12 col-md-12 mb-3">
                                                        <div className="form-floating">
                                                            <input autoComplete={'off'} type="text" onChange={(e)=>investAmount(e.target.value)} required={true} name={'amount'} value={amount} className="form-control" id="Amount"
                                                                   placeholder="Amount" />
                                                            <label htmlFor="Amount">Amount</label>
                                                        </div>
                                                        <p className={'text-end'}>
                                                            <span onClick={()=>investAmount(50)} className="badge badge-light bg-yellow-subtle text-yellow-emphasis me-1 mt-1 cursor-pointer">$50</span>
                                                            <span onClick={()=>investAmount(100)} className="badge badge-light bg-yellow-subtle text-yellow-emphasis me-1 mt-1 cursor-pointer">$100</span>
                                                            <span onClick={()=>investAmount(200)} className="badge badge-light bg-yellow-subtle text-yellow-emphasis me-1 mt-1 cursor-pointer">$200</span>
                                                            <span onClick={()=>investAmount(500)} className="badge badge-light bg-yellow-subtle text-yellow-emphasis me-1 mt-1 cursor-pointer">$500</span>
                                                            <span onClick={()=>investAmount(1000)} className="badge badge-light bg-yellow-subtle text-yellow-emphasis me-1 mt-1 cursor-pointer">$1,000</span>
                                                            <span onClick={()=>investAmount(2000)} className="badge badge-light bg-yellow-subtle text-yellow-emphasis me-1 mt-1 cursor-pointer">$2,000</span>
                                                            <span onClick={()=>investAmount(5000)} className="badge badge-light bg-yellow-subtle text-yellow-emphasis me-1 mt-1 cursor-pointer">$5,000</span>
                                                        </p>
                                                    </div>

                                                </div>
                                                <div className="row align-items-center">
                                                    <div className="col">
                                                        <h5>${dxeUsdPrice}</h5>
                                                        <p className="text-secondary small">DXE USD Price</p>
                                                    </div>
                                                    <div className="col">
                                                        <h5>{approxTokens}</h5>
                                                        <p className="text-secondary small">Approx DXE Tokens</p>
                                                    </div>
                                                    <div className="col">
                                                        <h5>{dailyTokensLeft}</h5>
                                                        <p className="text-secondary small">Daily Staking Tokens Left</p>
                                                    </div>
                                                    <div className="col-auto">
                                                        <button type="submit" className="btn btn-theme" disabled={btnLoading}>
                                                            {btnLoading ? "Loading..." : "Continue"}
                                                        </button>

                                                    </div>
                                                </div>
                                            </div>
                                        </form>
                                    </div>
                                </div>
                                <div className="col-12 col-lg-4 mb-4  invest-stat-boxes">
                                    {walletBalances &&
                                    Object.entries(walletBalances)
                                        .filter(([key, item]) => item?.id !== WITHDRAWAL_WALLET)
                                        .map(([key, item]) => (
                                            <div className="card adminuiux-card mb-4" key={key}>
                                                <div className="card-body">
                                                    <div className="row gx-3 ">
                                                        <div className="col-auto">
                                                            <i className="bi bi-wallet2 fs-4 avatar avatar-50 bg-theme-1 text-white rounded"></i>
                                                        </div>
                                                        <div className="col">
                                                            <h4 className="mb-0">{item?.formattedBalance} USD</h4>
                                                            <p className="small opacity-75">{item?.name} Wallet</p>
                                                        </div>
                                                    </div>

                                                </div>
                                            </div>
                                        ))}
                                </div>
                            </div>
                        </div>
                    </div>

                </div>
            </main>


        </UserLayout>
    );
}
