<?php

namespace App\Http\Controllers;

use App\Http\Controllers\Controller;
use App\Http\Requests\Settings\ProfileUpdateRequest;
use App\Models\RoyaltySetting;
use App\Models\Setting;
use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Inertia\Inertia;
use Inertia\Response;

class AppController extends Controller
{
    public function dashboard()
    {
        return Inertia::render('user/dashboard');
    }
    public function investNow()
    {
        $minStakeValue=(int)Setting::getValue(Setting::SETTING_MINIMUM_INVESTMENT);
        $maxStakeValue=(int)Setting::getValue(Setting::SETTING_MAXIMUM_INVESTMENT);
        return Inertia::render('user/investNow',compact('minStakeValue','maxStakeValue'));
    }
    public function depositHistory()
    {
        return Inertia::render('user/depositHistory');
    }
    public function investmentHistory()
    {
        return Inertia::render('user/investmentHistory');
    }
    public function directs()
    {
        return Inertia::render('user/directs');
    }
    public function levelWiseBusiness()
    {
        return Inertia::render('user/levelWiseBusiness');
    }
    public function rank()
    {
        return Inertia::render('user/rank');
    }
    public function openLevels()
    {
        return Inertia::render('user/openLevels');
    }
    public function teamBusiness()
    {
        return Inertia::render('user/teamBusiness');
    }
    public function tree()
    {
        return Inertia::render('user/tree');
    }
    public function portReward()
    {
        return Inertia::render('user/portReward');
    }
    public function royalty()
    {
        $plans=RoyaltySetting::query()
            ->with('rank')
            ->orderBy("id")
            ->get();
        if(count($plans)>0) {
            $plans=json_decode(json_encode($plans, true), true);
        }
        return Inertia::render('user/royalty',compact('plans'));
    }
    public function transactions()
    {
        return Inertia::render('user/transactions');
    }
    public function withdrawNow()
    {
        $minValue=(int)Setting::getValue(Setting::SETTING_MINIMUM_WITHDRAWAL);
        $multipleValue=(int)Setting::getValue(Setting::SETTING_WITHDRAWAL_MULTIPLE);
        return Inertia::render('user/withdrawNow',compact('minValue','multipleValue'));
    }
    public function withdrawalHistory()
    {
        return Inertia::render('user/withdrawalHistory');
    }
    public function tickets()
    {
        return Inertia::render('user/tickets');
    }
}
