<?php

namespace App\Base\Traits;

use App\Components\Helper;
use App\Constants\CommonConstants;
use App\Models\Log;
use Illuminate\Support\Str;

trait BaseModelTrait
{
    protected static function booted()
    {
        //parent::boot();

        self::creating(function ($model) {
            $model->onCreating();
        });

        self::created(function ($model) {
            $model->onCreated();
        });

        self::updating(function ($model) {
            $model->onUpdating();
        });

        self::updated(function ($model) {
            $model->onUpdated();
        });

        self::deleting(function ($model) {
            $model->onDeleting();
        });

        self::deleted(function ($model) {
            $model->onDeleted();
        });

        self::saving(function ($model) {
            $model->onSaving();
        });

        self::saved(function ($model) {
            $model->onSaved();
        });
    }

    public function onCreating()
    {
        $ip = request()->ip();
        if (is_array($this->fillable)) {

            if (in_array('uuid', $this->fillable)) {
                $this->uuid = (string)Str::uuid();
            }
            if (in_array('ref_no', $this->fillable)) {
                $this->ref_no = (string)self::generateReferenceNumber();
            }

            if (in_array('created_at', $this->fillable)) {
                if ($this->created_at == null) {
                    $this->created_at = date(CommonConstants::PHP_DATE_FORMAT);
                }
            }

            if (in_array('created_ip', $this->fillable)) {
                if ($this->created_ip == null) {
                    $this->created_ip = $ip;
                }
            }

            if (in_array('updated_at', $this->fillable)) {
                if ($this->updated_at == null) {
                    $this->updated_at = date(CommonConstants::PHP_DATE_FORMAT);
                }
            }

            if (in_array('updated_ip', $this->fillable)) {
                if ($this->updated_ip == null) {
                    $this->updated_ip = $ip;
                }
            }

            if (in_array('country_id', $this->fillable)) {
                if ($this->country_id == null) {
                    $ipDetails = Helper::fetchIpDetails($ip, true);
                    $this->country_id = ($ipDetails ? $ipDetails['country']->id : null);
                }

                if($this->country_id==null) {
                    $this->country_id=CommonConstants::DEFAULT_COUNTRY;
                }
            }

            if (in_array('user_agent', $this->fillable)) {
                if ($this->user_agent == null) {
                    $this->user_agent = request()->userAgent();
                }
            }

        }
    }

    public function onCreated()
    {
        $this->insertCreateUpdateLog();
    }

    public function onUpdating()
    {
        $ip = request()->ip();
        if (in_array('updated_at', $this->fillable)) {
            if ($this->updated_at == null) {
                $this->updated_at = date(CommonConstants::PHP_DATE_FORMAT);
            }
        }

        if (in_array('updated_ip', $this->fillable)) {
            if ($this->updated_ip == null) {
                $this->updated_ip = $ip;
            }
        }
    }

    public function onUpdated()
    {
        $this->insertCreateUpdateLog(false);
    }

    public function onDeleting()
    {
        //give implementation
    }

    public function onDeleted()
    {
        //give implementation
    }

    public function onSaving()
    {
        //give implementation
    }

    public function onSaved()
    {
        //give implementation
    }

    private function insertCreateUpdateLog($isNewRecord = true)
    {
        $calledClassTable = self::getTable();
        $logClass = new Log();
        $logClassTable = $logClass->getTable();
        if ($calledClassTable != $logClassTable) {
            if ($isNewRecord) {
                Log::insertLog([
                    'user_id' => Helper::getCurrentUserId(),
                    'particulars' => 'Record Created',
                    'type' => self::getTable() . '_create',
                    'data' => $this->attributesToArray()
                ]);
            } else {
                Log::insertLog([
                    'user_id' => Helper::getCurrentUserId(),
                    'particulars' => 'Record Updated',
                    'type' => self::getTable() . '_update',
                    'data' => $this->changedAttributes()
                ]);
            }
        }
    }

    public function changedAttributes()
    {
        $attributes = [];
        foreach ($this->original as $attributeKey => $attributeValue) {
            $newValue = $this->attributes[$attributeKey];
            if ($newValue != $attributeValue) {
                $attributes[$attributeKey] = [
                    'original' => $attributeValue,
                    'new' => $newValue
                ];
            }
        }
        return $attributes;
    }
}
