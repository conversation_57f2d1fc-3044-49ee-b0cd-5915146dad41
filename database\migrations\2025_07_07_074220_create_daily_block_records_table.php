<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends \App\Base\Database\MigrationBase
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        $this->schema->create('daily_block_records', function (\App\Base\Database\BlueprintBase $table) {
            $table->id();
            $table->bigInteger('start_block')->index();
            $table->bigInteger('end_block')->nullable()->index();
            $table->integer('is_completed')->default(\App\Constants\CommonConstants::NO)->index();
            $table->integer('total')->nullable()->index();
            $table->string('txn_hash')->nullable()->index();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('daily_block_records');
    }
};
