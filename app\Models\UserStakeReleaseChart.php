<?php

namespace App\Models;

use App\Base\Model\BaseModel;
use App\Constants\CommonConstants;

class UserStakeReleaseChart extends BaseModel
{
    protected $fillable = [
        'ref_no',
        'user_id','user_stake_id','tokens','txn_hash',
        'release_date','is_released','created_at','updated_at',
    ];

    public function user()
    {
        return $this->hasOne(User::class, 'id', 'user_id');
    }

    public function userStake()
    {
        return $this->hasOne(UserStake::class, 'id', 'user_stake_id');
    }

    public static function makeChart($userStake) {
        try {
            $alreadyExist=self::query()
                ->where('user_stake_id','=',$userStake->id)
                ->first();
            if($alreadyExist) {
                throw new \Exception('Stake #'.$userStake->id.' chart already exist');
            }

            if(!$userStake->tokens || $userStake->tokens<=0) {
                throw new \Exception('Invalid tokens ('.$userStake->tokens.') for stake #'.$userStake->id.' chart');
            }

            $duration=Setting::getValue(Setting::SETTING_STAKE_MATURITY_DURATION);
            if($duration>0) {
                $tokenAmount=round(($userStake->tokens/$duration),CommonConstants::TOKEN_PRECISION);
                for ($i=1;$i<=$duration; $i++) {
                    $model=new UserStakeReleaseChart();
                    $model->user_id=$userStake->user_id;
                    $model->user_stake_id=$userStake->id;
                    $model->tokens=$tokenAmount;
                    $model->release_date=date(CommonConstants::PHP_DATE_FORMAT_SHORT,strtotime("+".$i." months",strtotime($userStake->roi_end_date)));
                    $model->save();
                }
            }
            return true;
        } catch (\Exception $e) {
            Log::insertLog([
                'user_id' => CommonConstants::ADMINISTRATIVE,
                'particulars' => 'Unable to make stake release chart #'.$userStake->id,
                'type' => 'user_stake_release_chart_error',
                'data' => json_encode([
                    'user_stake_id' => $userStake->id,
                    'error' => $e->getMessage(),
                    'trace' => $e->getTraceAsString(),
                ])
            ]);
        }
        return false;
    }
}
