<?php

namespace App\Models;

use App\Base\Model\BaseModel;

class UserStakeResetLog extends BaseModel
{
    protected $fillable = [
        'user_id',
        'user_stake_id',
        'comments',
        'data',
        'created_at',
    ];

    public function user()
    {
        return $this->hasOne(User::class, 'id', 'user_id');
    }

    public function userStake()
    {
        return $this->hasOne(UserStake::class, 'id', 'user_stake_id');
    }
}
