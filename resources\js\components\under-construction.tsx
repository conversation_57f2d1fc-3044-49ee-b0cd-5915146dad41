import { Link } from '@inertiajs/react';


export function UnderConstruction() {
    return (
        <>
            <div className="container-fluid mt-2" id="main-content">
                <div className="card adminuiux-card height-dynamic position-relative overflow-hidden custom-calc-height"
                >
                    <figure className="m-0 position-absolute top-0 start-0 h-100 w-100 coverimg opacity-50">
                        <img src="/backend/assets/img/background-image/under-construction.jpg" className={'w-100'}/>
                    </figure>
                    <div className="card-body z-index-1">
                        <div className="row align-items-center justify-content-center text-center h-100">
                            <div className="col-auto">
                                <h1 className="fw-bold text-warning" style={{fontSize: '100px'}}><i
                                    className="bi bi-cone-striped mx-2"></i></h1>
                                <h1>Under <b>Construction</b></h1>
                                <p className="text-white mb-4">This page is still under
                                    construction</p>


                                <br/>
                                <Link href={route('user.dashboard')} className="btn btn-theme">Back to Dashboard <i
                                    className="bi bi-arrow-right"></i></Link>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </>
    );
}



