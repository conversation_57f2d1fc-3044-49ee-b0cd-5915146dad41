export default class HttpService {
    protected baseRoute = import.meta.env.VITE_API_URL || '';
    private async parseResponse(response: Response) {
        try {
            return response.headers.get("content-type")?.includes("application/json")
                ? await response.json()
                : null;
        } catch {
            return null;
        }
    }
    private async fetchBearerToken(): Promise<string> {
        let token = localStorage.getItem('token');
        if(!token || token=="") {
            return '';
        }
        return token;
    }
    public async post(uri: string, data: object = {}) {
        try {
            const bearerToken = await this.fetchBearerToken();
            const response = await fetch(`${this.baseRoute}${uri}`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    Authorization: `Bearer ${bearerToken}`,
                },
                body: JSON.stringify(data),
            });

            const responseData = await this.parseResponse(response);

            if (!response.ok) {
                return { error: responseData?.data || `POST request failed: ${response.statusText}` };
            }
            return responseData;
        } catch (error) {
            console.error('Error in POST request:', error);
            return { error: "Server is busy. Please try after sometime" };
        }
    }
}
