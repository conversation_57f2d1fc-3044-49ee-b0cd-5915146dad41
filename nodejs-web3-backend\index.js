require('dotenv').config({path: "./.env"});
const express = require('express');
const configSettings = require('./config');
const {Web3} = require('web3');
const web3Utils = require("web3-utils");
const ContractAbi = require("./Abis/DXEStakeContract.json");
const TokenAbi = require("./Abis/DXEContract.json");
const app = express();
const port = process.env.NODE_PORT;
const DEBUG_MODE = (process.env.DEBUG_MODE.toString().toLowerCase()=="true" ? true : false);
const networkConfig = configSettings.NETWORKS.bsc[process.env.PROVIDER_MODE];
const cors = require('cors');
const LogsDecoder = require('logs-decoder');
const logsDecoder = LogsDecoder.create();
const InputDataDecoder = require('ethereum-input-data-decoder');
app.use(cors());

const bodyParser = require("body-parser");
let jsonParser=bodyParser.json();
let urlEncodedParser=bodyParser.urlencoded({extended: false});

const bops = require("bops");

async function fetchWeb3Provider() {
    return new Web3(new Web3.providers.HttpProvider(networkConfig.config.rpcUrlHttp));
}

//public
app.post('/verify-login-hash',urlEncodedParser, async (req, response) => {
    if(DEBUG_MODE) {
        console.log("verify-login-hash called");
    }
    try {
        let messageHash=req.body.hash;
        if(DEBUG_MODE) {
            console.log("verify-login-hash hash ",messageHash);
        }
        if(messageHash!=undefined) {
            const message = 'Verify User Request';
            const msg = `0x${bops.from(message, 'utf8').toString('hex')}`;
            const web3 = await fetchWeb3Provider();
            const recoveredAddr = web3.eth.accounts.recover(msg, messageHash);
            response.send({status:'success',address:recoveredAddr});
        } else {
            response.send({status: 'error', message: 'Not verified properly'});
        }
    } catch (e) {
        response.send({status:'error',message:'Something went wrong.'});
    }
});
app.post('/get-balance',urlEncodedParser, async (req, response) => {
    if(DEBUG_MODE) {
        console.log("get-balance called");
    }
    try {
        let userAddress=req.body.address;
        if(DEBUG_MODE) {
            console.log("get-balance userAddress ",userAddress);
        }
        if(userAddress!=undefined) {
            const web3 = await fetchWeb3Provider();
            let balance = await web3.eth.getBalance(userAddress,"latest");
            if(balance) {
                balance = web3Utils.fromWei(balance,'ether');
                balance = balance * 1;
                balance = balance.toFixed(8);
                response.send({status:'success',data:{userAddress,balance}});
            } else {
                response.send({status: 'error', message: 'Not available at this moment'});
            }
        } else {
            response.send({status: 'error', message: 'Not verified properly'});
        }
    } catch (e) {
        response.send({status:'error',message:'Something went wrong.'});
    }
});
app.post('/get-current-block-number',urlEncodedParser, async (req, response) => {
    if(DEBUG_MODE) {
        console.log("get-current-block-number called");
    }
    try {
        const web3 = await fetchWeb3Provider();
        let blockNumber = await web3.eth.getBlockNumber();
        blockNumber=blockNumber.toString();
        if(blockNumber) {
            response.send({status:'success',data:{blockNumber}});
        } else {
            response.send({status: 'error', message: 'Not available at this moment'});
        }
    } catch (e) {
        console.log(e);
        response.send({status:'error',message:'Something went wrong.'});
    }
});
app.post('/transaction-details',urlEncodedParser, async (req, response) => {
    if(DEBUG_MODE) {
        console.log("transaction-details called");
    }
    try {
        let hash = req.body.hash;
        if (DEBUG_MODE) {
            console.log("transaction-details hash ", hash);
        }
        if (hash != undefined) {
            const web3 = await fetchWeb3Provider();
            const newWeb3 = new Web3();
            web3.eth.getTransaction(hash).then(res=> {
                if(res==null) {
                    response.send({status:'error',message:'Invalid hash requested.'});
                } else {
                    web3.eth.getTransactionReceipt(hash).then(async receipt => {
                        let currentBlock = await web3.eth.getBlockNumber();
                        if(currentBlock) {
                            currentBlock = web3.utils.fromWei(currentBlock,'ether');
                            currentBlock = web3.utils.toWei(currentBlock,'ether');
                            currentBlock = currentBlock * 1;
                        }
                        if(DEBUG_MODE) {
                            console.log("transaction-details currentBlock is ",currentBlock);
                        }
                        let finalResponse={};
                        let logsDecoded = [];
                        let confirmations = 0;
                        if(res.hasOwnProperty('blockNumber')) {
                            if(currentBlock>0) {
                                let txnBlock = res.blockNumber;
                                if(txnBlock) {
                                    txnBlock = web3.utils.fromWei(txnBlock,'ether');
                                    txnBlock = web3.utils.toWei(txnBlock,'ether');
                                    txnBlock = txnBlock * 1;

                                    confirmations = currentBlock - txnBlock;
                                }
                            }
                        }
                        finalResponse['confirmations']=confirmations;
                        finalResponse['hash']=receipt.transactionHash;
                        finalResponse['to']=receipt.to;
                        finalResponse['from']=receipt.from;


                        const contractInstance = new web3.eth.Contract(ContractAbi,networkConfig.contracts.stakeContract.contractAddress);
                        if(contractInstance) {
                            if(DEBUG_MODE) {
                                console.log("res to address is ",res.to);
                                console.log("contract address is ",contractInstance.options.address);
                                console.log("res is ",res);
                                console.log("receipt is ",receipt);
                                console.log("receipt logs are ",receipt.logs);
                                console.log("confirmations are ",confirmations);
                            }
                            if(receipt.to.toString().toLowerCase().trim()==contractInstance.options.address.toString().toLowerCase().trim()) {
                                if(DEBUG_MODE) {
                                    console.log("transaction-details decode logs here");
                                }
                                let inputDecoder = new InputDataDecoder(ContractAbi);
                                logsDecoder.addABI(ContractAbi);
                                const input = inputDecoder.decodeData(res.input);
                                if(DEBUG_MODE) {
                                    console.log("transaction-details input is ",input);
                                }
                                finalResponse['methodName']=input.method;
                                if(input.method=="depositToken" || input.method=="depositUSDT") {
                                    if (input.hasOwnProperty('inputs')) {
                                        try {
                                            if (input.inputs.length > 0) {
                                                let logIndex = 0;
                                                for (let item of input.inputs) {
                                                    if (DEBUG_MODE) {
                                                        console.log("item is ", item);
                                                        console.log("item hex is ", item._hex);
                                                        console.log("item hex number is ", parseInt(item._hex, 16).toLocaleString('fullwide', {useGrouping: false}));
                                                    }
                                                    let val = "" + parseInt(item._hex, 16).toLocaleString('fullwide', {useGrouping: false});
                                                    logsDecoded.push([input.names[logIndex], val]);
                                                    logIndex++;
                                                }
                                            }
                                        } catch (e) {
                                            if (DEBUG_MODE) {
                                                console.log("transaction-details "+input.method+" inputs decode error is ", e);
                                            }
                                        }
                                    }
                                } else if(input.method=="scheduleDepositUSDT") {
                                    if (input.hasOwnProperty('inputs')) {
                                        try {
                                            if (input.inputs.length > 0) {
                                                let logIndex = 0;
                                                for (let item of input.inputs) {
                                                    if (DEBUG_MODE) {
                                                        console.log("item is ", item);
                                                        console.log("item hex is ", item._hex);
                                                        console.log("item hex number is ", parseInt(item._hex, 16).toLocaleString('fullwide',{useGrouping:false}));
                                                    }
                                                    let val = ""+parseInt(item._hex, 16).toLocaleString('fullwide',{useGrouping:false});
                                                    logsDecoded.push([input.names[logIndex], val]);
                                                    logIndex++;
                                                }
                                            }
                                        } catch (e) {
                                            if (DEBUG_MODE) {
                                                console.log("transaction-details scheduleDepositUSDT inputs decode error is ", e);
                                            }
                                        }
                                    }
                                } else if(input.method=="authorizeAction") {
                                    if (input.hasOwnProperty('inputs')) {
                                        try {
                                            if (input.inputs.length > 0) {
                                                let logIndex = 0;
                                                for (let item of input.inputs) {
                                                    if(item.hasOwnProperty('_hex')) {
                                                        if (DEBUG_MODE) {
                                                            console.log("item is ", item);
                                                            console.log("item hex is ", item._hex);
                                                            console.log("item hex number is ", parseInt(item._hex, 16).toLocaleString('fullwide', {useGrouping: false}));
                                                        }
                                                        let val = "" + parseInt(item._hex, 16).toLocaleString('fullwide', {useGrouping: false});
                                                        logsDecoded.push([input.names[logIndex], val]);
                                                    } else {
                                                        if (DEBUG_MODE) {
                                                            console.log("item is ", item);
                                                            console.log("item is ", typeof item);
                                                            console.log("logIndex is ", logIndex);
                                                        }
                                                        if(typeof item=="string") {
                                                            if (DEBUG_MODE) {
                                                                console.log("item is ", item);
                                                                console.log("item hex number is ", web3Utils.hexToString(item));
                                                            }

                                                            let val = ""+web3Utils.hexToString(item);
                                                            logsDecoded.push([input.names[logIndex], val]);
                                                        }
                                                    }
                                                    logIndex++;
                                                }
                                            }
                                        } catch (e) {
                                            if (DEBUG_MODE) {
                                                console.log("transaction-details inputs decode error is ", e);
                                            }
                                        }
                                    }
                                }
                            } else {
                                finalResponse['methodName']='';
                            }
                        }
                        finalResponse['logs']=logsDecoded;
                        response.send({status:'success',result:finalResponse});
                    }).catch(error => {
                        if(DEBUG_MODE) {
                            console.log("transaction-details receipt error is ",error);
                        }
                        response.send({status:'error',message:'Hash details not fetched.'});
                    });
                }
            }).catch(error => {
                if(DEBUG_MODE) {
                    console.log("transaction-details error is ",error);
                }
                response.send({status:'error',message:'Hash details not fetched.'});
            });
        } else {
            response.send({status:'error',message:'Invalid request.'});
        }
    } catch (e) {
        response.send({status:'error',message:'Something went wrong.'});
    }
});
app.post('/token-usd-price',urlEncodedParser, async (req, response) => {
    if(DEBUG_MODE) {
        console.log("current-token-price called");
    }
    try {
        const web3 = await fetchWeb3Provider();
        if(DEBUG_MODE) {
            console.log("Stake contract address is ",networkConfig.contracts.stakeContract.contractAddress);
        }
        const contractInstance = new web3.eth.Contract(ContractAbi,networkConfig.contracts.stakeContract.contractAddress);
        if(contractInstance) {
            let tokenAddress = await contractInstance.methods.getDrixieToken().call();
            if(DEBUG_MODE) {
                console.log("Token address is ",tokenAddress);
            }
            const tokenInstance = new web3.eth.Contract(TokenAbi,tokenAddress);
            if(tokenInstance) {
                let price = await tokenInstance.methods.usdPrice().call();
                if(DEBUG_MODE) {
                    console.log("price is ", price);
                }
                if(price) {
                    price = web3Utils.fromWei(price,'ether');
                    price = price * 1;
                    price = (price.toFixed(8)*1);
                    response.send({status:'success',price:price});
                } else {
                    if(DEBUG_MODE) {
                        console.log("price is ", price);
                    }
                    response.send({status:'error',message:'Something went wrong. Try after sometime.'});
                }
            }
        } else {
            if(DEBUG_MODE) {
                console.log("contract ins");
            }
            response.send({status:'error',message:'Something went wrong. Try later.'});
        }
    } catch (e) {
        if(DEBUG_MODE) {
            console.log("error is ", e.message);
        }
        response.send({status:'error',message:'Something went wrong.'});
    }
});
app.post('/today-token-left',urlEncodedParser, async (req, response) => {
    if(DEBUG_MODE) {
        console.log("today-token-left called");
    }
    try {
        const web3 = await fetchWeb3Provider();
        if(DEBUG_MODE) {
            console.log("Stake contract address is ",networkConfig.contracts.stakeContract.contractAddress);
        }
        const contractInstance = new web3.eth.Contract(ContractAbi,networkConfig.contracts.stakeContract.contractAddress);
        if(contractInstance) {
            let dailyStakesLeft = await contractInstance.methods.getDailyStakesLeft().call();
            console.log("dailyStakesLeft is ",dailyStakesLeft);
            if(dailyStakesLeft) {
                dailyStakesLeft = web3Utils.fromWei(dailyStakesLeft,'ether');
                dailyStakesLeft = dailyStakesLeft * 1;
                dailyStakesLeft = (dailyStakesLeft.toFixed(8)*1);
                response.send({status:'success',amount:dailyStakesLeft});
            } else {
                console.log("dailyStakesLeft is ",dailyStakesLeft);
                if(DEBUG_MODE) {
                    response.send({status: 'error', message: 'Something went wrong. Try after sometime.'});
                }
            }
        } else {
            if(DEBUG_MODE) {
                console.log("contract ins");
            }
            response.send({status:'error',message:'Something went wrong. Try later.'});
        }
    } catch (e) {
        if(DEBUG_MODE) {
            console.log("error is ", e.message);
        }
        response.send({status:'error',message:'Something went wrong.'});
    }
});

//private
app.post('/send-token',urlEncodedParser, async (req, response) => {
    if(DEBUG_MODE) {
        console.log("send-token called");
    }
    try {
        let address = req.body.address;
        let amount = req.body.amount;
        if (DEBUG_MODE) {
            console.log("send-token params ", address, amount);
        }
        if (address != undefined && amount!=undefined) {
            const web3 = await fetchWeb3Provider();
            const contractInstance = new web3.eth.Contract(ContractAbi,networkConfig.contracts.stakeContract.contractAddress);

            let tokenAddress = await contractInstance.methods.getDrixieToken().call();
            if(DEBUG_MODE) {
                console.log("Token address is ",tokenAddress);
            }
            let encodedABI = contractInstance.methods.sendToken(tokenAddress,address,amount).encodeABI();

            let gasPrice = await web3.eth.getGasPrice();

            const userAddr = process.env.USER_ADDR;
            const userKey = process.env.USER_PK;

            const tx = {
                to: networkConfig.contracts.stakeContract.contractAddress,
                from: userAddr,
                gas: 2000000,
                gasPrice: gasPrice,
                //nonce: web3.eth.getTransactionCount(userAddr),
                data: encodedABI
            };

            web3.eth.accounts.signTransaction(tx, userKey)
                .then(signedTx => {
                    if(DEBUG_MODE) {
                        console.log("Signed Transaction: ", signedTx);
                    }
                    try {
                        web3.eth.sendSignedTransaction(signedTx.rawTransaction)
                            .on('transactionHash', (hash) => {
                                response.send({status: 'success', hash: hash});
                            }).catch((err3) => {
                            if(DEBUG_MODE) {
                                console.error("Error sendSignedTransaction ", err3);
                            }
                            response.send({status: 'error', message: 'Something went wrong'});
                        });
                    } catch (e) {
                        if(DEBUG_MODE) {
                            console.error("Error sending transaction: ", e);
                        }
                        response.send({status:'error',message:'Error in sending transaction.'});
                    }
                })
                .catch(err => {
                    if(DEBUG_MODE) {
                        console.error("Error signing transaction: ", err);
                    }
                    response.send({status:'error',message:'Error in signing transaction.'});
                });
        } else {
            response.send({status:'error',message:'Invalid request.'});
        }
    } catch (e) {
        response.send({status:'error',message:'Something went wrong.'});
    }
});
app.post('/register-stake',urlEncodedParser, async (req, response) => {
    if(DEBUG_MODE) {
        console.log("register-stake called");
    }
    try {
        let address = req.body.address;
        let amount = req.body.amount;
        let tokens = req.body.tokens;
        if (DEBUG_MODE) {
            console.log("register-stake params ", address, amount, tokens);
        }
        if (address != undefined && amount!=undefined && tokens!=undefined) {
            const web3 = await fetchWeb3Provider();
            const contractInstance = new web3.eth.Contract(ContractAbi,networkConfig.contracts.stakeContract.contractAddress);
            let encodedABI = contractInstance.methods.registerStake(address,tokens,amount,2).encodeABI();

            let gasPrice = await web3.eth.getGasPrice();

            const userAddr = process.env.USER_ADDR;
            const userKey = process.env.USER_PK;

            const tx = {
                to: networkConfig.contracts.stakeContract.contractAddress,
                from: userAddr,
                gas: 2000000,
                gasPrice: gasPrice,
                //nonce: web3.eth.getTransactionCount(userAddr),
                data: encodedABI
            };

            web3.eth.accounts.signTransaction(tx, userKey)
                .then(signedTx => {
                    if(DEBUG_MODE) {
                        console.log("Signed Transaction: ", signedTx);
                    }
                    try {
                        web3.eth.sendSignedTransaction(signedTx.rawTransaction)
                            .on('transactionHash', (hash) => {
                                response.send({status: 'success', hash: hash});
                            }).catch((err3) => {
                            if(DEBUG_MODE) {
                                console.error("Error sendSignedTransaction ", err3);
                            }
                            response.send({status: 'error', message: 'Something went wrong'});
                        });
                    } catch (e) {
                        if(DEBUG_MODE) {
                            console.error("Error sending transaction: ", e);
                        }
                        response.send({status:'error',message:'Error in sending transaction.'});
                    }
                })
                .catch(err => {
                    if(DEBUG_MODE) {
                        console.error("Error signing transaction: ", err);
                    }
                    response.send({status:'error',message:'Error in signing transaction.'});
                });
        } else {
            response.send({status:'error',message:'Invalid request.'});
        }
    } catch (e) {
        response.send({status:'error',message:'Something went wrong.'});
    }
});
app.post('/daily-mint',urlEncodedParser, async (req, response) => {
    if(DEBUG_MODE) {
        console.log("daily-mint called");
    }
    try {
        const web3 = await fetchWeb3Provider();
        if(DEBUG_MODE) {
            console.log("Stake contract address is ",networkConfig.contracts.stakeContract.contractAddress);
        }
        const contractInstance = new web3.eth.Contract(ContractAbi,networkConfig.contracts.stakeContract.contractAddress);
        if(contractInstance) {

        }
        let encodedABI = contractInstance.methods.dailyMint().encodeABI();

        let gasPrice = await web3.eth.getGasPrice();

        const userAddr = process.env.USER_ADDR;
        const userKey = process.env.USER_PK;

        const tx = {
            to: networkConfig.contracts.stakeContract.contractAddress,
            from: userAddr,
            gas: 2000000,
            gasPrice: gasPrice,
            //nonce: web3.eth.getTransactionCount(userAddr),
            data: encodedABI
        };

        web3.eth.accounts.signTransaction(tx, userKey)
            .then(signedTx => {
                if(DEBUG_MODE) {
                    console.log("Signed Transaction: ", signedTx);
                }
                try {
                    web3.eth.sendSignedTransaction(signedTx.rawTransaction)
                        .on('transactionHash', (hash) => {
                            response.send({status: 'success', hash: hash});
                        }).catch((err3) => {
                        if(DEBUG_MODE) {
                            console.error("Error sendSignedTransaction ", err3);
                        }
                        response.send({status: 'error', message: 'Something went wrong'});
                    });
                } catch (e) {
                    if(DEBUG_MODE) {
                        console.error("Error sending transaction: ", e);
                    }
                    response.send({status:'error',message:'Error in sending transaction.'});
                }
            })
            .catch(err => {
                if(DEBUG_MODE) {
                    console.error("Error signing transaction: ", err);
                }
                response.send({status:'error',message:'Error in signing transaction.'});
            });
    } catch (e) {
        response.send({status:'error',message:'Something went wrong.'});
    }
});

app.listen(port, () => {
    console.log(`Server is running on http://localhost:${port}`);
});
