<?php

namespace Database\Seeders;

use App\Constants\CommonConstants;
use App\Models\Country;
use App\Models\Setting;
use App\Models\TopupSetting;
use App\Models\User;
use App\Models\UserWallet;
use App\Models\Wallet;
use Faker\Provider\UserAgent;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;

class TopupSettingSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $rows=[
            ['counter'=>1,'income_x'=>CommonConstants::DEFAULT_INCOME_X],
            /* ['counter'=>2,'income_x'=>4],
            ['counter'=>3,'income_x'=>5], */
        ];

        foreach ($rows as $row) {
            if(!TopupSetting::query()->where('counter','=',$row['counter'])->first()) {
                TopupSetting::create($row);
            }
        }
    }
}
