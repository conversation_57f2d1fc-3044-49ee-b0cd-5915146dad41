/*! For license information please see component-progressbarjs.js.LICENSE.txt */
"use strict";document.addEventListener("DOMContentLoaded",(function(){var t;new ProgressBar.Circle(circleprogressblue1,{color:"#015EC2",strokeWidth:10,trailWidth:10,easing:"easeInOut",trailColor:"rgba(66, 157, 255, 0.15)",duration:1400,text:{autoStyleContainer:!1},from:{color:"#015EC2",width:10},to:{color:"#015EC2",width:10},step:function(t,e){e.path.setAttribute("stroke",t.color),e.path.setAttribute("stroke-width",t.width);var o=Math.round(100*e.value());0===o?e.setText(""):e.setText(o+"<small>%<small>")}}).animate(.65),(t=new ProgressBar.Line(containerLine,{strokeWidth:3,easing:"easeInOut",duration:1400,color:"#2196f3",trailColor:"#eee",trailWidth:1,svgStyle:{width:"100%",height:"100%"},text:{style:{color:"#999",position:"absolute",right:"0",top:"30px",padding:0,margin:0,transform:null},autoStyleContainer:!1},from:{color:"#2196f3"},to:{color:"#ffc107"},step:(t,e)=>{e.setText(Math.round(100*e.value())+" %")}})).animate(.85),(t=new ProgressBar.Path("#heart-path",{easing:"easeInOut",duration:1400})).set(0),t.animate(1),(t=new ProgressBar.SemiCircle(halfcircleprogress,{strokeWidth:6,color:"#ffc107",trailColor:"#DDDDDD",trailWidth:4,easing:"easeInOut",duration:1400,svgStyle:null,text:{value:"",alignToBottom:!1},from:{color:"#ffc107"},to:{color:"#4caf50"},step:(t,e)=>{e.path.setAttribute("stroke",t.color);var o=Math.round(100*e.value());0===o?e.setText(""):e.setText(o),e.text.style.color=t.color}})).text.style.fontFamily='"Raleway", Helvetica, sans-serif',t.text.style.fontSize="2rem",t.animate(.85)}));