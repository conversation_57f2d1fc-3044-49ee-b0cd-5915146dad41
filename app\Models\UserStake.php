<?php

namespace App\Models;

use App\Base\Model\BaseModel;
use App\Components\Helper;
use App\Components\Web3NodeBackend;
use App\Constants\CommonConstants;
use Illuminate\Support\Facades\DB;

class UserStake extends BaseModel
{
    protected $fillable = [
        'ref_no',
        'is_active','end_date','amount','user_id',
        'reference_id','earnings','time','next_roi',
        'is_admin','is_conditional','comments',
        'tokens','token_price','hash',
        'is_archived','is_saved','investment_counter',
        'created_at','updated_at','wallet_id','roi_end_date',
        'monthly_dividend',
    ];

    public function user()
    {
        return $this->hasOne(User::class, 'id', 'user_id');
    }

    public function generateColorCode() {
        $code=Helper::generateRandColor();
        $check=self::query()->where('user_id','=',$this->user_id)
            ->where('color_code','=',$code)
            ->first();
        if($check) {
            return $this->generateColorCode();
        } else {
            return $code;
        }
    }

    public function wallet()
    {
        return $this->hasOne(Wallet::class, 'id', 'wallet_id');
    }

    public function apiData() {
        return [
            'counter'=>$this->investment_counter,
            'ref_no'=>$this->ref_no,
            'amount'=>$this->amount,
            'formattedAmount'=>Helper::printAmount($this->amount),
            'earnings'=>$this->earnings,
            'formattedEarnings'=>Helper::printAmount($this->earnings),
            'token_price'=>$this->token_price,
            'formatted_token_price'=>Helper::printAmount($this->token_price),
            'tokens'=>$this->tokens,
            'formattedTokens'=>Helper::printNumber($this->tokens,CommonConstants::TOKEN_PRECISION),
            'monthly_dividend'=>$this->monthly_dividend.'%',
            'is_active'=>$this->is_active,
            'start_date'=>$this->time,
            'end_date'=>($this->end_date ? $this->end_date : ''),
            'roi_end_date'=>($this->roi_end_date ? $this->roi_end_date : ''),
            'hash'=>($this->hash ? $this->hash : ''),
            'short_hash'=>($this->hash ? User::shortAddress($this->hash) : ''),
            'color_code'=>$this->fetchColorCode(),
        ];
    }

    public function onCreating()
    {
        if($this->amount>0) {
            $minimumValue=Setting::getValue(Setting::SETTING_MINIMUM_INVESTMENT);
            if($minimumValue>$this->amount) {
                throw new \Exception('Minimum investment amount should be '.$minimumValue);
            }
            $this->next_roi=date(CommonConstants::PHP_DATE_FORMAT_SHORT,strtotime("+1 day"));
            if($this->roi_end_date==null || $this->roi_end_date=="") {
                $stakeDuration = Setting::getValue(Setting::SETTING_STAKE_DURATION);
                $this->roi_end_date = date(CommonConstants::PHP_DATE_FORMAT_SHORT, strtotime("+" . $stakeDuration . " months",strtotime($this->next_roi)));
            }
        }
        if($this->time==null) {
            $this->time=date(CommonConstants::PHP_DATE_FORMAT_SHORT);
        }

        if($this->user_id) {
            $this->color_code=$this->generateColorCode();
        }
        parent::onCreating(); // TODO: Change the autogenerated stub
    }

    public function fetchColorCode() {
        if(!$this->color_code) {
            $this->color_code=$this->generateColorCode();
            if($this->update(['color_code'])) {
                return $this->color_code;
            }
        }
        return $this->color_code;
    }

    public function updateRank($parent,$executiveCondition=null) {
        if($parent) {
            //to avoid same instance or cache
            $parent = User::query()->where('id','=',$parent->id)->limit(1)->first();
            if($parent->status==User::STATUS_ACTIVE && $parent->user_role_id==User::ROLE_USER && $parent->active_investment>0) {
                if(!$executiveCondition) {
                    $executiveCondition=RankSetting::query()->where('id','=',1)->first();
                }
                if($executiveCondition) {
                    if ($parent->total_directs >= 3) {
                        $log = [
                            'user_id' => $parent->id,
                            'rank_id' => null,
                        ];
                        if($parent->rank_id) {
                            $log['rank_id']=$parent->rank_id;
                        }

                        $directs = User::query()->where('referred_by', '=', $parent->id)
                            ->where('status','=',User::STATUS_ACTIVE)
                            ->where('active_investment','>',0)
                            ->orderBy('team_business')->get();
                        if (count($directs) > 0) {
                            $log['directs'] = [];
                            $log['directs_business'] = 0;
                            $virtual_power_leg_id = null;
                            $virtual_power_leg_business = null;
                            foreach ($directs as $direct) {
                                if ($direct->active_investment > 0 && !$direct->is_zero_pin) {
                                    $log['directs'][] = ['id' => $direct->id,'is_power_leg'=>$direct->is_power_leg, 'team_business' => round(($direct->lifetime_investment + $direct->team_business), 2)];
                                    $log['directs_business']= round(($log['directs_business']+$direct->lifetime_investment),CommonConstants::USD_PRECISION);

                                    if($direct->is_power_leg) {
                                        $virtual_power_leg_id=$direct->id;
                                        $virtual_power_leg_business=round(($direct->lifetime_investment + $direct->team_business), 2);
                                    }
                                }
                                if ($direct->active_investment > 0 && $direct->is_zero_pin) {
                                    $log['directs'][] = ['id' => $direct->id,'is_power_leg'=>$direct->is_power_leg, 'team_business' => round(($direct->active_investment + $direct->team_business), 2)];

                                    if($direct->is_power_leg) {
                                        $virtual_power_leg_id=$direct->id;
                                        $virtual_power_leg_business=round(($direct->active_investment + $direct->team_business), 2);
                                    }
                                }
                            }

                            if (count($log['directs']) >= 3) {
                                $power_leg_id = null;
                                $power_leg_business = null;
                                $second_power_leg_id = null;
                                $second_power_leg_business = null;


                                foreach ($log['directs'] as $logDirect) {
                                    if ($power_leg_business === null) {
                                        $power_leg_business = $logDirect['team_business'];
                                        $power_leg_id = $logDirect['id'];
                                    } else if ($second_power_leg_business === null) {
                                        $second_power_leg_business = $logDirect['team_business'];
                                        $second_power_leg_id = $logDirect['id'];
                                    } else {
                                        $business = $logDirect['team_business'];
                                        $business_id = $logDirect['id'];
                                        if ($business > $power_leg_business) {
                                            $second_power_leg_id=$power_leg_id;
                                            $second_power_leg_business=$power_leg_business;
                                            $power_leg_business = $business;
                                            $power_leg_id = $business_id;
                                        } else if ($business > $second_power_leg_business) {
                                            $second_power_leg_business = $business;
                                            $second_power_leg_id = $business_id;
                                        }
                                    }
                                }

                                if($virtual_power_leg_id) {
                                    $second_power_leg_id=$power_leg_id;
                                    $second_power_leg_business=$power_leg_business;

                                    $power_leg_id=$virtual_power_leg_id;
                                    $power_leg_business=$virtual_power_leg_business;
                                }


                                $legStats = ['power' => [],'second_power' => [], 'other' => ['team_business' => 0, 'directs' => []]];
                                foreach ($log['directs'] as $logDirect) {
                                    if ($logDirect['id'] == $power_leg_id) {
                                        $legStats['power'] = $logDirect;
                                    } else if ($logDirect['id'] == $second_power_leg_id) {
                                        $legStats['second_power'] = $logDirect;
                                    } else {
                                        $legStats['other']['team_business'] = round(($legStats['other']['team_business'] + $logDirect['team_business']), CommonConstants::USD_PRECISION);
                                        $legStats['other']['directs'][] = $logDirect;
                                    }
                                }
                                $log['calculations'] = $legStats;

                                if (count($log['calculations']['power']) > 0 && count($log['calculations']['second_power']) > 0 && count($log['calculations']['other']['directs']) > 0) {
                                    if($log['rank_id']) {
                                        $ranks = RankSetting::query()
                                            ->where('id','>',$log['rank_id'])
                                            ->orderBy("total_business")
                                            ->get();
                                    } else {
                                        $ranks = RankSetting::query()
                                            ->orderBy("total_business")->get();
                                    }
                                    if (count($ranks) > 0) {
                                        foreach ($ranks as $rank) {
                                            $isMatched = false;

                                            $needPowerLegBusiness = round((($rank->total_business * 40) / 100), CommonConstants::USD_PRECISION);
                                            $needSecondPowerLegBusiness = round((($rank->total_business * 40) / 100), CommonConstants::USD_PRECISION);
                                            $needOtherLegBusiness = round(($rank->total_business - ($needPowerLegBusiness+$needSecondPowerLegBusiness)), CommonConstants::USD_PRECISION);

                                            $userPowerLegBusiness = round($log['calculations']['power']['team_business'], CommonConstants::USD_PRECISION);
                                            $userSecondPowerLegBusiness = round($log['calculations']['second_power']['team_business'], CommonConstants::USD_PRECISION);
                                            $userOtherLegBusiness = round($log['calculations']['other']['team_business'], CommonConstants::USD_PRECISION);

                                            if($virtual_power_leg_id) {
                                                if($needPowerLegBusiness>$userPowerLegBusiness) {
                                                    $userPowerLegBusiness=$needPowerLegBusiness;
                                                }
                                            }

                                            //if($log['directs_business']>=$rank->self_investment) {
                                            if(true) {
                                                if ($userPowerLegBusiness >= $needPowerLegBusiness && $userSecondPowerLegBusiness >= $needSecondPowerLegBusiness && $userOtherLegBusiness >= $needOtherLegBusiness) {
                                                    $isMatched = true;
                                                }
                                            }

                                            if ($isMatched) {
                                                $log['rank_id'] = $rank->id;
                                            }

                                            $log['stats'] = [
                                                'rank_id' => $rank->id,
                                                'rank_total_business' => $rank->total_business,
                                                'user_direct_investment' => $log['directs_business'],
                                                'user_directs' => count($log['directs']),
                                                'rank_need_power_leg_business' => $needPowerLegBusiness,
                                                'rank_need_second_power_leg_business' => $needSecondPowerLegBusiness,
                                                'rank_need_other_leg_business' => $needOtherLegBusiness,
                                                'user_power_leg_business' => $userPowerLegBusiness,
                                                'user_second_power_leg_business' => $userSecondPowerLegBusiness,
                                                'user_other_leg_business' => $userOtherLegBusiness,
                                                'power_leg_user_id' => $power_leg_id,
                                                'second_power_leg_user_id' => $second_power_leg_id,
                                            ];
                                            if (!$isMatched) {
                                                break;
                                            }
                                        }
                                    }
                                }
                            }
                        }

                        if ($parent->rank_id != $log['rank_id']) {
                            if ($log['rank_id'] === null) {
                                DB::statement("update `users` set `rank_id`=null where `id`='" . $parent->id . "';");
                            } else {
                                DB::statement("update `users` set `rank_id`='" . $log['rank_id'] . "' where `id`='" . $parent->id . "';");
                            }

                            UserRankHistory::addRankHistory($parent->id, $log['rank_id']);
                        }
                        UserRankLog::saveLog($parent->id, $log);
                    }
                }
            }
            if($parent->referred) {
                return $this->updateRank($parent->referred,$executiveCondition);
            }
        }
        return true;
    }

    public function updateOpenedLevels($parent) {
        if($parent) {
            //to avoid same instance or cache
            $parent = User::query()->where('id','=',$parent->id)->limit(1)->first();
            if($parent->status==User::STATUS_ACTIVE && $parent->user_role_id==User::ROLE_USER && $parent->active_investment>0) {
                if ($parent->total_directs >= 1) {
                        $log = [
                            'user_id' => $parent->id,
                            'opened_levels' => 1,
                        ];

                        $directs = User::query()->where('referred_by', '=', $parent->id)
                            ->where('status','=',User::STATUS_ACTIVE)
                            ->where('active_investment','>',0)
                            ->orderBy('team_business')->get();
                        if (count($directs) > 0) {
                            $log['directs'] = [];
                            $log['directs_business'] = 0;
                            $virtual_power_leg_id = null;
                            $virtual_power_leg_business = null;
                            foreach ($directs as $direct) {
                                if ($direct->active_investment > 0 && !$direct->is_zero_pin) {
                                    $log['directs'][] = ['id' => $direct->id,'is_power_leg'=>$direct->is_power_leg, 'team_business' => round(($direct->lifetime_investment + $direct->team_business), 2)];
                                    $log['directs_business']= round(($log['directs_business']+$direct->lifetime_investment),CommonConstants::USD_PRECISION);
                                }
                                if ($direct->active_investment > 0 && $direct->is_zero_pin) {
                                    $log['directs'][] = ['id' => $direct->id,'is_power_leg'=>$direct->is_power_leg, 'team_business' => round(($direct->active_investment + $direct->team_business), 2)];
                                }
                            }

                            if($log['directs_business']>0) {
                                $eligibleLevels=LevelSetting::query()
                                    ->where('directs','<=',count($log['directs']))
                                    ->orderBy("directs")->get();
                                if(count($eligibleLevels)>0) {
                                    foreach ($eligibleLevels as $eligibleLevel) {
                                        if(count($log['directs'])>=$eligibleLevel->directs && $log['directs_business']>=$eligibleLevel->direct_business) {
                                            $log['opened_levels']=$eligibleLevel->opened_levels;
                                        }
                                    }
                                }
                            }
                        }

                        if ($parent->opened_levels != $log['opened_levels']) {
                            DB::statement("update `users` set `opened_levels`='" . $log['opened_levels'] . "' where `id`='" . $parent->id . "';");
                        }
                        UserOpenLevelLog::saveLog($parent->id, $log);
                    }
            }
        }
        return true;
    }
    public function updateParentStats($user=null) {
        if($user==null) {
            $user = $this->user;
        }
        //lock table here
        //$parentIds=$this->user->getParentIds();
        $parentIds=$user->getParentIds($user,null,false);
        if(is_array($parentIds) && count($parentIds)>0) {
            DB::statement("update `users` set `team_business`=`team_business`+'" . $this->amount . "',`current_month_business`=`current_month_business`+'" . $this->amount . "' where `id` in (".implode(",",$parentIds).");");
        }
        //unlock table here

        try {
            $parent=$user->referred;
            if($parent) {
                //to avoid same instance or cache
                $parent = User::query()->where('id','=',$parent->id)->limit(1)->first();
            }
            if(!$parent) {
                throw new \Exception('Parent details not found');
            } else {
                if($parent->status!=User::STATUS_ACTIVE || $parent->user_role_id!=User::ROLE_USER) {
                    throw new \Exception('Parent account status or role not matched');
                }
                if($parent->active_investment<=0) {
                    throw new \Exception('Parent self id is not activated');
                }
            }

            $directs=User::query()
                ->where('referred_by','=',$parent->id)
                ->where('active_investment','>','0')
                ->count();
            if($directs==null) {
                $directs=0;
            }
            if($directs<=0) {
                throw new \Exception('Parent active directs count is zero');
            } else {
                if($directs>$parent->active_directs) {
                    DB::statement("update `users` set `active_directs`='" . $directs . "' where `id`='" . $parent->id . "';");
                }

                if($parent->opened_levels<=0) {
                    $openedLevels = 1;
                    if ($openedLevels > 0 && $openedLevels > $parent->opened_levels) {
                        DB::statement("update `users` set `opened_levels`='" . $openedLevels . "' where `id`='" . $parent->id . "';");
                    } else {
                        //throw new \Exception('Parent opened levels is same');
                    }
                }
            }

        } catch (\Exception $e) {
            Log::insertLog([
                'user_id' => $this->user_id,
                'particulars' => 'Unable to update parent opened levels.',
                'type' => 'update_parent_opened_levels',
                'data' => json_encode([
                    'user_id' => $this->user_id,
                    'user_stake_id' => $this->id,
                    'message' => $e->getMessage(),
                ])
            ]);
        }
        return true;
    }
    public function processReferralIncome($user=null) {
        $referralSettings=ReferralSetting::query()->orderBy("level_id")->get();
        if(count($referralSettings)>0) {
            $parent=null;
            /**
             * @var $user User
             */
            if($user==null) {
                $user = $this->user;
            }
            foreach ($referralSettings as $referralSetting) {
                try {
                    if($referralSetting->level_id===1) {
                        if($parent==null) {
                            $parent=$user->direct;
                        }
                    }
                    /**
                     * @var $parent User
                     */
                    if($parent && $parent->status==User::STATUS_ACTIVE && $parent->user_role_id==User::ROLE_USER) {
                        if($parent->active_investment>0) {
                            if($this->tokens>0) {
                                $bonusTokens=round((($this->tokens*$referralSetting->incentive)/100),CommonConstants::TOKEN_PRECISION);
                                if($bonusTokens>0) {
                                    UserStakeReferralChart::makeChart($parent,$this,$bonusTokens);
                                } else {
                                    throw new \Exception('Bonus tokens not found for referral income');
                                }
                            } else {
                                throw new \Exception('Tokens not found for referral income');
                            }
                        } else {
                            throw new \Exception('Self investment not paid by parent');
                        }
                    } else {
                        throw new \Exception('Invalid parent');
                    }
                } catch (\Exception $e) {
                    Log::insertLog([
                        'user_id' => $this->user_id,
                        'particulars' => 'Stake referral income not processed. Invalid parent.',
                        'type' => 'stake_referral_income',
                        'data' => json_encode([
                            'user_id' => $this->user_id,
                            'user_stake_id' => $this->id,
                            'level_id' => $referralSetting->level_id,
                            'message'=>$e->getMessage(),
                        ])
                    ]);
                }

                if($parent) {
                    if($parent->direct) {
                        $parent=$parent->direct;
                    } else {
                        $parent=null;
                    }
                }
            }
        } else {
            Log::insertLog([
                'user_id' => $this->user_id,
                'particulars' => 'Unable to fetch referral settings.',
                'type' => 'stake_referral_income',
                'data' => json_encode([
                    'user_id' => $this->user_id,
                    'user_stake_id' => $this->id,
                ])
            ]);
        }
        return true;
    }
    public function resetSelfOpenedLevels($user=null) {
        if($user==null) {
            $user = $this->user;
        }
        $parent=$user;
        try {
            if($parent) {
                //to avoid same instance or cache
                $parent = User::query()->where('id','=',$parent->id)->limit(1)->first();
            }
            if(!$parent) {
                throw new \Exception('Parent details not found');
            } else {
                if($parent->status!=User::STATUS_ACTIVE || $parent->user_role_id!=User::ROLE_USER) {
                    throw new \Exception('Parent account status or role not matched');
                }
                if($parent->active_investment<=0) {
                    throw new \Exception('Parent self id is not activated');
                }
            }

            $directs=User::query()
                ->where('referred_by','=',$parent->id)
                ->where('active_investment','>','0')
                ->count();
            if($directs==null) {
                $directs=0;
            }

            if($directs>0) {
                if($directs!=$parent->active_directs) {
                    DB::statement("update `users` set `active_directs`='" . $directs . "' where `id`='" . $parent->id . "';");
                }

                if($parent->opened_levels<=0) {
                    self::updateOpenedLevels($parent);
                }
            }

        } catch (\Exception $e) {
            Log::insertLog([
                'user_id' => $this->user_id,
                'particulars' => 'Unable to update self opened levels.',
                'type' => 'update_self_opened_levels',
                'data' => json_encode([
                    'user_id' => $this->user_id,
                    'user_stake_id' => $this->id,
                    'message' => $e->getMessage(),
                ])
            ]);
        }
        return true;
    }
    public function updateSelfStats($user=null) {
        if($user==null) {
            $user = $this->user;
        }
        $isFirstActivation=false;
        if($user->active_investment<=0) {
            $isFirstActivation=true;
        }
        if($isFirstActivation) {
            $joiningDate=$this->time;
            if(!$joiningDate) {
                $joiningDate=date(CommonConstants::PHP_DATE_FORMAT_SHORT);
            }
            $query="";
            $query.="`joining_date`='".date(CommonConstants::PHP_DATE_FORMAT_SHORT,strtotime($joiningDate))."',";
            $query.="`first_activation`='".$this->amount."',";
            if($this->is_conditional) {
                DB::statement("update `users` set " . $query . "`active_investment`=`active_investment`+'" . $this->amount . "' where `id`='" . $this->user_id . "';");
            } else {
                DB::statement("update `users` set " . $query . "`active_investment`=`active_investment`+'" . $this->amount . "',`lifetime_investment`=`lifetime_investment`+'" . $this->amount . "' where `id`='" . $this->user_id . "';");
            }
        } else {
            if($this->is_conditional) {
                DB::statement("update `users` set `active_investment`=`active_investment`+'" . $this->amount . "' where `id`='" . $this->user_id . "';");
            } else {
                DB::statement("update `users` set `active_investment`=`active_investment`+'" . $this->amount . "',`lifetime_investment`=`lifetime_investment`+'" . $this->amount . "' where `id`='" . $this->user_id . "';");
            }
        }

        if(!$this->is_conditional) {
            /* if($this->investment_counter>0) {
                $incomeX = TopupSetting::query()
                    ->where('counter','<=',$this->investment_counter)
                    ->orderByDesc('counter')->first();
                if($incomeX) {
                    DB::statement("update `users` set `income_x`='" . $incomeX->income_x . "' where `id`='" . $this->user_id . "';");
                }
            } */
        }
        return true;
    }
    public function startProcessing($user=null) {
        if($user==null) {
            $user = $this->user;
        }
        DB::statement("update `user_stakes` set `is_saved`='1' where `id`='" . $this->id . "';");
        $this->updateSelfStats($user);
        if(!$this->is_conditional) {
            UserStakeReleaseChart::makeChart($this);

            $this->resetSelfOpenedLevels($user);

            if ($user->direct) {
                $this->processReferralIncome($user);
            }

            $this->updateParentStats($user);

            if ($user->referred) {
                $this->updateOpenedLevels($user->referred);
                $this->updateRank($user->referred);
            }
        } else {
            $opened_levels=1;
            DB::statement("update `users` set `opened_levels`='".$opened_levels."' where `id`='" . $user->id . "';");
        }
    }
    public static function processNewInvestments($endTime=null) {
        if($endTime==null) {
            $endTime = strtotime("+1 minute") - 10;
        }
        if(time()>=$endTime) {
            return true;
        }

        try {
            $newStakes=self::query()
                ->where('is_saved','=',CommonConstants::NO)
                ->orderBy("id")
                ->limit(rand(10,50))->get();
            if(count($newStakes)>0) {
                foreach ($newStakes as $newStake) {
                    if(time()>=$endTime) {
                        return true;
                    }
                    print "Adding new stake #".$newStake->id.PHP_EOL;
                    $newStake->startProcessing();
                }

                //Save UserDbLogs
                \App\Models\UserDbLog::saveDailyLogs();
            }
        } catch (\Exception $e) {

        }
        return true;
    }

    public static function addNewStake($user,$amount,$reference_id=null,$isAdmin=false,$isConditional=false,$comments=null,$tokenPrice=null,$hash=null) {
        try {
            if($reference_id) {
                if(!$user->is_binary_created) {
                    throw new \Exception('User tree structure not added yet');
                }

                if(!$tokenPrice) {
                    $tokenPrice=Web3NodeBackend::getTokenUSDPrice();
                    if($tokenPrice===false) {
                        throw new \Exception('Token price not fetched');
                    }
                }

                $tokenPrice=round($tokenPrice,CommonConstants::USD_PRECISION);
                $check=self::query()->where(['reference_id'=>$reference_id])->limit(1)->first();
                if($check) {
                    Log::insertLog([
                        'user_id' => $user->id,
                        'particulars' => 'Duplicate stake request prevented',
                        'type' => 'duplicate_stake_request',
                        'data' => json_encode([
                            'user_id' => $user->id,
                            'amount' => $amount,
                            'reference_id' => $reference_id,
                        ])
                    ]);
                    return false;
                }

                $userWallet = UserWallet::fetchUserWallet($user->id, Wallet::WALLET_USD);
                if(!$userWallet) {
                    throw new \Exception('Unable to fetch user wallet');
                } else {
                    if(!$isAdmin) {
                        if ($amount > $userWallet->balance) {
                            throw new \Exception('Insufficient user wallet balance');
                        }
                    }
                }

                $investmentCounter=1;
                $check=UserStake::query()
                    ->where('user_id','=',$user->id)
                    ->orderByDesc('investment_counter')
                    ->limit(1)->first();
                if($check) {
                    $investmentCounter+=$check->investment_counter;
                }

                $model=new UserStake();
                $model->user_id=$user->id;
                $model->amount=$amount;
                $model->reference_id=$reference_id;
                $model->investment_counter=$investmentCounter;
                $model->is_active=CommonConstants::YES;
                $monthlyDividend=round(Setting::getValue(Setting::SETTING_MONTHLY_DIVIDEND),2);
                $model->monthly_dividend=$monthlyDividend;

                $model->wallet_id=Wallet::WALLET_USD;

                $model->time=date(CommonConstants::PHP_DATE_FORMAT_SHORT);

                if($isAdmin) {
                    $model->is_admin=CommonConstants::YES;
                } else {
                    $model->is_admin=CommonConstants::NO;
                }
                if($isConditional) {
                    $model->is_conditional=CommonConstants::YES;
                } else {
                    $model->is_conditional=CommonConstants::NO;
                }
                if($comments) {
                    $model->comments=$comments;
                }
                if($hash) {
                    $model->hash=$hash;
                }

                if($tokenPrice) {
                    $model->token_price=round($tokenPrice,CommonConstants::USD_PRECISION);
                    $model->tokens=round(($amount/$tokenPrice),CommonConstants::TOKEN_PRECISION);
                }

                $stakeDuration = Setting::getValue(Setting::SETTING_STAKE_DURATION);
                $model->roi_end_date = date(CommonConstants::PHP_DATE_FORMAT_SHORT, strtotime("+" . $stakeDuration . " months"));

                if($model->save()) {
                    if(!$model->is_conditional) {
                        $result = $user->addTransaction(
                            'New stake request #' . $model->ref_no,
                            TransactionType::TRANSACTION_TYPE_STAKE,
                            CommonConstants::DEBIT,
                            $amount,
                            $model->wallet_id,
                            json_encode(['user_stake_id' => $model->id]),
                            $model->id
                        );
                    } else {
                        $result=true;
                    }
                    if($result) {
                        return $model;
                    } else {
                        throw new \Exception('Unable to deduct invested funds');
                    }
                } else {
                    throw new \Exception('Unable to add new investment request');
                }
            }
        } catch (\Exception $e) {
            Log::insertLog([
                'user_id' => $user->id,
                'particulars' => 'New stake request error',
                'type' => 'stake_request_error',
                'data' => json_encode([
                    'user_id' => $user->id,
                    'amount' => $amount,
                    'reference_id' => $reference_id,
                    'error' => $e->getMessage(),
                    'trace' => $e->getTraceAsString(),
                ])
            ]);
        }
        return false;
    }
}
