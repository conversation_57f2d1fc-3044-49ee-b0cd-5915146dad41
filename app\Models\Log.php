<?php

namespace App\Models;

use App\Base\Model\BaseModel;
use Illuminate\Database\Eloquent\Model;

class Log extends BaseModel
{
    const USER_PASSWORD_CHANGE = "user_password_change";
    const USER_PASSWORD_RESET = "user_password_reset";
    const USER_PASSWORD_FORGOT = "user_password_forgot";
    const USER_PROFILE_UPDATE = "user_profile_update";
    const USER_ACCOUNT_VERIFIED = "user_account_verified";
    const USER_LOGIN = "user_login";
    const USER_REGISTER = "user_register";
    const USER_DELETED = "user_deleted";

    const SYSTEM_ERROR = 'system_error';
    const JOIN_CONTEST_ERROR = 'join_contest_error';
    const CREATE_TEAM_ERROR = 'create_team_error';
    const UPDATE_TEAM_ERROR = 'update_team_error';
    const GENERATING_ADDRESS_FROM_API_ERROR = 'generating_address_from_api';

    const SYSTEM_USER_DEPOSIT_ERROR = 'user_deposit_error';
    const SYSTEM_IPN_PAYMENT_CREDIT_ERROR = 'ipn_payment_credit_error';

    protected $fillable = [
        'user_id', 'particulars', 'created_ip', 'user_agent', 'type', 'data'
    ];

    protected $attributes = [

    ];

    public function user()
    {
        return $this->hasOne(User::class, 'id', 'user_id');
    }

    public static function insertLog(array $data){
        if(array_key_exists('data', $data)){
            if(is_array($data['data'])){
                $data['data'] = json_encode($data['data']);
            }
        }
        Log::create($data);
    }
}
