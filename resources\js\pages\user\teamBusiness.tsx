import {Head, <PERSON>} from '@inertiajs/react';
import UserLayout from "@/layouts/user/user-layout";
import {<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>} from "@mui/x-charts";
import BreadcrumbsButtons from "@/components/user/breadcrumbButtons";
import {useUserContext} from "@/context/UserContext";
import {useEffect, useState} from "react";
import UserService from "@/services/UserService";
import {YES} from "@/constants/Constants";


export default function MonthlyTeamBusiness() {
    const {
        httpServiceObject,
        copyButton
    } = useUserContext();

    const [teamBusiness, setTeamBusiness] = useState<any>(null);

    const fetchStats = async () => {

        try {
            let stats = await UserService.monthlyTeamBusiness(httpServiceObject);
            setTeamBusiness(stats);

        } catch (error) {
            console.error("Error fetching stats:", error);
        }
    };
    useEffect(() => {
        fetchStats();
    }, [])

    return (
        <UserLayout>

            <main className="adminuiux-content has-sidebar">

                <div className="container mt-4">
                    <div className="row gx-3 align-items-center">
                        <div className="col-12 col-sm">
                            <nav aria-label="breadcrumb" className="mb-2">
                                <ol className="breadcrumb mb-0">
                                    <li className="breadcrumb-item bi"><Link href={route('user.dashboard')}><i
                                        className="bi bi-house-door me-1 fs-14"></i> Dashboard</Link></li>
                                    <li className="breadcrumb-item active bi" aria-current="page">Monthly Team Business</li>
                                </ol>
                            </nav>
                            <h5>Monthly Team Business</h5>
                        </div>
                       {/* <div className="col-12 col-sm-auto text-end py-3 py-sm-0">
                            <BreadcrumbsButtons/>
                        </div>*/}
                    </div>
                </div>

                <div className="container" id="main-content">


                    <div className="card adminuiux-card mt-4 mb-0">
                        <div className="card-body">
                            <div className="table-responsive">
                                <table
                                    className="table">
                                    <thead>
                                    <tr>
                                        <th className="text-center sorting">Month
                                        </th>
                                        <th className="text-center sorting">Total Business
                                        </th>
                                        <th className="text-center sorting">New Business
                                        </th>
                                    </tr>
                                    </thead>
                                    <tbody>

                                    {(teamBusiness && teamBusiness.length > 0 ) ? (
                                        teamBusiness &&
                                        Object.entries(teamBusiness)
                                            .map(([key, item]) => (
                                                <tr className="odd" key={key}>
                                                    <td className="text-center">{item?.month}</td>
                                                    <td className="text-center">
                                                        <span className="badge badge-light text-bg-theme-1">{item?.formatted_team_business}</span>
                                                    </td>
                                                    <td className="text-center">
                                                        <span className="badge badge-light text-bg-theme-1">{item?.formatted_team_business_diff}</span>
                                                    </td>
                                                </tr>
                                            ))
                                    ) : (
                                        <>
                                            <tr className="odd">
                                                <td className="text-center" colSpan={3}>No Data Found</td>
                                            </tr>
                                        </>
                                    )}







                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>


                </div>



            </main>


        </UserLayout>
    );
}
