<?php

namespace App\Components;

use App\Constants\CommonConstants;
use App\Models\User;

class Web3NodeBackend
{
    //public access
    public static function verifyLoginHash($hash)
    {
        $curl = curl_init();

        curl_setopt_array($curl, array(
            CURLOPT_URL => env('VITE_WEB3_API_URL').'/verify-login-hash',
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => '',
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 0,
            CURLOPT_FOLLOWLOCATION => true,
            CURLOPT_SSL_VERIFYPEER => false,
            CURLOPT_SSL_VERIFYHOST => false,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => 'POST',
            CURLOPT_POSTFIELDS => 'hash='.$hash,
            CURLOPT_HTTPHEADER => array(
                'Content-Type: application/x-www-form-urlencoded'
            ),
        ));
        $response = curl_exec($curl);
        curl_close($curl);
        if($response!="") {
            try {
                $response=json_decode($response,true);
                //var_dump($response);
                if(is_array($response) && array_key_exists('status',$response)) {
                    if($response['status']=="success") {
                        return $response['address'];
                    }
                }
            } catch (\Exception $e) {

            }
        }
        return false;
    }
    public static function getTransactionDetails($hash)
    {
        $curl = curl_init();

        curl_setopt_array($curl, array(
            CURLOPT_URL => env('VITE_WEB3_API_URL').'/transaction-details',
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => '',
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 0,
            CURLOPT_FOLLOWLOCATION => true,
            CURLOPT_SSL_VERIFYPEER => false,
            CURLOPT_SSL_VERIFYHOST => false,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => 'POST',
            CURLOPT_POSTFIELDS => 'hash='.$hash,
            CURLOPT_HTTPHEADER => array(
                'Content-Type: application/x-www-form-urlencoded'
            ),
        ));
        $response = curl_exec($curl);
        curl_close($curl);
        if($response!="") {
            try {
                $response=json_decode($response,true);
                if(is_array($response) && array_key_exists('status',$response)) {
                    if($response['status']=="success") {
                        if(array_key_exists('result',$response)) {
                            if(array_key_exists('methodName',$response['result'])) {
                                if($response['result']['methodName']!="") {
                                    return $response['result'];
                                }
                            }
                        }
                    }
                }
            } catch (\Exception $e) {

            }
        }
        return false;
    }
    public static function getTokenUSDPrice()
    {
        $curl = curl_init();

        curl_setopt_array($curl, array(
            CURLOPT_URL => env('VITE_WEB3_API_URL').'/token-usd-price',
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => '',
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 0,
            CURLOPT_FOLLOWLOCATION => true,
            CURLOPT_SSL_VERIFYPEER => false,
            CURLOPT_SSL_VERIFYHOST => false,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => 'POST',
            CURLOPT_POSTFIELDS => '',
            CURLOPT_HTTPHEADER => array(
                'Content-Type: application/x-www-form-urlencoded'
            ),
        ));
        $response = curl_exec($curl);
        curl_close($curl);
        if($response!="") {
            try {
                $response=json_decode($response,true);
                if(is_array($response) && array_key_exists('status',$response)) {
                    if($response['status']=="success") {
                        return $response['price'];
                    }
                }
            } catch (\Exception $e) {

            }
        }
        return false;
    }
    public static function getCurrentBlockNumber()
    {
        $curl = curl_init();

        curl_setopt_array($curl, array(
            CURLOPT_URL => env('VITE_WEB3_API_URL').'/get-current-block-number',
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => '',
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 0,
            CURLOPT_FOLLOWLOCATION => true,
            CURLOPT_SSL_VERIFYPEER => false,
            CURLOPT_SSL_VERIFYHOST => false,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => 'POST',
            CURLOPT_POSTFIELDS => '',
            CURLOPT_HTTPHEADER => array(
                'Content-Type: application/x-www-form-urlencoded'
            ),
        ));
        $response = curl_exec($curl);
        curl_close($curl);
        if($response!="") {
            try {
                $response=json_decode($response,true);
                if(is_array($response) && array_key_exists('status',$response)) {
                    if($response['status']=="success") {
                        return $response['data']['blockNumber'];
                    }
                }
            } catch (\Exception $e) {

            }
        }
        return false;
    }
    public static function getTodayTokensLeft()
    {
        $curl = curl_init();

        curl_setopt_array($curl, array(
            CURLOPT_URL => env('VITE_WEB3_API_URL').'/today-token-left',
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => '',
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 0,
            CURLOPT_FOLLOWLOCATION => true,
            CURLOPT_SSL_VERIFYPEER => false,
            CURLOPT_SSL_VERIFYHOST => false,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => 'POST',
            CURLOPT_POSTFIELDS => '',
            CURLOPT_HTTPHEADER => array(
                'Content-Type: application/x-www-form-urlencoded'
            ),
        ));
        $response = curl_exec($curl);
        curl_close($curl);
        if($response!="") {
            try {
                $response=json_decode($response,true);
                if(is_array($response) && array_key_exists('status',$response)) {
                    if($response['status']=="success") {
                        return $response['amount'];
                    }
                }
            } catch (\Exception $e) {

            }
        }
        return false;
    }

    //private access
    public static function mintToken()
    {
        $curl = curl_init();

        curl_setopt_array($curl, array(
            CURLOPT_URL => env('VITE_WEB3_API_URL').'/daily-mint',
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => '',
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 0,
            CURLOPT_FOLLOWLOCATION => true,
            CURLOPT_SSL_VERIFYPEER => false,
            CURLOPT_SSL_VERIFYHOST => false,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => 'POST',
            CURLOPT_POSTFIELDS => '',
            CURLOPT_HTTPHEADER => array(
                'Content-Type: application/x-www-form-urlencoded'
            ),
        ));
        $response = curl_exec($curl);
        curl_close($curl);
        if($response!="") {
            try {
                $response=json_decode($response,true);
                if(is_array($response) && array_key_exists('status',$response)) {
                    if($response['status']=="success") {
                        return $response['hash'];
                    }
                }
            } catch (\Exception $e) {

            }
        }
        return false;
    }
    public static function registerStake($address,$weiUSDAmount,$weiTokenAmount)
    {
        $curl = curl_init();

        curl_setopt_array($curl, array(
            CURLOPT_URL => env('VITE_WEB3_API_URL').'/register-stake',
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => '',
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 0,
            CURLOPT_FOLLOWLOCATION => true,
            CURLOPT_SSL_VERIFYPEER => false,
            CURLOPT_SSL_VERIFYHOST => false,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => 'POST',
            CURLOPT_POSTFIELDS => 'address='.$address.'&amount='.$weiUSDAmount.'&tokens='.$weiTokenAmount,
            CURLOPT_HTTPHEADER => array(
                'Content-Type: application/x-www-form-urlencoded'
            ),
        ));
        $response = curl_exec($curl);
        curl_close($curl);
        if($response!="") {
            try {
                $response=json_decode($response,true);
                if(is_array($response) && array_key_exists('status',$response)) {
                    if($response['status']=="success") {
                        return $response['hash'];
                    }
                }
            } catch (\Exception $e) {

            }
        }
        return false;
    }
    public static function sendToken($address,$weiTokenAmount)
    {
        $curl = curl_init();

        curl_setopt_array($curl, array(
            CURLOPT_URL => env('VITE_WEB3_API_URL').'/send-token',
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => '',
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 0,
            CURLOPT_FOLLOWLOCATION => true,
            CURLOPT_SSL_VERIFYPEER => false,
            CURLOPT_SSL_VERIFYHOST => false,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => 'POST',
            CURLOPT_POSTFIELDS => 'address='.$address.'&amount='.$weiTokenAmount,
            CURLOPT_HTTPHEADER => array(
                'Content-Type: application/x-www-form-urlencoded'
            ),
        ));
        $response = curl_exec($curl);
        curl_close($curl);
        if($response!="") {
            try {
                $response=json_decode($response,true);
                if(is_array($response) && array_key_exists('status',$response)) {
                    if($response['status']=="success") {
                        return $response['hash'];
                    }
                }
            } catch (\Exception $e) {

            }
        }
        return false;
    }
    public static function sendBlockRewardToken($address,$weiTokenAmount)
    {
        return '0x'.rand(1000,9999).rand(1000,9999).rand(1000,9999).time();
        $curl = curl_init();

        curl_setopt_array($curl, array(
            CURLOPT_URL => env('VITE_WEB3_API_URL').'/send-block-reward-token',
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => '',
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 0,
            CURLOPT_FOLLOWLOCATION => true,
            CURLOPT_SSL_VERIFYPEER => false,
            CURLOPT_SSL_VERIFYHOST => false,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => 'POST',
            CURLOPT_POSTFIELDS => 'address='.$address.'&amount='.$weiTokenAmount,
            CURLOPT_HTTPHEADER => array(
                'Content-Type: application/x-www-form-urlencoded'
            ),
        ));
        $response = curl_exec($curl);
        curl_close($curl);
        if($response!="") {
            try {
                $response=json_decode($response,true);
                if(is_array($response) && array_key_exists('status',$response)) {
                    if($response['status']=="success") {
                        return $response['hash'];
                    }
                }
            } catch (\Exception $e) {

            }
        }
        return false;
    }
}
