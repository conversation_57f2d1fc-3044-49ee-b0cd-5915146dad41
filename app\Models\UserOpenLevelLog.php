<?php

namespace App\Models;

use App\Base\Model\BaseModel;
use App\Constants\CommonConstants;

class UserOpenLevelLog extends BaseModel
{
    protected $fillable = [
        'user_id',
        'data',
        'created_at',
        'updated_at',
    ];

    public function user()
    {
        return $this->hasOne(User::class, 'id', 'user_id');
    }

    public static function saveLog($user_id,$logs) {
        try {
            $model=self::query()->where('user_id','=',$user_id)->limit(1)->first();
            if(!$model) {
                $model=new UserOpenLevelLog();
                $model->user_id=$user_id;
                $model->data=json_encode([]);
                if(!$model->save()) {
                    throw new \Exception('Unable to create log row');
                }
            }

            $model->data=json_encode($logs);
            $model->updated_at=date(CommonConstants::PHP_DATE_FORMAT);
            if($model->update(['data','updated_at'])) {
                return true;
            }
        } catch (\Exception $e) {

        }
        return false;
    }
}
