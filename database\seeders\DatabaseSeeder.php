<?php

namespace Database\Seeders;

use App\Models\User;
// use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     */
    public function run(): void
    {
        // User::factory(10)->create();
        $this->call([
            CountrySeeder::class,
            WalletSeeder::class,
            TransactionTypeSeeder::class,
            UserSeeder::class,
            SettingSeeder::class,
            TopupSettingSeeder::class,
            ReferralSettingSeeder::class,
            LevelSettingSeeder::class,
            RankSettingSeeder::class,
            RoyaltySettingSeeder::class,
        ]);
    }
}
