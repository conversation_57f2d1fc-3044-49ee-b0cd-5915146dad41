import {PlaceholderPattern} from '@/components/ui/placeholder-pattern';
import AppLayout from '@/layouts/app-layout';
import {type BreadcrumbItem} from '@/types';
import {Head, Link} from '@inertiajs/react';
import UserLayout from "@/layouts/user/user-layout";
import {Line<PERSON><PERSON>, <PERSON><PERSON><PERSON>} from "@mui/x-charts";
import BreadcrumbsButtons from "@/components/user/breadcrumbButtons";


export default function DipositHistory() {


    return (
        <UserLayout>

            <main className="adminuiux-content has-sidebar">

                <div className="container mt-4">
                    <div className="row gx-3 align-items-center">
                        <div className="col-12 col-sm">
                            <nav aria-label="breadcrumb" className="mb-2">
                                <ol className="breadcrumb mb-0">
                                    <li className="breadcrumb-item bi"><Link href={route('user.dashboard')}><i
                                        className="bi bi-house-door me-1 fs-14"></i> Dashboard</Link></li>
                                    <li className="breadcrumb-item active bi" aria-current="page">Deposit History</li>
                                </ol>
                            </nav>
                            <h5>Deposit History</h5>
                        </div>
                        <div className="col-12 col-sm-auto text-end py-3 py-sm-0">
                            <BreadcrumbsButtons/>
                        </div>
                    </div>
                </div>

                <div className="container mt-4" id="main-content" data-bs-spy="scroll" data-bs-target="#list-example"
                     data-bs-smooth-scroll="true">

                    <div className="row" id="list-item-2">

                        <div className="col-12 mb-2">
                            <div className="card adminuiux-card mb-3">
                                <div className="card-body">
                                    <div className="row align-items-center">
                                        <div className="col-12 col-sm-9 col-xxl mb-3 mb-xxl-0">
                                            <div className="row align-items-center">
                                                <div className="col">
                                                    <h5>Guaranteed Return Plan</h5>
                                                    <span
                                                        className="badge badge-light text-bg-theme-1">5 Year Payment</span>
                                                    <span
                                                        className="badge badge-light text-bg-success mx-1">Pay Monthly</span>
                                                </div>
                                            </div>
                                        </div>
                                        <div className="col-12 col-sm-3 col-xxl-auto mb-3 mb-sm-0">
                                            <h5>$ 6.0 L</h5>
                                            <p className="text-secondary small">You will give</p>
                                        </div>
                                        <div className="col-12 col-md-9 col-xxl-4 mb-3 mb-md-0">
                                            <div className="card">
                                                <div className="card-body">
                                                    <div className="row align-items-center justify-content-between">
                                                        <div className="col-auto text-start">
                                                            <h5 className="mb-1">7.2%
                                                                <small>
                                                                    <span
                                                                        className="badge badge-sm badge-light text-bg-success mx-1 fw-normal">Tax Free</span>
                                                                </small>
                                                            </h5>
                                                            <p className="text-secondary small">You get</p>
                                                        </div>
                                                        <div className="col-auto">
                                                            <i className="bi bi-plus-lg"></i>
                                                        </div>
                                                        <div className="col-auto text-end">
                                                            <h5>$ 10.0 L</h5>
                                                            <p className="text-secondary small">in 10 years</p>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div className="col-auto">
                                            <button className="btn btn-outline-theme">Get Details</button>
                                        </div>
                                    </div>
                                </div>
                            </div>


                        </div>
                        <div className="col-12 mb-2">
                            <div className="card adminuiux-card mb-3">
                                <div className="card-body">
                                    <div className="row align-items-center">
                                        <div className="col-12 col-sm-9 col-xxl mb-3 mb-xxl-0">
                                            <div className="row align-items-center">
                                                <div className="col">
                                                    <h5>Guaranteed Return Plan</h5>
                                                    <span
                                                        className="badge badge-light text-bg-theme-1">5 Year Payment</span>
                                                    <span
                                                        className="badge badge-light text-bg-success mx-1">Pay Monthly</span>
                                                </div>
                                            </div>
                                        </div>
                                        <div className="col-12 col-sm-3 col-xxl-auto mb-3 mb-sm-0">
                                            <h5>$ 6.0 L</h5>
                                            <p className="text-secondary small">You will give</p>
                                        </div>
                                        <div className="col-12 col-md-9 col-xxl-4 mb-3 mb-md-0">
                                            <div className="card">
                                                <div className="card-body">
                                                    <div className="row align-items-center justify-content-between">
                                                        <div className="col-auto text-start">
                                                            <h5 className="mb-1">7.2%
                                                                <small>
                                                                    <span
                                                                        className="badge badge-sm badge-light text-bg-success mx-1 fw-normal">Tax Free</span>
                                                                </small>
                                                            </h5>
                                                            <p className="text-secondary small">You get</p>
                                                        </div>
                                                        <div className="col-auto">
                                                            <i className="bi bi-plus-lg"></i>
                                                        </div>
                                                        <div className="col-auto text-end">
                                                            <h5>$ 10.0 L</h5>
                                                            <p className="text-secondary small">in 10 years</p>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div className="col-auto">
                                            <button className="btn btn-outline-theme">Get Details</button>
                                        </div>
                                    </div>
                                </div>
                            </div>


                        </div>
                        <div className="col-12 mb-2">
                            <div className="card adminuiux-card mb-3">
                                <div className="card-body">
                                    <div className="row align-items-center">
                                        <div className="col-12 col-sm-9 col-xxl mb-3 mb-xxl-0">
                                            <div className="row align-items-center">
                                                <div className="col">
                                                    <h5>Guaranteed Return Plan</h5>
                                                    <span
                                                        className="badge badge-light text-bg-theme-1">5 Year Payment</span>
                                                    <span
                                                        className="badge badge-light text-bg-success mx-1">Pay Monthly</span>
                                                </div>
                                            </div>
                                        </div>
                                        <div className="col-12 col-sm-3 col-xxl-auto mb-3 mb-sm-0">
                                            <h5>$ 6.0 L</h5>
                                            <p className="text-secondary small">You will give</p>
                                        </div>
                                        <div className="col-12 col-md-9 col-xxl-4 mb-3 mb-md-0">
                                            <div className="card">
                                                <div className="card-body">
                                                    <div className="row align-items-center justify-content-between">
                                                        <div className="col-auto text-start">
                                                            <h5 className="mb-1">7.2%
                                                                <small>
                                                                    <span
                                                                        className="badge badge-sm badge-light text-bg-success mx-1 fw-normal">Tax Free</span>
                                                                </small>
                                                            </h5>
                                                            <p className="text-secondary small">You get</p>
                                                        </div>
                                                        <div className="col-auto">
                                                            <i className="bi bi-plus-lg"></i>
                                                        </div>
                                                        <div className="col-auto text-end">
                                                            <h5>$ 10.0 L</h5>
                                                            <p className="text-secondary small">in 10 years</p>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div className="col-auto">
                                            <button className="btn btn-outline-theme">Get Details</button>
                                        </div>
                                    </div>
                                </div>
                            </div>


                        </div>
                        <div className="col-12 mb-2">
                            <div className="card adminuiux-card mb-3">
                                <div className="card-body">
                                    <div className="row align-items-center">
                                        <div className="col-12 col-sm-9 col-xxl mb-3 mb-xxl-0">
                                            <div className="row align-items-center">
                                                <div className="col">
                                                    <h5>Guaranteed Return Plan</h5>
                                                    <span
                                                        className="badge badge-light text-bg-theme-1">5 Year Payment</span>
                                                    <span
                                                        className="badge badge-light text-bg-success mx-1">Pay Monthly</span>
                                                </div>
                                            </div>
                                        </div>
                                        <div className="col-12 col-sm-3 col-xxl-auto mb-3 mb-sm-0">
                                            <h5>$ 6.0 L</h5>
                                            <p className="text-secondary small">You will give</p>
                                        </div>
                                        <div className="col-12 col-md-9 col-xxl-4 mb-3 mb-md-0">
                                            <div className="card">
                                                <div className="card-body">
                                                    <div className="row align-items-center justify-content-between">
                                                        <div className="col-auto text-start">
                                                            <h5 className="mb-1">7.2%
                                                                <small>
                                                                    <span
                                                                        className="badge badge-sm badge-light text-bg-success mx-1 fw-normal">Tax Free</span>
                                                                </small>
                                                            </h5>
                                                            <p className="text-secondary small">You get</p>
                                                        </div>
                                                        <div className="col-auto">
                                                            <i className="bi bi-plus-lg"></i>
                                                        </div>
                                                        <div className="col-auto text-end">
                                                            <h5>$ 10.0 L</h5>
                                                            <p className="text-secondary small">in 10 years</p>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div className="col-auto">
                                            <button className="btn btn-outline-theme">Get Details</button>
                                        </div>
                                    </div>
                                </div>
                            </div>


                        </div>
                    </div>

                </div>



            </main>


        </UserLayout>
    );
}
