import {Head, <PERSON>} from '@inertiajs/react';
import UserLayout from "@/layouts/user/user-layout";
import {<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>} from "@mui/x-charts";
import BreadcrumbsButtons from "@/components/user/breadcrumbButtons";
import {UnderConstruction} from "@/components/under-construction";
import {useUserContext} from "@/context/UserContext";
import {useEffect, useState} from "react";
import UserService from "@/services/UserService";
import {z} from "zod";
import {YES} from "@/constants/Constants";


export default function Tickets() {
    const {
        httpServiceObject,
        copyButton,
        showNotification
    } = useUserContext();

    const [tickets, setTickets] = useState<any>(null);
    const [btnLoading, setBtnLoading] = useState(false);
    const [selectedTicket, setSelectedTicket] = useState(null);

    const fetchStats = async () => {

        try {
            let stats = await UserService.getTickets(httpServiceObject);
            setTickets(stats);

        } catch (error) {
            console.error("Error fetching stats:", error);
        }
    };
    useEffect(() => {
        fetchStats();
    }, [])

    const openModal = (item) => {
        let myModal = new bootstrap.Modal(document.getElementById('ticketModal'), {});
        myModal.show();
    }

    const submitForm = async (e: React.FormEvent<HTMLFormElement>) => {
        e.preventDefault();
        setBtnLoading(true);

        const myformdata = new FormData(e.currentTarget);
        let formData = Object.fromEntries(myformdata.entries());

        const userInfoSchema = z.object({
            subject: z.string().min(1, "Please enter subject."),
            message: z.string().min(1, "Please enter message."),
        });

        try {
            userInfoSchema.parse(formData);
            //console.log(myformdata);
            const response = await UserService.createTicket(httpServiceObject, formData);

            if (response) {
                showNotification('Ticket created successfully.', 'success');
                if (document.getElementById('form-subject') != null) {
                    document.getElementById('form-subject').value = "";
                }
                if (document.getElementById('form-message') != null) {
                    document.getElementById('form-message').value = "";
                }
                const existingModal = bootstrap.Modal.getOrCreateInstance(document.getElementById('ticketModal'));
                existingModal.hide();
                fetchStats();
                //console.log(response?.data[0].ref_no, 'ticket number');
                setTimeout(function () {
                    setSelectedTicket(response);
                    //history.push(`/ticket-details/${response?.data[0].ref_no}`);
                }, 500);
            }
        } catch (err) {
            if (err instanceof z.ZodError) {
                showNotification(err.issues[0].message);
            } else {
                showNotification(err);
            }
        } finally {
            setBtnLoading(false);
        }
    };

    const viewTicket = (item) => {
        setSelectedTicket(item);
    }

    const sendMessage = async (e: React.FormEvent<HTMLFormElement>) => {
        e.preventDefault();
        setBtnLoading(true);

        const myformdata = new FormData(e.currentTarget);
        let formData = Object.fromEntries(myformdata.entries());

        const userInfoSchema = z.object({
            message: z.string().min(1, "Please enter your message."),
        });

        try {

            formData.ref_no = (selectedTicket?.ref_no).toString();
            userInfoSchema.parse(formData);
            //console.log(myformdata);
            const response = await UserService.sendMessage(httpServiceObject, formData);
            if(response){
                setSelectedTicket(response);
                if (document.getElementById('custom-msg-input') != null) {
                    document.getElementById('custom-msg-input').value = "";
                }
            }

            await fetchStats();
        } catch (err) {
            if (err instanceof z.ZodError) {
                showNotification(err.issues[0].message);
            } else {
                showNotification(err);
            }
        } finally {
            setBtnLoading(false);
        }
    };


    const closeTicket = async () => {

        try {
            let stats = await UserService.closeTicket(httpServiceObject, selectedTicket?.ref_no);
            if(stats){
                fetchStats();
                setSelectedTicket(null);
                showNotification('Ticket Close Successfully','success');

            }

        } catch (error) {
            console.error("Error fetching stats:", error);
        }
    };


    return (
        <UserLayout>

            <main className="adminuiux-content has-sidebar">

                <div className="container mt-4">
                    <div className="row gx-3 align-items-center">
                        <div className="col-12 col-sm">
                            <nav aria-label="breadcrumb" className="mb-2">
                                <ol className="breadcrumb mb-0">
                                    <li className="breadcrumb-item bi"><Link href={route('user.dashboard')}><i
                                        className="bi bi-house-door me-1 fs-14"></i> Dashboard</Link></li>
                                    <li className="breadcrumb-item active bi" aria-current="page">Tickets</li>
                                </ol>
                            </nav>
                            <h5>Tickets</h5>
                        </div>
                        <div className="col-12 col-sm-auto text-end py-3 py-sm-0">
                            <button onClick={openModal} className="btn btn-outline-theme">
                                Create New Ticket
                            </button>
                        </div>
                    </div>
                </div>


                <div className="container-fluid mt-4" id="main-content">

                </div>


                <div className="container mt-4 tickets-area" id="main-content" data-bs-spy="scroll" data-bs-target="#list-example"
                     data-bs-smooth-scroll="true">

                    <div className="row" id="list-item-2">
                        {(tickets && tickets.length > 0) ?
                            <>
                                <div className="inner-sidebar-wrap">
                                    <div className="inner-sidebar py-0 height-dynamic">
                                        <div className="card adminuiux-card bg-none h-100">
                                            <div className="card-body overflow-y-auto p-0 custom-height-area">
                                                <ul className="list-group adminuiux-list-group list-group-flush chat-list-contacts bg-none rounded-0 ">

                                                    {
                                                        (
                                                            tickets &&
                                                            Object.entries(tickets)
                                                                .map(([key, item]) => (
                                                                    <li className="list-group-item" key={key}
                                                                        onClick={() => viewTicket(item)}>
                                                                        <div className="row">
                                                                            <div
                                                                                className="col-12 align-self-center ps-0">
                                                                                <div className="row g-0">
                                                                                    <div className="col-8">
                                                                                        <p className="text-truncate mb-0">#{item?.ref_no} {
                                                                                            (item?.is_active == YES) ?
                                                                                                <span
                                                                                                    className="badge badge-light text-bg-success mx-1">Active</span> :
                                                                                                <span
                                                                                                    className="badge badge-light text-bg-danger mx-1">Closed</span>
                                                                                        }</p>

                                                                                    </div>
                                                                                    <div className="col-4 text-end">
                                                                                        <small
                                                                                            className="opacity-50 fs-10 mb-1"><i
                                                                                            className="bi bi-check-all text-success"></i> {item?.created_at}
                                                                                        </small>
                                                                                    </div>
                                                                                </div>
                                                                                <p className="opacity-75 small text-truncate">{item?.subject}</p>
                                                                            </div>
                                                                        </div>
                                                                    </li>
                                                                ))

                                                        )
                                                    }


                                                </ul>
                                            </div>
                                        </div>
                                    </div>
                                    <div className="inner-sidebar-content h-100">
                                        <div className="row gx-3 h-100 mx-0">
                                            <div
                                                className="col-12 height-dynamic mb-4 mb-xl-0">

                                                {(selectedTicket !== null) ? <>
                                                        <div className="card adminuiux-card h-100 mb-3">
                                                            <div className="card-header border-bottom">
                                                                <div className="row align-items-center gx-2">
                                                                    <div className="col-auto">
                                                                        <div className="row align-items-center gx-2">

                                                                            <div className="col align-self-center">
                                                                                <h6 className="mb-0">
                                                                                    <span
                                                                                        className="position-relative">{selectedTicket?.subject}</span>
                                                                                </h6>

                                                                            </div>
                                                                        </div>
                                                                    </div>

                                                                    <div className="col"></div>
                                                                    {(selectedTicket?.is_active == YES) &&
                                                                    <div className="col-auto">
                                                                        <button className="btn btn-outline-danger"
                                                                                onClick={closeTicket}>
                                                                            Close Ticket
                                                                        </button>
                                                                    </div>
                                                                    }

                                                                </div>
                                                            </div>

                                                            <div className="card-body overflow-y-auto chat-list">

                                                                {
                                                                    (
                                                                        selectedTicket.messages &&
                                                                        Object.entries(selectedTicket.messages)
                                                                            .map(([key, item]) => (
                                                                                    <div
                                                                                        className={(item?.type == 'user') ? "row no-margin right-chat" : "row no-margin left-chat"}>
                                                                                        <div className="col-12">
                                                                                            <div className="chat-block">
                                                                                                <div className="row">
                                                                                                    <div className="col">
                                                                                                        <p>{item?.message}</p>
                                                                                                    </div>
                                                                                                </div>
                                                                                            </div>
                                                                                        </div>
                                                                                        <div className="col-12 text-end">
                                                                                            <p className="text-secondary small time">
                                                                                                <i
                                                                                                    className="bi bi-check-all text-primary"></i> {item?.created_at}
                                                                                            </p>
                                                                                        </div>
                                                                                    </div>
                                                                                )
                                                                            )
                                                                    )
                                                                }
                                                            </div>
                                                            <div className="card-footer border-top">
                                                                {(selectedTicket?.is_active == YES) &&
                                                                <form onSubmit={sendMessage}>
                                                                    <div className="input-group ">

                                                                        <input id={'custom-msg-input'} name="message"
                                                                               type="text" className="form-control border-0"
                                                                               placeholder="Type your message... "
                                                                               required/>
                                                                        <button type="submit"
                                                                                className="btn btn-square btn-link">
                                                                            {btnLoading ?
                                                                                <i className={'fa fa-spin fa-spinner'}></i> :
                                                                                <i className={'bi bi-send'}></i>}
                                                                        </button>
                                                                    </div>
                                                                </form>
                                                                }

                                                            </div>
                                                        </div>
                                                    </> :
                                                    <div
                                                        className="card adminuiux-card mb-3 d-flex align-items-center justify-content-center"
                                                        style={{height: '150px'}}>
                                                        <div className="card-header ">
                                                            <div className="row align-items-center gx-2">
                                                                <div className="col-auto">
                                                                    <div className="row align-items-center gx-2">

                                                                        <div className="col align-self-center">
                                                                            <h6 className="mb-0">
                                                                                <span className="position-relative">Select a ticket

                                                                                </span>
                                                                            </h6>

                                                                        </div>
                                                                    </div>
                                                                </div>


                                                            </div>
                                                        </div>
                                                    </div>
                                                }


                                            </div>

                                        </div>
                                    </div>
                                </div>
                            </>
                            : (
                                <>
                                    <div className="col-12 mb-2">
                                        <div className="card adminuiux-card mb-3">
                                            <div className="card-body">
                                                <div className="row align-items-center">
                                                    <div className="col-12 col-sm-9 col-xxl mb-3 mb-xxl-0">
                                                        <div className="row align-items-center">
                                                            <div className="col">
                                                                <h5 className={'text-center pt-5 pb-5'}>No Ticket
                                                                    Found.</h5>

                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </>
                            )}
                    </div>
                </div>
            </main>

            <div className="modal fade" id="ticketModal" tabIndex="-1" aria-labelledby="ticketmodalLabel"
                 aria-hidden="true">
                <div className="modal-dialog">
                    <div className="modal-content">
                        <div className="modal-header">
                            <p className="modal-title h5" id="ticketmodalLabel">Create Ticket</p>
                            <button type="button" className="btn-close" data-bs-dismiss="modal"
                                    aria-label="Close"></button>
                        </div>
                        <div className="modal-body">
                            <form onSubmit={submitForm}>

                                <div className="mb-3">
                                    <label className="text-white"><strong>Subject</strong></label>
                                    <input type="text" id={'form-subject'} name="subject" className="form-control" placeholder="Subject"
                                           required/>
                                </div>
                                <div className="mb-3">
                                    <label className="text-white">Message</label>
                                    <textarea name="message" id={'form-message'} rows={5} className="form-control  f-13 h-100"
                                              placeholder="Message" required></textarea>
                                </div>
                                <div className={'text-end'}>
                                    <button type="submit" className="btn btn-theme" disabled={btnLoading}>
                                        {btnLoading ? "Loading..." : "Create"}
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>


        </UserLayout>
    );
}
