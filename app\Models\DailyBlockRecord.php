<?php

namespace App\Models;

use App\Base\Model\BaseModel;
use App\Components\Helper;
use App\Components\Web3NodeBackend;
use App\Constants\CommonConstants;
use Illuminate\Database\Eloquent\Model;

class DailyBlockRecord extends BaseModel
{
    protected $fillable = [
        'start_block',
        'end_block',
        'is_completed',
        'total',
        'txn_hash',
        'created_at',
        'updated_at',
    ];

    public function transferBalance() {
        try {
            if($this->is_completed && $this->total && $this->total>0 && !$this->txn_hash) {
                $tokens=Helper::toWei($this->total*3);
                $transferHash = Web3NodeBackend::sendBlockRewardToken('',$tokens);
                if ($transferHash) {
                    $this->updated_at=date(CommonConstants::PHP_DATE_FORMAT);
                    $this->txn_hash=$transferHash;
                    if($this->update(['txn_hash','updated_at'])) {
                        return true;
                    } else {
                        throw new \Exception('Transaction hash not updated');
                    }
                } else {
                    throw new \Exception('Transfer not sent properly');
                }
            } else {
                Log::insertLog([
                    'user_id' => CommonConstants::ADMINISTRATIVE,
                    'particulars' => 'Transfer balance not called - '.$this->id,
                    'type' => 'transfer_balance_called_error',
                    'data' => [
                        'is_completed' => $this->is_completed,
                        'total' => $this->total,
                        'txn_hash' => $this->txn_hash,
                    ]
                ]);
            }
        } catch (\Exception $e) {
            Log::insertLog([
                'user_id' => CommonConstants::ADMINISTRATIVE,
                'particulars' => 'Transfer balance not worked - '.$this->id,
                'type' => 'transfer_balance_error',
                'data' => [
                    'message' => $e->getMessage(),
                ]
            ]);
        }
        return null;
    }

    public static function updateRecord() {
        try {
            $blockNumber = Web3NodeBackend::getCurrentBlockNumber();
            if ($blockNumber) {
                $pending = self::query()->where('is_completed', '=', CommonConstants::NO)
                    ->limit(1)->first();
                if ($pending) {
                    $pending->end_block = $blockNumber;
                    $pending->is_completed = CommonConstants::YES;
                    $pending->updated_at=date(CommonConstants::PHP_DATE_FORMAT);
                    $pending->total=($pending->end_block-$pending->start_block);
                    if($pending->update(['end_block','is_completed','total','updated_at'])) {
                        $model = new DailyBlockRecord();
                        $model->start_block = ($blockNumber+1);
                        if (!$model->save()) {
                            throw new \Exception('Unable to save '.$blockNumber);
                        }
                        $pending->transferBalance();
                        return $pending;
                    } else {
                        throw new \Exception('Unable to update '.$blockNumber);
                    }
                } else {
                    $model = new DailyBlockRecord();
                    $model->start_block = $blockNumber;
                    if (!$model->save()) {
                        throw new \Exception('Unable to save '.$blockNumber);
                    }
                    return $model;
                }
            } else {
                Log::insertLog([
                    'user_id' => CommonConstants::ADMINISTRATIVE,
                    'particulars' => 'Block number not fetched',
                    'type' => 'block_number_error',
                    'data' => [
                        'message' => 'Unable to fetch and update block number',
                    ]
                ]);
            }
        } catch (\Exception $e) {
            Log::insertLog([
                'user_id' => CommonConstants::ADMINISTRATIVE,
                'particulars' => 'Block number not fetched',
                'type' => 'block_number_error',
                'data' => [
                    'message' => $e->getMessage(),
                ]
            ]);
        }
        return null;
    }
}
