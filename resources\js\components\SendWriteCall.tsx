import { useWriteContract } from "wagmi";
import {useEffect} from "react";

export function SendWriteCall({onCompleted,txData}) {
    const { writeContract,data: hash, isPending, isSuccess, error } = useWriteContract();
    const SendTransaction = (txData) => {
        console.log("SendTransaction called");
        writeContract(txData);
    }

    useEffect(()=>{
        if(isSuccess){
            onCompleted(hash);
        }
        if(error) {
            console.log("error is ",error);
            onCompleted(false);
        }
    },[isSuccess,error]);
    useEffect(()=>{
        SendTransaction(txData);
    },[]);

    return (<></>);
}
