<?php

namespace App\Models;

use App\Base\Model\BaseModel;
use Illuminate\Database\Eloquent\Model;

class UserTicket extends BaseModel
{
    protected $fillable = [
        'user_id',
        'ref_no',
        'subject',
        'is_active',
        'created_at',
        'updated_at',
    ];

    public function onCreating()
    {
        if($this->subject!="") {
            $this->subject=trim(addslashes(strip_tags($this->subject)));
        }
        parent::onCreating(); // TODO: Change the autogenerated stub
    }

    public function user()
    {
        return $this->hasOne(User::class, 'id', 'user_id');
    }

    public function apiData() {
        return [
            'ref_no'=>$this->ref_no,
            'subject'=>$this->subject,
            'is_active'=>$this->is_active,
            'created_at'=>$this->created_at,
            'messages'=>$this->getRecentMessages(),
        ];
    }

    public function getRecentMessages() {
        $messages=[];
        $recentMessages=UserTicketMessage::query()->where('user_ticket_id','=',$this->id)
            ->orderByDesc("id")->limit(50)->get();
        if(count($recentMessages)>0) {
            foreach ($recentMessages as $recentMessage) {
                $messages[]=$recentMessage->apiData($this->user_id);
            }
        }
        return $messages;
    }
}
