<?php

namespace App\Models;

use App\Base\Model\BaseModel;
use App\Components\Helper;
use App\Constants\CommonConstants;
use Illuminate\Support\Facades\DB;

class RoiLog extends BaseModel
{
    protected $fillable = ['date','logs','created_at','updated_at'];

    public static function sendRoi($startDate=null) {
        if(!$startDate) {
            $startDate=time();
        }
        $check=self::query()->where('date','=',date(CommonConstants::PHP_DATE_FORMAT_SHORT,$startDate))->limit(1)->first();
        if($check) {
            print "ROI already sent for ".date(CommonConstants::PHP_DATE_FORMAT_SHORT,$startDate).PHP_EOL;
            die("completed");
        }

        $model=new RoiLog();
        $model->date=date(CommonConstants::PHP_DATE_FORMAT_SHORT,$startDate);
        $model->logs=json_encode([]);
        if($model->save()) {
            self::processRoi($model->date,$model);
            die("success");
        } else {
            print "Unable to call send ROI";
            return false;
        }
    }

    public static function processRoi($date,$logModel) {
        DB::beginTransaction();
        try {

            $raw_stats=['batches'=>[],'users'=>[],'monthly_roi'=>[]];
            $userStakes=UserStake::query()
                ->where('is_active','=',CommonConstants::YES)
                ->where('next_roi','<=',$date)
                ->orderBy("id")->get();
            if(count($userStakes)>0) {
                $batch=0;
                $counter=0;

                foreach ($userStakes as $userStake) {
                    if($userStake->is_conditional) {
                        continue;
                    }
                    if($counter>=25) {
                        $batch++;
                        $counter=0;
                    }
                    if(!array_key_exists($batch,$raw_stats['batches'])) {
                        $raw_stats['batches'][$batch]=[];
                    }
                    if(!array_key_exists('stakes',$raw_stats['batches'][$batch])) {
                        $raw_stats['batches'][$batch]['stakes']=[];
                    }
                    if(!array_key_exists('stakes_ids',$raw_stats['batches'][$batch])) {
                        $raw_stats['batches'][$batch]['stakes_ids']=[];
                    }
                    if(!array_key_exists('users',$raw_stats['batches'][$batch])) {
                        $raw_stats['batches'][$batch]['users']=[];
                    }

                    //if($dailyRoi>0) {
                    if(true) {

                        $user_id=$userStake->user_id;
                        if(!array_key_exists($user_id,$raw_stats['users'])) {
                            $userWallet=UserWallet::fetchUserWallet($user_id,Wallet::WALLET_DIVIDEND);
                            $raw_stats['users'][$user_id]['wallet_id']=$userWallet->id;
                            $raw_stats['users'][$user_id]['balance']=0;
                            $raw_stats['users'][$user_id]['opened_levels']=$userStake->user->opened_levels;
                            $raw_stats['users'][$user_id]['id']=$user_id;
                            $raw_stats['users'][$user_id]['active_investment']=$userStake->user->active_investment;
                            $raw_stats['users'][$user_id]['total_earnings']=$userStake->user->total_earnings;
                            $raw_stats['users'][$user_id]['income_x']=$userStake->user->income_x;
                            $raw_stats['users'][$user_id]['parent_id']=$userStake->user->referred_by;
                        }
                        if(!array_key_exists($user_id,$raw_stats['batches'][$batch]['users'])) {
                            $raw_stats['batches'][$batch]['users'][$user_id]=$raw_stats['users'][$user_id];
                        }

                        $wallet_id=$raw_stats['batches'][$batch]['users'][$user_id]['wallet_id'];
                        $data = [
                            'id' => $userStake->id,
                            'ref_no' => $userStake->ref_no,
                            'user_id'=>$user_id,
                            'wallet_id'=>$wallet_id,
                        ];
                        //daily calculation

                        //$roiPer = $dailyRoi;
                        $roiPer = $userStake->monthly_dividend;
                        $roi = round((($userStake->amount * $roiPer) / 100), CommonConstants::USD_PRECISION);
                        $roi = round(($roi/date("t")),CommonConstants::USD_PRECISION);

                        //check earning is greater than X
                        $roi = Helper::fetchUpdatedAmount($roi,$raw_stats['users'][$user_id]['income_x'],$raw_stats['users'][$user_id]['active_investment'],$raw_stats['users'][$user_id]['total_earnings']);
                        if($roi>0) {
                            $userTodayEarnings=round($raw_stats['users'][$user_id]['balance']+$roi,CommonConstants::USD_PRECISION);
                            $userMaxEarnings = Helper::fetchUpdatedAmount($userTodayEarnings,$raw_stats['users'][$user_id]['income_x'],$raw_stats['users'][$user_id]['active_investment'],$raw_stats['users'][$user_id]['total_earnings']);
                            if($userMaxEarnings<=0) {
                                $data['roi_reset']=$roi;
                                $roi=0;
                            }
                        }

                        $data['roi_per']=$roiPer;
                        $data['amount']=$roi;
                        $data['before_balance']=$raw_stats['batches'][$batch]['users'][$user_id]['balance'];
                        $data['after_balance']=round(($data['amount']+$data['before_balance']),CommonConstants::USD_PRECISION);

                        $raw_stats['users'][$user_id]['balance']=round(($raw_stats['users'][$user_id]['balance']+$data['amount']),CommonConstants::USD_PRECISION);
                        $raw_stats['batches'][$batch]['users'][$user_id]['balance']=$raw_stats['users'][$user_id]['balance'];

                        $raw_stats['batches'][$batch]['stakes'][] = $data;
                        $raw_stats['batches'][$batch]['stakes_ids'][] = $data['id'];
                    }

                    $counter++;
                }
            }

            Log::insertLog([
                'user_id' => null,
                'particulars' => $date.' raw calculations',
                'type' => 'daily_roi_stats',
                'data' => json_encode([
                    'date' => $date,
                    'message' => $raw_stats,
                ])
            ]);
            //print_r($raw_stats);die;
            if(count($raw_stats['batches'])>0) {
                foreach ($raw_stats['batches'] as $key=>$batch) {
                    if(count($batch['stakes'])>0) {
                        $transactionQuery="INSERT INTO `transactions` (`ref_no`, `type_id`, `user_id`, `wallet_id`, `user_wallet_id`, `particulars`, `is_debit`, `amount`, `before_balance`, `after_balance`, `data`, `reference_id`, `time`, `created_at`, `updated_at`) VALUES ";
                        $txnTime=Transaction::generateReferenceNumber(10,false,strtotime($date));
                        $txnCounter=1;
                        foreach ($batch['stakes'] as $stake) {
                            $transactionQuery.="(".($txnTime.$key.$txnCounter).", ".TransactionType::TRANSACTION_TYPE_DIVIDEND.", ".$stake['user_id'].", ".Wallet::WALLET_DIVIDEND.", ".$stake['wallet_id'].", 'Dividend received against #".$stake['ref_no']."', 0, '".$stake['amount']."', '".$stake['before_balance']."', '".$stake['after_balance']."', '".json_encode(['date'=>$date,'stake_id'=>$stake['id'],'ref_no'=>$stake['ref_no']])."', '".$date.'-'.$stake['id']."','".date(CommonConstants::PHP_DATE_FORMAT)."', '".date(CommonConstants::PHP_DATE_FORMAT)."', '".date(CommonConstants::PHP_DATE_FORMAT)."'),";
                            $txnCounter++;
                        }
                        $transactionQuery=rtrim($transactionQuery,",");
                        $transactionQuery.=";";
                        DB::statement($transactionQuery);

                        Log::insertLog([
                            'user_id' => null,
                            'particulars' => $date.' batch '.$key." transaction sql",
                            'type' => 'daily_roi_transaction_query',
                            'data' => json_encode([
                                'date' => $date,
                                'message' => $transactionQuery,
                            ])
                        ]);

                        $updateEarningsQuery="UPDATE `user_stakes` SET `earnings` = `earnings` + CASE ";
                        foreach ($batch['stakes'] as $stake) {
                            $updateEarningsQuery .= " WHEN `id` = '" . $stake['id'] . "' THEN " . $stake['amount'];
                        }
                        $updateEarningsQuery .= " ELSE 0 END;";
                        DB::statement($updateEarningsQuery);

                        Log::insertLog([
                            'user_id' => null,
                            'particulars' => $date." stake earning sql",
                            'type' => 'daily_roi_earning_query',
                            'data' => json_encode([
                                'date' => $date,
                                'message' => $updateEarningsQuery,
                            ])
                        ]);

                        $stakeQuery="update `user_stakes` set `next_roi`='".date(CommonConstants::PHP_DATE_FORMAT_SHORT,strtotime("+1 day",strtotime($date)))."' where id in (".implode(",",$batch['stakes_ids']).");";
                        DB::statement($stakeQuery);

                        Log::insertLog([
                            'user_id' => null,
                            'particulars' => $date.' batch '.$key." stake sql",
                            'type' => 'daily_roi_stake_query',
                            'data' => json_encode([
                                'date' => $date,
                                'message' => $stakeQuery,
                            ])
                        ]);

                        //print $transactionQuery.PHP_EOL.PHP_EOL.$updateQuery.PHP_EOL.PHP_EOL;
                        sleep(1);
                    }
                }
            }

            if(count($raw_stats['users'])>0) {
                $updateQuery="UPDATE `user_wallets` SET `balance` = `balance` + CASE ";
                foreach ($raw_stats['users'] as $batchUser) {
                    $updateQuery .= " WHEN `id` = '" . $batchUser['wallet_id'] . "' THEN " . $batchUser['balance'];
                }
                $updateQuery .= " ELSE 0 END;";
                DB::statement($updateQuery);

                Log::insertLog([
                    'user_id' => null,
                    'particulars' => $date." wallet sql",
                    'type' => 'daily_roi_wallet_query',
                    'data' => json_encode([
                        'date' => $date,
                        'message' => $updateQuery,
                    ])
                ]);

                $updateUserEarningQuery="UPDATE `users` SET `total_earnings` = `total_earnings` + CASE ";
                foreach ($raw_stats['users'] as $batchUser) {
                    $updateUserEarningQuery .= " WHEN `id` = '" . $batchUser['id'] . "' THEN " . $batchUser['balance'];
                }
                $updateUserEarningQuery .= " ELSE 0 END;";
                DB::statement($updateUserEarningQuery);

                $updateUserEarningQuery="UPDATE `users` SET `lifetime_earnings` = `lifetime_earnings` + CASE ";
                foreach ($raw_stats['users'] as $batchUser) {
                    $updateUserEarningQuery .= " WHEN `id` = '" . $batchUser['id'] . "' THEN " . $batchUser['balance'];
                }
                $updateUserEarningQuery .= " ELSE 0 END;";
                DB::statement($updateUserEarningQuery);

                Log::insertLog([
                    'user_id' => null,
                    'particulars' => $date." user earning sql",
                    'type' => 'daily_roi_user_earning_query',
                    'data' => json_encode([
                        'date' => $date,
                        'message' => $updateUserEarningQuery,
                    ])
                ]);
            }

            $logModel->logs=json_encode($raw_stats);
            $logModel->update(['logs']);
            DB::commit();
            return true;
        } catch (\Exception $e) {
            DB::rollBack();
            Log::insertLog([
                'user_id' => null,
                'particulars' => 'Unable to process daily roi',
                'type' => 'daily_roi_error',
                'data' => json_encode([
                    'date' => $date,
                    'message' => $e->getMessage(),
                    'trace' => $e->getTraceAsString(),
                ])
            ]);
            return false;
        }
        return false;
    }
}
