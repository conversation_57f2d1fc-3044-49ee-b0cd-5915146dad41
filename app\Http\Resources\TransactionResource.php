<?php

namespace App\Http\Resources;

use App\Components\Helper;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class TransactionResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'created_at'=>$this->created_at,
            'ref_no'=>$this->ref_no,
            'particulars'=>$this->particulars,
            'type'=>$this->type->name,
            'wallet'=>$this->wallet->name,
            'debit_amount'=>($this->is_debit ? Helper::printAmount($this->amount) : ''),
            'credit_amount'=>(!$this->is_debit ? Helper::printAmount($this->amount) : ''),
        ];
    }
}
