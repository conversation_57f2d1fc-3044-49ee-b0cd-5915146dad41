import {createContext, useContext, useEffect, useState} from "react";
import {useAppKit, useAppKitAccount, useAppKitBalance, useAppKitNetwork,useDisconnect, useAppKitProvider} from "@reown/appkit/react";
import HttpService from "@/services/HttpService";
import {useSignMessage,useBalance} from "wagmi";
import Snackbar ,{SnackbarCloseReason} from "@mui/material/Snackbar";
import {toast} from "react-toastify";
import { router } from '@inertiajs/react';
import LocalStorageService from "@/services/LocalStorageService";
import UserService from "@/services/UserService";
import { BrowserProvider, Contract, formatEther, parseEther } from "ethers"
import DXEContractABI from "@/abis/DXEContract.json";
import USDTContractABI from "@/abis/USDTContract.json";
import DXEStakeContractABI from "@/abis/DXEStakeContract.json";
import Web3 from "web3";

const Context = createContext({});

export const UserContextProvider = ({children}) => {
    const web3Instance = new Web3();
    const { walletProvider } = useAppKitProvider("eip155");
    const { signMessageAsync, signMessage } = useSignMessage();
    const [openNotification, setOpenNotification ] = useState(false);
    const [notification, setNotification ] = useState({type:'error',message:'Invalid request'});
    //const { open, close, walletProvider } = useAppKit();
    const { open, close } = useAppKit();
    const { disconnect } = useDisconnect();
    const { address, isConnected, caipAddress, status, embeddedWalletInfo } =
        useAppKitAccount();

    const { fetchBalance } = useAppKitBalance();
    const httpServiceObject = new HttpService();

    const localStorage = new LocalStorageService();

    const { caipNetwork, caipNetworkId, chainId, switchNetwork } = useAppKitNetwork();

    const [userDisconnect,setUserDisconnect] = useState(false);
    const [userRegistration,setUserRegistration] = useState(false);
    const [accountSetup,setAccountSetup] = useState(false);
    const [registerHash,setRegisterHash] = useState<any>(null);
    const [userDetails,setUserDetails] = useState(null);
    const [userToken,setUserToken] = useState(null);
    const [walletBalances, setWalletBalances] = useState<any>(null);
    //const [USDTAddress,setUSDTAddress] = useState('******************************************');
    const [USDTAddress,setUSDTAddress] = useState<string>(null);
    const [DXEAddress,setDXEAddress] = useState<string>(null);
    const [DXEStakeAddress,setDXEStakeAddress] = useState(import.meta.env.VITE_STAKE_CONTRACT_ADDRESS || '');
    const [USDTContractInstance,setUSDTContractInstance] = useState<any>(null);
    const [DXEContractInstance,setDXEContractInstance] = useState<any>(null);
    const [DXEStakeContractInstance,setDXEStakeContractInstance] = useState<any>(null);
    const [neonixBalance,setNeonixBalance] = useState(0);
    const [usdtBalance,setUsdtBalance] = useState(0);
    const [dxeBalance,setDxeBalance] = useState(0);
    const [dxeUsdPrice,setDxeUsdPrice] = useState(0);

    const handleCloseNotification = (
        event: React.SyntheticEvent | Event,
        reason?: SnackbarCloseReason,
    ) => {
        if (reason === 'clickaway') {
            return;
        }

        setOpenNotification(false);
    };

    const showNotification=function (message,type:'info' | 'success' | 'warning' | 'error' | 'default' = 'error') {
        if(!message) {
            message='Invalid request sent';
        }
        toast(message,{
            type:type,
            position: "bottom-right",
            autoClose: 5000,
            hideProgressBar: false,
            closeOnClick: false,
            pauseOnHover: true,
            draggable: true,
            progress: undefined,
            theme: "dark",
        });
        //setNotification({type,message});
        //setOpenNotification(true);
    }

    const connectWallet = function () {
        if(!isConnected) {
            open({view: "Connect"});
        } else {
            userLogin();
        }
    };
    const disconnectWallet = function () {
        setUserDisconnect(true);
        setUserToken(null);
        setUserDetails(null);
        close();
        if(isConnected) {
            disconnect().then().catch(e=>{});
        }
        //showNotification('Disconnect successfully','success');

        router.visit('/');
    };

    // function to sing a msg
    const handleSignMsg = async () => {
        return new Promise(async (resolve, reject) => {
            try {
                // message to sign
                const msg = "Verify User Request"
                const sig = await signMessageAsync({ message: msg, account: address});
                return resolve(sig);
            } catch (err) {
                return resolve(null);
            }
        });
    }

    const userLoginRegisterRequest = function (data) {
        return new Promise(async (resolve, error) => {
            httpServiceObject.post('/api/login',data).then(result=>{
                console.log("Login result ",result);
                if(result.hasOwnProperty('success')) {
                    switch (result.status) {
                        case "new_user":
                            showNotification(result?.message,'info');
                            setRegisterHash(result?.hash);
                            setUserRegistration(true);
                            break;
                        case "login":
                            setUserDetails(result?.user);
                            setUserToken(result?.token);
                            localStorage.setItem('token',result?.token);
                            showNotification(result?.message,'success');
                            router.visit('/user-dashboard');
                            break;
                        case "account_setup":
                            setUserRegistration(false);
                            setRegisterHash(result?.hash);
                            setAccountSetup(true);
                            showNotification(result?.message,'success');
                            localStorage.setItem('direct','');
                            break;
                    }
                } else {
                    if(result.hasOwnProperty('error')) {
                        showNotification(result?.error);
                    } else {
                        showNotification('Something went wrong. Try later.');
                    }
                }
                return resolve(true);
            }).catch(err=>{
                showNotification('Something went wrong. Try later.');
                return error(err);
            });
        });
    }
    const userLogin = function () {
        setUserRegistration(false);
        setAccountSetup(false);
        setRegisterHash(null);
        handleSignMsg().then(res=>{
            if(!res) {
                showNotification('Unable to Connect Wallet');
            } else {
                userLoginRegisterRequest({hash:res}).then(r=>{

                }).catch(e=>{

                });
            }
        }).catch(err=>{
            showNotification('Unable to Connect Wallet');
        });
        updateWalletBalances();
    };

    const updateContractInstances = function () {
        updateDXEStakeContractInstance();
        updateDXEContractInstance();
        updateUSDTContractInstance();
    }

    const fetchTokenAddress = function () {
        if(isConnected && DXEStakeContractInstance) {
            if (!DXEAddress) {
                DXEStakeContractInstance.getOtherToken().then(res=>{
                    console.log("fetchTokenAddress DXEAddress res",res);
                    setDXEAddress(res);
                }).catch(err=>{
                    console.log("fetchTokenAddress DXEAddress err is ",err);
                });
            }
            if (!USDTAddress) {
                DXEStakeContractInstance.getUSDToken().then(res=>{
                    console.log("fetchTokenAddress USDTAddress res",res);
                    setUSDTAddress(res);
                }).catch(err=>{
                    console.log("fetchTokenAddress USDTAddress err is ",err);
                });
            }
        }
        return false;
    }

    useEffect(()=>{
        if(isConnected && DXEStakeContractInstance) {
            if(!DXEAddress || !USDTAddress) {
                fetchTokenAddress();
            } else {
                if (DXEAddress) {
                    updateDXEContractInstance();
                }
                if (USDTAddress) {
                    updateUSDTContractInstance();
                }
            }
        }
    },[isConnected,DXEStakeContractInstance,DXEAddress,USDTAddress]);

    const updateDXEContractInstance=function () {
        console.log("updateDXEContractInstance called");
        setDXEContractInstance(null);

        if (!walletProvider) {
            console.log("updateDXEContractInstance walletProvider not found");
            return;
        }

        if (!DXEAddress) {
            console.log("updateDXEContractInstance DXEAddress not found");
            return;
        }

        const ethersProvider = new BrowserProvider(walletProvider);

        const contract = new Contract(
            DXEAddress, // Contract address
            DXEContractABI,
            ethersProvider
        )
        setDXEContractInstance(contract);
    }

    const updateUSDTContractInstance=function () {
        console.log("updateUSDTContractInstance called");
        setUSDTContractInstance(null);

        if (!walletProvider) {
            console.log("updateUSDTContractInstance walletProvider not found");
            return;
        }

        if (!USDTAddress) {
            console.log("updateUSDTContractInstance USDTAddress not found");
            return;
        }

        const ethersProvider = new BrowserProvider(walletProvider);

        const contract = new Contract(
            USDTAddress, // Contract address
            DXEContractABI,
            ethersProvider
        )
        setUSDTContractInstance(contract);
    }

    const updateDXEStakeContractInstance=function () {
        console.log("updateDXEStakeContractInstance called");
        setDXEStakeContractInstance(null);

        if (!walletProvider) {
            console.log("updateDXEStakeContractInstance walletProvider not found");
            return;
        }

        if(!DXEStakeAddress) {
            console.log("updateDXEStakeContractInstance DXEStakeAddress not found");
            return;
        }

        const ethersProvider = new BrowserProvider(walletProvider);

        const contract = new Contract(
            DXEStakeAddress, // Contract address
            DXEStakeContractABI,
            ethersProvider
        )
        setDXEStakeContractInstance(contract);
    }

    const updateWalletBalances = function () {
        updateNeoNixBlockchainBalance();
        updateUSDTBlockchainBalance();
        updateDXEBlockchainBalance();
        updateDXEPrice();
    }

    const convertToEth = (bigNumberValue) => {
        try {
            const ethAmount = formatEther(bigNumberValue);
            return ethAmount;
        } catch (error) {
            console.error('Error converting to ETH:', error);
            return '0';
        }
    };

    const convertToWei = (bigNumberValue) => {
        try {
            const ethAmount = parseEther(""+bigNumberValue);
            return ethAmount;
        } catch (error) {
            console.error('Error converting to ETH:', error);
            return '0';
        }
    };

    const convertWeiToEth = function (balance) {
        balance = balance.toString();
        let formattedBalance = (parseFloat(convertToEth(balance)).toFixed(6))*1;
        return formattedBalance;
    }

    const convertEthToWei = function (balance) {
        balance = balance.toString();
        let formattedBalance = convertToWei(balance);
        return formattedBalance;
    }

    const updateDXEPrice = function () {
        setDxeUsdPrice(0);
        if(DXEContractInstance) {
            console.log("Fetch DXE USD price called");
            DXEContractInstance.usdPrice().then(res=>{
                console.log("Fetch DXE USD price res",res);
                let usdPrice = convertWeiToEth(res);
                setDxeUsdPrice(usdPrice);
            }).catch(err=>{
                console.log("Fetch DXE USD price err is ",err);
            });
        }
    }

    const updateNeoNixBlockchainBalance = function () {
        console.log("Fetch NeoNix balance called");
        setNeonixBalance(0);
        if(isConnected) {
            console.log("Fetch NeoNix balance");
            fetchBalance().then(res=>{
                let balance = res?.data.balance;
                let formattedBalance = (parseFloat(balance).toFixed(6))*1;
                setNeonixBalance(formattedBalance);
            }).catch(err=>{

            });
        }
    }

    const { data: usdtToken, isLoading: tokenLoading } = useBalance({
        address: address,
        token: USDTAddress // Your token contract address
    })

    const { data: dxeToken, isLoading: tokenLoading1 } = useBalance({
        address: address,
        token: DXEAddress // Your token contract address
    })

    const updateUSDTBlockchainBalance = function () {
        console.log("Fetch USDT balance called");
        setUsdtBalance(0);
        if(isConnected && USDTAddress) {
            console.log("Fetch USDT balance");
            let balance = usdtToken?.formatted;
            let formattedBalance = (parseFloat(balance).toFixed(6))*1;
            setUsdtBalance(formattedBalance);
        } else {
            console.log("USDTAddress not found");
        }
    }

    const updateDXEBlockchainBalance = function () {
        console.log("Fetch DXE balance called");
        setDxeBalance(0);
        if(isConnected && DXEAddress) {
            console.log("Fetch DXE balance");
            let balance = dxeToken?.formatted;
            let formattedBalance = (parseFloat(balance).toFixed(6))*1;
            setDxeBalance(formattedBalance);
        } else {
            console.log("DXEAddress not found");
        }
    }

    useEffect(()=>{
        if(isConnected && !userDetails && !userDisconnect) {
            console.log("Send login user request");
            userLogin();
        }
    },[isConnected,userDetails]);

    useEffect(()=>{
        if(accountSetup) {
            setAccountSetup(false);
            if(registerHash) {
                setTimeout(async function (){
                    await userLoginRegisterRequest({hash: registerHash});
                },6000);
            }
        }
    },[accountSetup]);

    useEffect(()=>{
        updateContractInstances();
    },[walletProvider]);

    const fetchBalanceFromApi = async () =>{
        let details = await UserService.walletBalanceStats(httpServiceObject);
        setWalletBalances(details);
    }

    const copyButton = (textToCopy) => {
        navigator.clipboard.writeText(textToCopy)
            .then(() => {
                showNotification('Copied Successfully', 'success');
            })
            .catch(err => {
                console.error("Failed to copy: ", err);
            });
    }

    return (
        <Context.Provider value={{copyButton,DXEAddress,web3Instance,convertWeiToEth,USDTContractABI,DXEContractABI,DXEStakeContractABI,convertEthToWei,DXEStakeAddress,USDTAddress, dxeBalance,setDxeBalance,dxeUsdPrice,USDTContractInstance,DXEContractInstance,DXEStakeContractInstance,updateWalletBalances, handleSignMsg, setDxeUsdPrice , usdtBalance,setUsdtBalance, neonixBalance,setNeonixBalance,userDetails,address,userToken,setUserToken,httpServiceObject,localStorage,disconnectWallet,accountSetup,setAccountSetup,fetchBalanceFromApi, walletBalances, setWalletBalances, isConnected,connectWallet,userLoginRegisterRequest,openNotification,userRegistration,setUserRegistration,registerHash, setOpenNotification,handleCloseNotification,showNotification,notification, setNotification}}>
            {children}
        </Context.Provider>
    );
}

export const useUserContext = () => {
    return useContext(Context);
}
