<?php

namespace App\Http\Controllers;

use App\Http\Controllers\API\BaseApiController;
use App\Http\Resources\TransactionResource;
use Illuminate\Http\Request;
use App\Models\Transaction;

class UserTransactionController extends BaseApiController
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $identity=$request->user();
        $perPage=$request->input('per_page',20);
        $wallet=$request->input('wallet_id',null);
        $type=$request->input('type_id',null);
        $startDate=$request->input('start_date',null);
        $endDate=$request->input('end_date',null);
        if($startDate=="" || $endDate==""){
            $startDate=null;
            $endDate=null;
        }
        else{
            if(strtotime($startDate) > strtotime($endDate)){
                $endDate=$startDate;
            }
        }

        if(trim($type)=="") {
            $type=null;
        }
        if(trim($wallet)=="") {
            $wallet=null;
        }

        $query=Transaction::query()
            ->where('user_id','=',$identity->id);
        if($wallet) {
            $query->where('wallet_id','=',$wallet);
        }
        if($type) {
            $query->where('type_id','=',$type);
        }
        if($startDate && $endDate) {
            $query->whereBetween('time', [$startDate." 00:00:00", $endDate." 23:59:59"]);
        }

        $rows=$query->orderByDesc('id')
            ->paginate($perPage);
        return $this->sendResponse([
            'data'=>TransactionResource::collection($rows),
            'pagination'=>$this->fetchPaginationStats($rows),
        ]);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        return $this->index($request);
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        return $this->sendError('Unauthorized request 3.');
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        return $this->sendError('Unauthorized request 2.');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        return $this->sendError('Unauthorized request 1.');
    }
}
