<?php

namespace App\Constants;

class CommonConstants
{
    const DISPLAY_DATE_FORMAT = 'm/d/Y';
    const DISPLAY_DATE_TIME_FORMAT = 'm/d/Y h:i A';

    const PHP_DATE_FORMAT = 'Y-m-d H:i:s';
    const PHP_DATE_FORMAT_SHORT = 'Y-m-d';

    const DEFAULT_REFERRAL_CODE = 'system';
    const DEFAULT_USER_ROLE = 2;

    const DEFAULT_INCOME_X = 5;

    const AFTER_LOGIN_REDIRECT_URL = 'dashboard';

    const ADMINISTRATIVE = 1;

    const WEI = '1000000000000000000';
    const CURRENCY_PRECISION = 6;
    const TOKEN_PRECISION = 6;
    const USD_PRECISION = 4;
    const ETH_PRECISION = 8;
    const OTHER_LEG_PERCENTAGE = 20;

    const ALLOWED_EXTENSIONS = ['jpg', 'png', 'jpeg'];

    const CURRENCY_ICON = '$';
    const CURRENCY_CODE = 'USD';
    const TOKEN_CODE = 'DRIXE';
    const TOKEN_CODE_USERNAME = 'DX';

    const DATA_TYPE_STRING ="text";
    const DATA_TYPE_TEXTAREA ="textarea";

    const YES = 1;
    const NO = 0;

    const DEBIT = 1;
    const CREDIT = 0;

    const DEFAULT_COUNTRY = 99;

    const DEPOSIT_EXPIRY_MINUTES = 120;

    const SETTING_SUPPORT_EMAIL = 1;

    const YES_STRING = 'yes';
    const NO_STRING = 'no';

    //for settings
    const ENUM_SETTINGS_TYPE_INPUT = 'input';
    const ENUM_SETTINGS_TYPE_SELECT = 'select';
    const ENUM_SETTINGS_TYPE_TEXTAREA = 'textarea';

    const LIVE_WIRE_THEME = 'bootstrap-4';

    const YES_NO_PROPERTIES = [
        self::YES => ['class' => 'badge badge-success', 'text' => 'Yes'],
        self::NO => ['class' => 'badge badge-danger', 'text' => 'No']
    ];

    const STATUS_ACTIVE = 'active';
    const STATUS_INACTIVE = 'inactive';

    const STATUS_PROPERTIES = [
        self::STATUS_ACTIVE => ['class' => 'badge badge-success', 'text' => 'Active'],
        self::STATUS_INACTIVE => ['class' => 'badge badge-danger', 'text' => 'In-Active']
    ];
}
