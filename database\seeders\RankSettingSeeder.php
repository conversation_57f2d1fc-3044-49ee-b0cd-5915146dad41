<?php

namespace Database\Seeders;

use App\Constants\CommonConstants;
use App\Models\Country;
use App\Models\LevelSetting;
use App\Models\RankSetting;
use App\Models\Setting;
use App\Models\TopupSetting;
use App\Models\User;
use App\Models\UserWallet;
use App\Models\Wallet;
use Faker\Provider\UserAgent;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;

class RankSettingSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $rows=[
            ['id'=>1,'business'=>5000,'incentive'=>100,'name'=>'Explorer'],
            ['id'=>2,'business'=>15000,'incentive'=>500,'name'=>'Achiever'],
            ['id'=>3,'business'=>50000,'incentive'=>1000,'name'=>'Connector'],
            ['id'=>4,'business'=>100000,'incentive'=>2000,'name'=>'Builder'],
            ['id'=>5,'business'=>300000,'incentive'=>5000,'name'=>'Influencer'],
            ['id'=>6,'business'=>600000,'incentive'=>10000,'name'=>'Strategist'],
            ['id'=>7,'business'=>1200000,'incentive'=>25000,'name'=>'Leader'],
            ['id'=>8,'business'=>3000000,'incentive'=>75000,'name'=>'Director'],
            ['id'=>9,'business'=>6000000,'incentive'=>150000,'name'=>'Champion'],
            ['id'=>10,'business'=>12000000,'incentive'=>300000,'name'=>'Elite'],
            ['id'=>11,'business'=>50000000,'incentive'=>1000000,'name'=>'Legend'],
            ['id'=>12,'business'=>100000000,'incentive'=>2000000,'name'=>'Icon'],
        ];

        $totalBusiness=0;
        foreach ($rows as $row) {
            $totalBusiness=round($totalBusiness+$row['business']);
            $row['total_business']=$totalBusiness;
            if(!array_key_exists('name',$row)) {
                $row['name']='Rank '.$row['id'];
            }
            if(!RankSetting::query()->where('id','=',$row['id'])->first()) {
                RankSetting::create($row);
            }
        }
    }
}
