import {type BreadcrumbItem} from '@/types';
import {type PropsWithChildren} from 'react';
import {Head, Link, router} from '@inertiajs/react';
import {useEffect} from "react";
import {useAppKit} from "@reown/appkit/react";
import {useUserContext} from "@/context/UserContext";
import Alert from "@mui/material/Alert";
import Snackbar from "@mui/material/Snackbar";
import {address} from "wagmi/dist/types/hooks/codegen/createUseWatchContractEvent";
import {z} from "zod";
import {useState} from "react";
// import '/frontend/assets/js/javascript.js';

export default function GuestLayout({
                                        children,
                                        breadcrumbs = []
                                    }: PropsWithChildren<{ breadcrumbs?: BreadcrumbItem[] }>) {
    useEffect(() => {
        const script = document.createElement('script');
        script.src = 'frontend/assets/js/javascript.js';
        script.async = true;
        document.body.appendChild(script);

        const script2 = document.createElement('script');
        script2.src = "https://cdn.jsdelivr.net/npm/bootstrap@5.3.6/dist/js/bootstrap.bundle.min.js";
        script2.integrity = "sha384-j1CDi7MgGQ12Z7Qab0qlWQ/Qqz24Gc6BM0thvEMVjHnfYGF0rmFCozFSxQBxwHKO";
        script2.crossOrigin = "anonymous";
        script2.async = true;
        document.body.appendChild(script2);

        return () => {
            document.body.removeChild(script);
            document.body.removeChild(script2);
        };
    }, []);

    const {userDetails,address,httpServiceObject,accountSetup,userToken,setAccountSetup, isConnected,connectWallet,disconnectWallet,openNotification,registerHash,handleCloseNotification,userRegistration,userLoginRegisterRequest,setUserRegistration,showNotification,notification} = useUserContext();

    const [sponsorCode,setSponsorCode] = useState<any>(null);
    const [validSponsorCode,setValidSponsorCode] = useState<any>(null);
    const [sponsorDetails,setSponsorDetails] = useState<any>(null);
    const [directCode,setDirectCode] = useState<any>(null);
    const [validDirectCode,setValidDirectCode] = useState<any>(null);
    const [directDetails,setDirectDetails] = useState<any>(null);

    useEffect(()=>{
        console.log("isConnected guest layout is ",isConnected);

        if(isConnected && address && userRegistration) {
            let myModal = new bootstrap.Modal(document.getElementById('registerModal'), {});
            myModal.show();
        }

        if(isConnected && address && accountSetup) {
            const existingModal = bootstrap.Modal.getOrCreateInstance(document.getElementById('registerModal'));
            existingModal.hide();

            //document.querySelector('.modal-backdrop')?.remove();
        }
    },[isConnected,address,userRegistration,accountSetup])
    useEffect(()=>{
        let userSponsor=localStorage.getItem('sponsor');
        if(userSponsor && userSponsor!='') {
            setSponsorCode(userSponsor);
        }
        let userDirect=localStorage.getItem('direct');
        if(userDirect && userDirect!='') {
            setDirectCode(userDirect);
        }
    },[])

    // Auto-validate sponsor code when sponsorCode changes
    useEffect(()=>{
        if(sponsorCode && sponsorCode!='') {
            // Create a mock event object for validation
            const mockEvent = {
                target: { value: sponsorCode }
            } as React.ChangeEvent<HTMLInputElement>;
            validateSponsorCode(mockEvent);
        }
    },[sponsorCode])

    // Auto-validate direct code when directCode changes
    useEffect(()=>{
        if(directCode && directCode!='') {
            // Create a mock event object for validation
            const mockEvent = {
                target: { value: directCode }
            } as React.ChangeEvent<HTMLInputElement>;
            validateDirectCode(mockEvent);
        }
    },[directCode])

    const validateSponsorCode = function (event: React.ChangeEvent<HTMLInputElement>) {
        setValidSponsorCode(null);
        setSponsorDetails(null);
        let code = event.target.value.trim();
        if(code!="" && code.length>=6) {
            httpServiceObject.post('/api/validate-sponsor', {code}).then(result => {
                //console.log("Validate sponsor result ", result);
                if (result.hasOwnProperty('success')) {
                    localStorage.setItem('sponsor',result?.sponsor.code);
                    setSponsorCode(result?.sponsor.code);
                    setValidSponsorCode(true);
                    setSponsorDetails(result.sponsor);
                } else {
                    setValidSponsorCode(false);
                    if (result.hasOwnProperty('error')) {
                        showNotification(result?.error);
                    } else {
                        showNotification('Something went wrong. Try later.');
                    }
                }
            }).catch(err => {
                setValidSponsorCode(false);
                showNotification('Something went wrong. Try later.');
            });
        }
    }

    const validateDirectCode = function (event: React.ChangeEvent<HTMLInputElement>) {
        setValidDirectCode(null);
        setDirectDetails(null);
        let code = event.target.value.trim();
        if(code!="" && code.length>=6) {
            httpServiceObject.post('/api/validate-sponsor', {code}).then(result => {
                //console.log("Validate direct result ", result);
                if (result.hasOwnProperty('success')) {
                    localStorage.setItem('direct',result?.sponsor.code);
                    setDirectCode(result?.sponsor.code);
                    setValidDirectCode(true);
                    setDirectDetails(result.sponsor);
                } else {
                    setValidDirectCode(false);
                    if (result.hasOwnProperty('error')) {
                        showNotification(result?.error);
                    } else {
                        showNotification('Something went wrong. Try later.');
                    }
                }
            }).catch(err => {
                setValidDirectCode(false);
                showNotification('Something went wrong. Try later.');
            });
        }
    }

    const registerForm = async (e: React.FormEvent<HTMLFormElement>)=>{
        e.preventDefault();

        const myFormData = new FormData(e.currentTarget);

        let formData = Object.fromEntries(myFormData.entries());

        const RegisterSchema = z.object({
            name: z.string().min(1,'Enter a valid name'),
            email: z.string().email('Enter a valid email'),
            sponsor: z.string().min(1,'Enter a valid sponsor code'),
            direct: z.string().optional(),
        });

        try {
            RegisterSchema.parse(formData);
            myFormData.append('hash',registerHash);
            formData = Object.fromEntries(myFormData.entries());
            userLoginRegisterRequest(formData).then(res=>{

            }).catch(e=>{

            });
        } catch (err) {
            if(err instanceof z.ZodError) {
                showNotification(err.issues[0].message);
            } else {
                showNotification("An unexpected error occurred. Try later.");
            }
        }
    }
    return (

        <>
            <Head>
                <title>Drixe</title>
                <link rel="icon" type="image/x-icon" href="frontend/assets/images/favicon.png"/>
                <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.6/dist/css/bootstrap.min.css" rel="stylesheet"
                      integrity="sha384-4Q6Gf2aSP4eDXB8Miphtr37CMZZQ5oXLH2yaXMJ2w8e2ZtHTl7GptT4jmndRuHDT"
                      crossOrigin="anonymous"/>
                <link rel="preconnect" href="https://fonts.googleapis.com"/>
                <link rel="preconnect" href="https://fonts.gstatic.com" crossOrigin/>
                <link
                    href="https://fonts.googleapis.com/css2?family=Barlow:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&display=swap"
                    rel="stylesheet"/>
                <link rel="stylesheet"
                      href="https://cdnjs.cloudflare.com/ajax/libs/line-awesome/1.3.0/line-awesome/css/line-awesome.min.css"
                      integrity="sha512-vebUliqxrVkBy3gucMhClmyQP9On/HAWQdKDXRaAlb/FKuTbxkjPKUyqVOxAcGwFDka79eTF+YXwfke1h3/wfg=="
                      crossOrigin="anonymous" referrerPolicy="no-referrer"/>
                <link rel="stylesheet"
                      href="https://cdnjs.cloudflare.com/ajax/libs/OwlCarousel2/2.3.4/assets/owl.carousel.min.css"/>
                <link rel="stylesheet"
                      href="https://cdnjs.cloudflare.com/ajax/libs/OwlCarousel2/2.3.4/assets/owl.theme.default.min.css"/>
                <link type="text/css" rel="stylesheet" href="frontend/assets/css/style.css"/>


            </Head>

            <Snackbar open={openNotification} autoHideDuration={6000} onClose={handleCloseNotification}>
                <Alert
                    onClose={handleCloseNotification}
                    severity={notification.type}
                    variant="filled"
                    sx={{ width: '100%' }}
                >
                    {notification.message}
                </Alert>
            </Snackbar>

            {(isConnected && address && (userRegistration || accountSetup)) ? (<>
                <div className="modal fade" id="registerModal" tabIndex="-1" aria-labelledby="registerModalLabel"
                     aria-hidden="true">
                    <div className="modal-dialog">
                        <div className="modal-content">
                            <div className="modal-header">
                                <h1 className="modal-title fs-5 w-100 d-block text-center" id="exampleModalLabel">Join Us</h1>
                                <button id={"close-register-box"} type="button" data-bs-dismiss="modal" aria-label="Close"><i className="las la-times text-golden"></i></button>
                            </div>
                            <form onSubmit={registerForm}>
                                <div className="modal-body">
                                    <div>
                                        <div className="row">
                                            <div className="col-md-12">
                                                <label className="form-label">Name</label>
                                                <input required={true} autoComplete={'off'} type="text" name={'name'} className="form-control" placeholder={'Full Name'}/>
                                            </div>
                                            <div className="col-md-12">
                                                <label className="form-label">Email</label>
                                                <input required={true} autoComplete={'off'} type="email" name={'email'} className="form-control" placeholder={'Email'}/>
                                            </div>
                                            <div className="col-md-12">
                                                <label className="form-label">Wallet Address</label>
                                                <input type="text" disabled={true} value={address} className="form-control" placeholder={'Wallet Address'}/>
                                            </div>
                                            <div className="col-md-12">
                                                <label className="form-label">Sponsor Code</label>
                                                <input readOnly={(sponsorCode && directCode) ? true : false} defaultValue={sponsorCode} required={true} onKeyUp={validateSponsorCode} onBlur={validateSponsorCode} autoComplete={'off'} type="text" name={'sponsor'} className="form-control" placeholder={'Sponsor Code'}/>
                                                {(validSponsorCode!==null) ? (
                                                    <>
                                                        {(validSponsorCode) ? (<>
                                                            <strong className={'text-success'}>Sponsor: {sponsorDetails.short_address}</strong>
                                                        </>) : (<>
                                                            <strong className={'text-danger'}>Invalid sponsor code</strong>
                                                        </>)}
                                                    </>
                                                ) : (<></>)}
                                            </div>
                                            {(directCode && directCode!="") ? (<>
                                                <div className="col-md-12">
                                                    <label className="form-label">Direct Code</label>
                                                    <input readOnly={true} defaultValue={directCode} onKeyUp={validateDirectCode} onBlur={validateDirectCode} autoComplete={'off'} type="text" name={'direct'} className="form-control" placeholder={'Direct Code'}/>
                                                    {(validDirectCode!==null) ? (
                                                        <>
                                                            {(validDirectCode) ? (<>
                                                                <strong className={'text-success'}>Direct: {directDetails.short_address}</strong>
                                                            </>) : (<>
                                                                <strong className={'text-danger'}>Invalid direct code</strong>
                                                            </>)}
                                                        </>
                                                    ) : (<></>)}
                                                </div>
                                            </>) : (<></>)}
                                        </div>

                                    </div>
                                </div>
                                <div className="modal-footer text-center d-block">
                                    <button type="submit" className="btn login-btn">Register</button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </>) : (<></>)}

            <canvas data-waves id="wave-canvas"></canvas>
            <div className="main">
                <div className="header">
                    <div className="top-bar">
                        <div className="container-fluid">
                            <div className="row">
                                <div className="col-md-12">
                                    <nav className="navbar navbar-expand-lg">
                                        <div className="container-fluid">
                                            <Link className="navbar-brand" href={route('home')} ><img
                                                src="frontend/assets/images/logo.png"
                                                className="logo"
                                                alt="" /></Link>
                                            <button className="navbar-toggler" type="button" data-bs-toggle="offcanvas"
                                                    data-bs-target="#offcanvasWithBothOptions"
                                                    aria-controls="offcanvasWithBothOptions">
                                                <span className="navbar-toggler-icon"></span>
                                            </button>
                                            <div className="collapse navbar-collapse justify-content-end"
                                                 id="desktop-login-regs-btns">
                                                <div className="d-flex header-btns">
                                                    {/*<button className="btn login-btn"  data-bs-toggle="modal" data-bs-target="#exampleModal">Connect Wallet</button>*/}
                                                    {(!isConnected || !userToken || !userDetails) ? (<>
                                                        <button className="btn login-btn" onClick={connectWallet}>Connect Wallet</button>
                                                    </>) : (<>
                                                        <button className="btn login-btn" onClick={disconnectWallet}>Disconnect</button>
                                                    </>)}
                                                </div>
                                            </div>
                                        </div>
                                    </nav>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div className="sidebar" id="desktop-menu">
                        <div id="menu-icon"></div>
                        <ul id="menu-list">
                            <li><a href="#home" className="active offcanvas-link">Home</a></li>
                            <li><a href="#about" className="offcanvas-link">About Us</a></li>
                            <li><a href="#services" className="offcanvas-link">Our Strengths</a></li>
                            <li><a href="#roadmap" className="offcanvas-link">Project Roadmap</a></li>
                            <li><a href="#sales" className="offcanvas-link">CES Details</a></li>
                            <li><a href="#contact-us" className="offcanvas-link">Contact</a></li>
                            <li><a href="#legal" className="offcanvas-link">Legals & Policies</a></li>
                        </ul>
                    </div>
                    <div className="social-media-icon" id="desktop-social-media-icon">
                        <ul>
                            <li><a href="javascript:void(0)"><i className="lab la-facebook-f"></i></a></li>
                            <li><a href="javascript:void(0)"><i className="lab la-linkedin-in"></i></a></li>
                            <li><a href="javascript:void(0)"><i className="lab la-twitter"></i></a></li>
                            <li><a href="javascript:void(0)"><i className="lab la-youtube"></i></a></li>
                        </ul>
                    </div>
                    <div className="offcanvas offcanvas-start" data-bs-scroll="true" tabIndex="-1"
                         id="offcanvasWithBothOptions"
                         aria-labelledby="offcanvasWithBothOptionsLabel">
                        <div className="offcanvas-header">
                            <img src="frontend/assets/images/logo.png" className="logo" alt=""/>
                            <button type="button" className="btn-close" data-bs-dismiss="offcanvas"
                                    aria-label="Close"></button>
                        </div>
                        <div className="offcanvas-body">
                            <div id="mobile-menu">

                            </div>
                        </div>
                        <div className="offcanvas-footer">
                            <div id="mobile-social-media-icon">

                            </div>
                        </div>
                    </div>
                </div>
                <div className="top-bg-circle-img"></div>

                <div className="page-wrapper scroller" id="scroll-container">
                    {children}
                    <div className="footer">
                        <div className="footer-bg">
                            <img src="frontend/assets/images/footer-bg-img.png" className="img-fluid" alt="footer-bg-img"/>
                        </div>
                    </div>
                </div>
            </div>

            {/*<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.6/dist/js/bootstrap.bundle.min.js"
                    integrity="sha384-j1CDi7MgGQ12Z7Qab0qlWQ/Qqz24Gc6BM0thvEMVjHnfYGF0rmFCozFSxQBxwHKO"
                    crossOrigin="anonymous"></script>*/}
        </>
);
}
