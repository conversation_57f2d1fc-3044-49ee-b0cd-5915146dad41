import {PlaceholderPattern} from '@/components/ui/placeholder-pattern';
import AppLayout from '@/layouts/app-layout';
import {type BreadcrumbItem} from '@/types';
import {Head, Link} from '@inertiajs/react';
import UserLayout from "@/layouts/user/user-layout";
import {Line<PERSON><PERSON>, <PERSON><PERSON><PERSON>} from "@mui/x-charts";
import BreadcrumbsButtons from "@/components/user/breadcrumbButtons";
import {UnderConstruction} from "@/components/under-construction";
import {YES} from "@/constants/Constants";
import {useUserContext} from "@/context/UserContext";


export default function Royalty({plans}) {

    const modifyText = function (a) {
        return a.split(',').map(num => {
            let n = parseInt(num);
            let suffix = 'th';

            // Handle special cases for 11th, 12th, 13th
            if (n % 100 >= 11 && n % 100 <= 13) {
                suffix = 'th';
            } else {
                // Handle 1st, 2nd, 3rd cases
                switch (n % 10) {
                    case 1: suffix = 'st'; break;
                    case 2: suffix = 'nd'; break;
                    case 3: suffix = 'rd'; break;
                    default: suffix = 'th';
                }
            }

            return n + suffix;
        }).join(', ');
    }

    const {
        httpServiceObject,
        copyButton,
        userDetails
    } = useUserContext();
    return (
        <UserLayout>

            <main className="adminuiux-content has-sidebar">

                <div className="container mt-4">
                    <div className="row gx-3 align-items-center">
                        <div className="col-12 col-sm">
                            <nav aria-label="breadcrumb" className="mb-2">
                                <ol className="breadcrumb mb-0">
                                    <li className="breadcrumb-item bi"><Link href={route('user.dashboard')}><i
                                        className="bi bi-house-door me-1 fs-14"></i> Dashboard</Link></li>
                                    <li className="breadcrumb-item active bi" aria-current="page">Royalty</li>
                                </ol>
                            </nav>
                            <h5>Royalty</h5>
                        </div>
                        <div className="col-12 col-sm-auto text-end py-3 py-sm-0">

                        </div>
                    </div>
                </div>

               <div className="container mt-4" id="main-content" data-bs-spy="scroll" data-bs-target="#list-example"
                     data-bs-smooth-scroll="true">

                    <div className="row" id="list-item-2">
                        {(plans && plans.length > 0 ) ? (
                            plans &&
                            Object.entries(plans)
                                .map(([key, item]) => (
                                    <div className="col-12 col-md-3 mb-4" key={key}>
                                        <div className="card adminuiux-card">
                                            <div className="card-body">
                                                <div className="row align-items-center text-center mb-3">
                                                    <div className="col">
                                                        <h5 className="mb-0 fw-medium">{item.rank.name}</h5>
                                                    </div>
                                                </div>

                                                <div className="row">
                                                    <div className="text-center mb-3">
                                                        <h4 className="mb-1 text-center ">
                                                            <span className={(item?.rank_id==userDetails?.rank_id) ? 'avatar avatar-100 bg-theme-1 rounded w-160' : 'avatar avatar-100 bg-theme-1-subtle text-theme-1 rounded w-160'}>{item?.incentive}%</span>
                                                        </h4>
                                                    </div>
                                                </div>

                                                <div className="text-center">
                                                    <button className="btn btn-outline-white text-white border" style={{whiteSpace: 'break-spaces'}}>
                                                        <b className={'fw-bold'}>Release Days: </b>
                                                        {modifyText(item.allowed_days)}
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                ))
                        ) : (
                            <></>
                        )}
                    </div>
                </div>
            </main>


        </UserLayout>
    );
}
