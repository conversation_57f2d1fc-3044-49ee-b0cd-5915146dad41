<?php

namespace App\Models;

use App\Base\Model\BaseModel;
use App\Components\Helper;
use App\Components\Web3NodeBackend;
use App\Constants\CommonConstants;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\DB;

class BlockchainTransaction extends BaseModel
{
    protected $fillable = [
        'user_id', 'hash','ref_no','expiry_time',
        'is_processed','is_correct','method','comments','raw_data',
        'created_at','updated_at',
    ];

    public function onCreating()
    {
        if($this->expiry_time==null) {
            $this->expiry_time=strtotime("+5 minutes");
        }
        parent::onCreating();
    }

    public static function saveNewTransaction($hash,$user_id=null) {
        $model=new BlockchainTransaction();
        $model->hash=$hash;
        if($user_id) {
            $model->user_id=$user_id;
        }
        $check=self::query()
            ->where('hash','=',$hash)
            ->limit(1)->first();
        if($check) {
            return false;
        } else {
            if($model->save()) {
                return $model;
            }
        }
        return false;
    }

    public function user()
    {
        return $this->hasOne(User::class, 'id', 'user_id');
    }

    public static function processTransactions($endTime=null) {
        if($endTime==null) {
            $endTime = strtotime("+1 minute") - 10;
        }
        if(time()>=$endTime) {
            return true;
        }
        $transactions=self::query()
            ->where('is_processed','=',CommonConstants::NO)
            ->orderByRaw("rand()")
            ->limit(10)->get();
        if(count($transactions)>0) {
            $tokenPrice=Web3NodeBackend::getTokenUSDPrice();
            if(!$tokenPrice || $tokenPrice===false) {
                $tokenPrice=null;
            }
            foreach ($transactions as $transaction) {
                if(!$transaction->hash || $transaction->hash=="") {
                    DB::statement("update `blockchain_transactions` set `is_processed`='1',`is_correct`='0',`comments`='Invalid hash' where `id`='" . $transaction->id . "';");
                    continue;
                }
                if(time()>=$endTime) {
                    return true;
                }
                print "Start processing blockchain #".$transaction->id.PHP_EOL;
                $transactionDetails=Web3NodeBackend::getTransactionDetails($transaction->hash);
                //print_r($transactionDetails);die;
                if(is_array($transactionDetails) && count($transactionDetails)>0) {
                    $methodName=trim(strtolower($transactionDetails['methodName']));
                    switch ($methodName) {
                        case "deposittoken":
                        case "depositusdt":
                        case "scheduledepositusdt":
                            //$contractAddress=Setting::getValue(Setting::SETTING_CONTRACT_ADDRESS);
                            $contractAddress=env('VITE_STAKE_CONTRACT_ADDRESS');
                            if(trim(strtolower($contractAddress))==trim(strtolower($transactionDetails['to']))) {
                                /**
                                 * @var $userDetails User
                                 */
                                $userDetails=User::findByAddress($transactionDetails['from']);
                                if($userDetails) {
                                    $transaction->user_id=$userDetails->id;
                                    $amount=0;
                                    if(array_key_exists('logs',$transactionDetails)) {
                                        if(count($transactionDetails['logs'])>0) {
                                            foreach ($transactionDetails['logs'] as $log) {
                                                if($log['0']=="_amount") {
                                                    if(array_key_exists(1,$log)) {
                                                        $amount=Helper::fromWei($log[1]);
                                                    }
                                                }
                                            }
                                        }
                                    }

                                    if($amount>0) {
                                        if($methodName=="deposittoken") {
                                            $tokenPrice=Web3NodeBackend::getTokenUSDPrice();
                                            if($tokenPrice) {
                                                $amount=round($tokenPrice*$amount);
                                            }
                                        }
                                        $actualAmount=$amount;
                                        /* $fee=Setting::getValue(Setting::SETTING_NEW_STAKING_FEE);
                                        if($fee>0) {
                                            $actualAmount = round($amount / (1 + ($fee / 100)));
                                        } */
                                        if($actualAmount>0) {
                                            $transaction->comments=json_encode(['amount'=>$amount,'stake_amount'=>$actualAmount]);
                                            $transaction->is_correct=CommonConstants::YES;
                                        } else {
                                            $transaction->comments='Fee amount not fetched';
                                        }
                                    } else {
                                        $transaction->comments='Amount not found';
                                    }
                                } else {
                                    $transaction->comments='User details not found';
                                }
                            } else {
                                $transaction->comments='Contract address not matched';
                            }
                            $transaction->is_processed=CommonConstants::YES;
                            $transaction->method=$transactionDetails['methodName'];
                            $transaction->updated_at=date(CommonConstants::PHP_DATE_FORMAT);
                            if($transaction->update(['is_processed','user_id','is_correct','updated_at','comments'])) {
                                if($transaction->is_correct) {
                                    $decodeData=json_decode($transaction->comments,true);
                                    $result=$userDetails->addTransaction(
                                        'Deposit request #'.$transaction->ref_no,
                                        TransactionType::TRANSACTION_TYPE_DEPOSIT,
                                        CommonConstants::CREDIT,
                                        $decodeData['stake_amount'],
                                        Wallet::WALLET_USD,
                                        json_encode(['blockchain_txn_id'=>$transaction->id]),
                                        $transaction->id
                                    );
                                    if($result) {
                                        if($methodName=="scheduledepositusdt") {
                                            ScheduleStaking::saveRecord($userDetails->id,$decodeData['stake_amount'],$transaction->hash);
                                        } else {
                                            $stake = UserStake::addNewStake($userDetails, $decodeData['stake_amount'], 'BT-' . $transaction->id, false, false, null, $tokenPrice, $transaction->hash);
                                            if ($stake) {
                                                $decodeData['user_stake_id'] = $stake->id;
                                                DB::statement("update `blockchain_transactions` set `comments`='" . json_encode($decodeData) . "' where `id`='" . $transaction->id . "';");
                                            }
                                        }
                                    }
                                }
                            }
                            break;

                        case "authorizeaction":
                            $contractAddress=trim(env('VITE_STAKE_CONTRACT_ADDRESS'));
                            if(trim(strtolower($contractAddress))==trim(strtolower($transactionDetails['to']))) {
                                /**
                                 * @var $userDetails User
                                 */
                                $userDetails=User::findByAddress($transactionDetails['from']);
                                if($userDetails) {
                                    $transaction->user_id=$userDetails->id;
                                    $actionType="";
                                    $action=null;
                                    if(array_key_exists('logs',$transactionDetails)) {
                                        if(count($transactionDetails['logs'])>0) {
                                            foreach ($transactionDetails['logs'] as $log) {
                                                if($log['0']=="_actionType") {
                                                    if(array_key_exists(1,$log)) {
                                                        $actionType=$log[1];
                                                    }
                                                }
                                                if($log['0']=="_data") {
                                                    if(array_key_exists(1,$log) && $actionType!="") {
                                                        if($actionType=="1") { //withdrawal
                                                            if(stripos($log[1],"withdraw")!==false) {
                                                                $exp=explode("-",$log[1]);
                                                                if(count($exp)===2) {
                                                                    $amount=(trim($exp[1])*1);
                                                                    if($amount>0) {
                                                                        $action=['action'=>'withdraw','amount'=>$amount,'hash'=>$transactionDetails['hash']];
                                                                    }
                                                                }
                                                            }
                                                        }
                                                    }
                                                }
                                            }
                                        }
                                    }

                                    if(is_array($action) && count($action)>0) {
                                        $transaction->comments=json_encode($action);
                                        $transaction->is_correct=CommonConstants::YES;
                                    } else {
                                        $transaction->comments='Unable to detect action';
                                    }
                                } else {
                                    $transaction->comments='User details not found';
                                }
                            } else {
                                $transaction->comments='Contract address not matched';
                            }
                            $transaction->is_processed=CommonConstants::YES;
                            $transaction->method=$transactionDetails['methodName'];
                            $transaction->raw_data=json_encode($transactionDetails);
                            $transaction->updated_at=date(CommonConstants::PHP_DATE_FORMAT);
                            if($transaction->update(['is_processed','user_id','is_correct','raw_data','updated_at','comments'])) {
                                if($transaction->is_correct) {
                                    $decodeData=json_decode($transaction->comments,true);
                                    if(is_array($decodeData) && array_key_exists('action',$decodeData)) {
                                        switch ($decodeData['action']) {
                                            case "withdraw":
                                                $amount=$decodeData['amount'];
                                                $hash=$decodeData['hash'];

                                                $isValidRequest=false;
                                                $userWallet=UserWallet::fetchUserWallet($userDetails->id,Wallet::WALLET_WITHDRAWAL);
                                                if($userWallet) {
                                                    if ($userWallet->balance >= $amount) {
                                                        $isValidRequest=true;
                                                    }
                                                }

                                                if($isValidRequest) {
                                                    $userWithdrawal = UserWithdrawal::saveRequest($transaction->user_id, $hash,$amount,$decodeData,null,$tokenPrice);
                                                    if ($userWithdrawal) {
                                                        $decodeData['user_withdrawal_id'] = $userWithdrawal->id;
                                                        DB::statement("update `blockchain_transactions` set `comments`='" . json_encode($decodeData) . "' where `id`='" . $transaction->id . "';");
                                                    }
                                                } else {
                                                    $decodeData['message']='Insufficient wallet balance.';
                                                    if($userWallet) {
                                                        $decodeData['message']='Insufficient wallet balance. Current balance is '.$userWallet->balance;
                                                    }
                                                    DB::statement("update `blockchain_transactions` set `is_correct`='0',`comments`='" . json_encode($decodeData) . "' where `id`='" . $transaction->id . "';");
                                                }
                                                break;
                                        }
                                    }
                                }
                            }
                            break;
                    }
                }
            }
            sleep(2);

            DB::statement("update `blockchain_transactions` set `is_processed`='1',`is_correct`='0',`comments`='Timeout' where `is_processed`='0' and `expiry_time`<='".time()."';");
            return self::processTransactions($endTime);
        } else {
            return true;
        }
    }
}
