import {Head, <PERSON>} from '@inertiajs/react';
import UserLayout from "@/layouts/user/user-layout";
import {<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>} from "@mui/x-charts";
import BreadcrumbsButtons from "@/components/user/breadcrumbButtons";
import {useUserContext} from "@/context/UserContext";
import {useEffect, useState} from "react";
import UserService from "@/services/UserService";


export default function Directs() {

    const {
        userDetails,
        copyButton,
        showNotification,
        httpServiceObject
    } = useUserContext();

    const [directs, setDirects] = useState<any>(null);
    const [selectedDirect, setSelectedDirect] = useState(null);
    const [code, setCode] = useState(null);

    const fetchStats = async () => {
        try {
            let stats = await UserService.getDirects(httpServiceObject, code);
            setDirects(stats);

        } catch (error) {
            console.error("Error fetching stats:", error);
        }
    };
    useEffect(() => {
        fetchStats();
    }, [])

    const openModal = (item) => {
        setSelectedDirect(item);
        let myModal = new bootstrap.Modal(document.getElementById('standardmodal'), {});
        myModal.show();
    }

    const getDirects = (code) =>{
        setCode(code);
    }

    useEffect(() => {
        const existingModal = bootstrap.Modal.getOrCreateInstance(document.getElementById('standardmodal'));
        existingModal.hide();
        fetchStats();
    }, [code]);

    return (
        <UserLayout>

            <main className="adminuiux-content has-sidebar">

                <div className="container mt-4">
                    <div className="row gx-3 align-items-center">
                        <div className="col-12 col-sm">
                            <nav aria-label="breadcrumb" className="mb-2">
                                <ol className="breadcrumb mb-0">
                                    <li className="breadcrumb-item bi"><Link href={route('user.dashboard')}>
                                        <i className="bi bi-house-door me-1 fs-14"></i> Dashboard</Link></li>
                                    <li className="breadcrumb-item active bi" aria-current="page">Directs</li>
                                </ol>
                            </nav>
                            <h5>My Directs</h5>
                        </div>
                        <div className="col-12 col-sm-auto text-end py-3 py-sm-0">
                            {
                                (code && code!="") && <button className="btn btn-theme ms-2 me-2" onClick={()=>getDirects('')}>Back to Root</button>
                            }
                            {/*<BreadcrumbsButtons/>*/}
                        </div>
                    </div>
                </div>


                <div className="container" id="main-content">


                    <div className="card adminuiux-card mt-4 mb-0">
                        <div className="card-body">
                            <div className="table-responsive">
                                <table
                                    className="table">
                                    <thead>
                                    <tr>
                                        <th className="text-center sorting">Joining Date</th>
                                        <th className="text-center sorting">Username</th>
                                        <th className="text-center sorting">Lifetime Investment</th>
                                        <th className="text-center sorting">Team Business</th>
                                        <th className="text-center sorting">This Month Total Business</th>
                                        <th className="text-center sorting">Rank</th>
                                        <th className="text-center sorting">Active Directs</th>
                                        <th className="text-center sorting">Team</th>
                                        {
                                            (!code || code=="") && <th className="text-center sorting">Spill Link</th>
                                        }
                                        <th className={'text-center sorting'}></th>
                                    </tr>
                                    </thead>
                                    <tbody>

                                    {(directs && directs.length > 0) ? (
                                        directs &&
                                        Object.entries(directs)
                                            .map(([key, item]) => (
                                                <tr className="odd" key={key}>
                                                    <td className="text-center">{item?.joining_date}</td>
                                                    <td className="text-center">
                                                        <span className="badge badge-light text-bg-theme-1 cursor-pointer" onClick={()=>getDirects(item?.referral_code)}>{item?.username}</span>
                                                    </td>
                                                    <td className="text-center sorting_1">{item?.formatted_lifetime_investment}</td>
                                                    <td className="text-center">{item?.formatted_team_business}</td>
                                                    <td className="text-center">{item?.formatted_current_month_business}</td>
                                                    <td className="text-center"><span className="badge badge-light text-bg-theme-2">{(item?.rank!="") ? item?.rank : 'n/a'}</span></td>
                                                    <td className="text-center">{item?.active_directs}</td>
                                                    <td className="text-center">{item?.total_team}</td>
                                                    {
                                                        (!code || code=="") && <td className="text-center">
                                                            <button className="btn btn-outline-theme" onClick={()=>copyButton(item?.referral_link+'&direct='+userDetails?.referral_code)}>Copy Link</button>
                                                        </td>
                                                    }
                                                    <td className="text-center">
                                                        <button className="btn btn-outline-theme" onClick={()=>openModal(item)}>Get Details</button>
                                                    </td>
                                                </tr>
                                            ))
                                        )
                                        : (
                                            <>
                                                <tr>
                                                    <td colSpan={9} className={'text-center'}>No Directs Found</td>
                                                </tr>
                                            </>
                                        )}
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>


                </div>


            </main>

            <div className="modal fade" id="standardmodal" tabIndex="-1" aria-labelledby="standardmodalLabel"
                 aria-hidden="true">
                <div className="modal-dialog">
                    <div className="modal-content">
                        <div className="modal-header">
                            <p className="modal-title h5" id="standardmodalLabel">Direct Details</p>
                            <button type="button" className="btn-close" data-bs-dismiss="modal"
                                    aria-label="Close"></button>
                        </div>
                        <div className="modal-body">
                            {(selectedDirect!=null) &&
                            <ul className="list-group">
                                <li className="list-group-item d-flex justify-content-between align-items-center">
                                    Joining Date
                                    <span className="badge badge-light text-bg-warning rounded-pill">{selectedDirect?.joining_date}</span>
                                </li>
                                <li className="list-group-item d-flex justify-content-between align-items-center">
                                    Username
                                    <span className="badge badge-light text-bg-theme-1 cursor-pointer"  onClick={()=>getDirects(selectedDirect?.referral_code)}>{selectedDirect?.username}</span>
                                </li>
                                <li className="list-group-item d-flex justify-content-between align-items-center">
                                    Active Investment
                                    <span className="badge badge-light text-bg-warning rounded-pill">{selectedDirect?.formatted_active_investment}</span>
                                </li>
                                <li className="list-group-item d-flex justify-content-between align-items-center">
                                    Lifetime Investment
                                    <span className="badge badge-light text-bg-warning rounded-pill">{selectedDirect?.formatted_lifetime_investment}</span>
                                </li>
                                <li className="list-group-item d-flex justify-content-between align-items-center">
                                    Team Business
                                    <span className="badge badge-light text-bg-warning rounded-pill">{selectedDirect?.formatted_team_business}</span>
                                </li>
                                <li className="list-group-item d-flex justify-content-between align-items-center">
                                    Total Business
                                    <span className="badge badge-light text-bg-warning rounded-pill">{selectedDirect?.formatted_total_business}</span>
                                </li>
                                <li className="list-group-item d-flex justify-content-between align-items-center">
                                    This Month Business
                                    <span className="badge badge-light text-bg-warning rounded-pill">{selectedDirect?.formatted_current_month_business}</span>
                                </li>
                                {/*<li className="list-group-item d-flex justify-content-between align-items-center">
                                    Lifetime Team Working Income
                                    <a href="javascript:void(0);"
                                       className="fetch-team-working-income btn btn-outline-theme btn-sm"
                                       data-id="techoxio">Calculate Now</a>
                                </li>*/}
                                <li className="list-group-item d-flex justify-content-between align-items-center">
                                    Rank
                                    <span className="badge badge-light text-bg-theme-2 rounded-pill">{(selectedDirect?.rank!="") ? selectedDirect?.rank : 'n/a'}</span>
                                </li>
                                <li className="list-group-item d-flex justify-content-between align-items-center">
                                    Directs
                                    <span className="badge badge-light text-bg-warning rounded-pill">{selectedDirect?.total_directs}</span>
                                </li>
                                <li className="list-group-item d-flex justify-content-between align-items-center">
                                    Active Directs
                                    <span className="badge badge-light text-bg-warning rounded-pill">{selectedDirect?.active_directs}</span>
                                </li>
                                <li className="list-group-item d-flex justify-content-between align-items-center">
                                    Team
                                    <span className="badge badge-light text-bg-warning rounded-pill">{selectedDirect?.total_team}</span>
                                </li>
                            </ul>
                            }

                        </div>
                    </div>
                </div>
            </div>


        </UserLayout>
    );
}
