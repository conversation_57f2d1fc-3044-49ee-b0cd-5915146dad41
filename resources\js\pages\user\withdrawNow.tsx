import {Head, <PERSON>} from '@inertiajs/react';
import UserLayout from "@/layouts/user/user-layout";
import {<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>} from "@mui/x-charts";
import {useUserContext} from "@/context/UserContext";
import {useEffect, useState} from "react";
import UserService from "@/services/UserService";
import {z} from "zod";
import {WITHDRAWAL_WALLET} from "@/constants/Constants";
import {SendWriteCall} from "@/components/SendWriteCall";


export default function WithdrawNow({minValue,multipleValue}) {
    const {
        userDetails,
        address,
        userToken,
        disconnectWallet,
        showNotification,
        httpServiceObject,
        walletBalances,
        setWalletBalances,
        fetchBalanceFromApi,
        neonixBalance,
        usdtBalance,
        dxeBalance,
        dxeUsdPrice,
        DXEStakeAddress,
        updateWalletBalances,
        DXEStakeContractABI,
        convertEthToWei,
        web3Instance,
        handleSignMsg
    } = useUserContext();

    const [dashboardStats, setDashboardStats] = useState<any>(null);
    const [btnLoading, setBtnLoading] = useState(false);
    const [minimumWithdrawAmount, setMinimumWithdrawAmount] = useState(minValue);
    const [multipleAmount, setMultipleAmount] = useState(multipleValue);
    const [amount, setAmount] = useState<any>(minimumWithdrawAmount);
    const [withdrawalWallet, setWithdrawalWallet] = useState<any>(0);
    const [sendTransferRequest, setSendTransferRequest] = useState<any>(null);

    const investAmount = (val) => {
        // setAmount(null);
        setAmount(val);
    }

    const fetchStats = async () => {

        try {
            await fetchBalanceFromApi();

            /*let stats = await UserService.dashboardStats(httpServiceObject);
            setDashboardStats(stats);*/
            walletBalances &&
            Object.entries(walletBalances)
                .filter(([key, item]) => item?.id === WITHDRAWAL_WALLET)
                .map(([key, item]) => (
                    setWithdrawalWallet(item?.balance)
                ))

        } catch (error) {
            console.error("Error fetching stats:", error);
        }
    };
    useEffect(() => {
        fetchStats();
        updateWalletBalances();
    }, [])

    const submitForm = async (e: React.FormEvent<HTMLFormElement>) => {
        e.preventDefault();
        setBtnLoading(true);
        setSendTransferRequest(null);
        const myformdata = new FormData(e.currentTarget);
        let formData = Object.fromEntries(myformdata.entries());

        const userInfoSchema = z.object({
            amount: z.number()
                .min(minimumWithdrawAmount, "Minimum amount should be $" + minimumWithdrawAmount)
                .max(withdrawalWallet, 'Insufficient balance. You have '+parseFloat(withdrawalWallet).toFixed(6)+' in your withdrawal wallet.'),
        });

        try {
            formData.amount = (formData.amount * 1);
            if(formData.amount%multipleAmount!==0) {
                showNotification('Amount should be multiple of $'+multipleAmount);
                return;
            }
            userInfoSchema.parse(formData);
            console.log(formData.amount, 'amount is', formData);
            const response = await UserService.withdrawalRequest(httpServiceObject, formData);
            if (response) {
                const hex = web3Instance.utils.stringToHex("withdraw-" + formData.amount.toString());
                console.log("hex is ",hex);
                setSendTransferRequest({
                    address: DXEStakeAddress, // Contract address
                    abi: DXEStakeContractABI,
                    functionName: "authorizeAction",
                    args: [1,1,web3Instance.utils.padLeft(hex,64)],
                });
            }

        } catch (err) {
            if (err instanceof z.ZodError) {
                showNotification(err.issues[0].message);
            } else {
                showNotification(err);
            }
        } finally {
            setBtnLoading(false);
        }
    };

    const handleTransfer = (params) => {
        setSendTransferRequest(null);
        if(params) {
            showNotification('Request sent successfully.','success');
            UserService.saveHash(httpServiceObject, params).then(res=>{
                investAmount(minimumWithdrawAmount);
                showNotification('Your transaction will auto approve after getting confirmations.', 'success');
            }).catch(err=>{
                showNotification('Something went wrong while adding request.');
            });
        } else {
            showNotification('Withdraw request cancelled');
        }
        console.log("handleTransfer params is ",params);
    }

    return (
        <UserLayout>

            {(sendTransferRequest ? (<>
                <SendWriteCall onCompleted={handleTransfer} txData={sendTransferRequest}/>
            </>) : (<></>))}

            <main className="adminuiux-content has-sidebar">

                <div className="container mt-4">
                    <div className="row gx-3 align-items-center">
                        <div className="col-12 col-sm">
                            <nav aria-label="breadcrumb" className="mb-2">
                                <ol className="breadcrumb mb-0">
                                    <li className="breadcrumb-item bi"><Link href={route('user.dashboard')}><i
                                        className="bi bi-house-door me-1 fs-14"></i> Dashboard</Link></li>
                                    <li className="breadcrumb-item active bi" aria-current="page">Withdraw Now</li>
                                </ol>
                            </nav>
                            <h5>Withdraw Now</h5>
                        </div>
                        {/*<div className="col-12 col-sm-auto text-end py-3 py-sm-0">

                        </div>*/}
                    </div>
                </div>


                <div className="container mt-4" id="main-content" data-bs-spy="scroll" data-bs-target="#list-example"
                     data-bs-smooth-scroll="true">

                    <div className="row" id="list-item-1">

                        <div className="col-12 ">
                            <div className="row">
                                <div className="col-12 col-lg-8 mb-4">

                                    <div className="card adminuiux-card">
                                        <div className="card-header">
                                            <h5>Withdraw Now </h5>
                                            <p className="text-secondary">Withdraw your earnings instantly and securely</p>
                                        </div>
                                        <div className="card-body">
                                            <form onSubmit={submitForm}>
                                                <div className="row mb-2">
                                                    <div className="col-12 mb-3">
                                                        <p className="alert alert-info bg-theme-1" style={{border:'0px'}}>
                                                            Withdraw Wallet Balance: <strong>{withdrawalWallet} USD</strong>, DRIXE USD Price: <strong>${dxeUsdPrice}</strong>
                                                        </p>
                                                    </div>


                                                    <div className="col-12 col-md-12 mb-3">
                                                        <div className="form-floating">
                                                            <input type="text" className="form-control"
                                                                   id="Wallet-Address"
                                                                   defaultValue={userDetails?.address}
                                                                   placeholder="Wallet Address" readOnly={true}/>
                                                            <label htmlFor="Wallet-Address">Wallet Address</label>
                                                        </div>

                                                    </div>


                                                    <div className="col-12 col-md-12 mb-3">
                                                        <div className="form-floating">
                                                            <input autoComplete={'off'} type="text" onChange={(e)=>investAmount(e.target.value)} required={true} name={'amount'} value={amount} className="form-control" id="Amount"
                                                                   placeholder="Amount" />
                                                            <label htmlFor="Amount">Amount</label>
                                                        </div>

                                                    </div>

                                                   {/* <div className="col-12 mb-3">
                                                        <p className="alert alert-warning">
                                                            <strong>Note:</strong> Please note that withdrawals may take
                                                            up to 48 hours to process.
                                                        </p>
                                                    </div>*/}

                                                </div>
                                                <div className="row align-items-center">
                                                    <div className="col">

                                                    </div>
                                                    <div className="col-auto">
                                                        <button type="submit" className="btn btn-theme"
                                                                disabled={btnLoading}>
                                                            {btnLoading ? "Loading..." : "Continue"}
                                                        </button>
                                                    </div>
                                                </div>
                                            </form>
                                        </div>
                                    </div>
                                </div>
                                <div className="col-12 col-lg-4 mb-4 invest-stat-boxes-2">
                                    {walletBalances &&
                                    Object.entries(walletBalances)
                                        .map(([key, item]) => (
                                            <div className="card adminuiux-card mb-4" key={key}>
                                                <div className="card-body">
                                                    <div className="row gx-3 ">
                                                        <div className="col-auto">
                                                            <i className="bi bi-wallet2 fs-4 avatar avatar-50 bg-theme-1 text-white rounded"></i>
                                                        </div>
                                                        <div className="col">
                                                            <h4 className="mb-0">{item?.formattedBalance} USD</h4>
                                                            <p className="small opacity-75">{item?.name} Wallet</p>
                                                        </div>
                                                    </div>

                                                </div>
                                            </div>

                                        ))}

                                </div>
                            </div>
                        </div>


                    </div>

                </div>
            </main>


        </UserLayout>
    );
}
