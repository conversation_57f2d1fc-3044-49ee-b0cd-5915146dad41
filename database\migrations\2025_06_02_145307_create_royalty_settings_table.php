<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends \App\Base\Database\MigrationBase
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        $this->schema->create('royalty_settings', function (\App\Base\Database\BlueprintBase $table) {
            $table->id();
            $table->integer('rank_id')->comment('in USD');
            $table->double('incentive')->comment('in % - from week leg monthly');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('royalty_settings');
    }
};
