<?php

namespace App\Http\Controllers;

use App\Components\Helper;
use App\Http\Controllers\Controller;
use App\Http\Requests\Settings\ProfileUpdateRequest;
use App\Models\Setting;
use App\Models\User;
use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\Rule;
use Inertia\Inertia;
use Inertia\Response;

class SiteController extends Controller
{
    public function index(Request $request)
    {
        $sponsorCode='';
        $directCode='';
        $sponsorDetails=null;
        $directDetails=null;

        if($request->input('sponsor')) {
            $sponsorCode=trim($request->input('sponsor'));
        }
        if($request->input('direct')) {
            $directCode=trim($request->input('direct'));
        }

        if($sponsorCode!="") {
            $sponsorDetails=User::findBySponsorCode($sponsorCode);
            if(!$sponsorDetails) {
                return redirect(route('home'));
            }
        }

        if($directCode!="") {
            $directDetails=User::findBySponsorCode($directCode);
            if(!$directDetails) {
                return redirect(route('home'));
            }
        }

        if($directDetails && !$sponsorDetails) {
            return redirect(route('home'));
        } else {
            if($sponsorDetails && $directDetails) {
                if(!$sponsorDetails->isDescendantOf($directDetails)) {
                    return redirect(route('home'));
                }
            }
        }

        return Inertia::render('site/index',compact('sponsorCode','directCode'));
    }

    public function registerUser(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'nullable|string|max:255',
            'email' => 'nullable|string|email|max:255',
            'username' => 'nullable|string|max:255|unique:'.User::class,
            'sponsor' => 'required|string|max:255',
            'direct' => 'nullable|string|max:255',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'error' => $validator->errors()
            ]);
        }
        $name=trim($request->input('name'));
        $email=trim($request->input('email'));
        $username=$request->input('username');
        if(!$username || $username=="") {
            $username = User::generateUsername();
        }
        if(!$name || $name=="") {
            $name=$username;
        }
        if(!$email || $email=="") {
            $email=strtolower($username).'@drixe.com';
        }
        $address="0x".strtolower($username);
        // Validate sponsor
        $sponsor = User::findBySponsorCode($request->input('sponsor'));
        if(!$sponsor) {
            // Check if it's the system default referral code
            if($request->input('sponsor') === \App\Constants\CommonConstants::DEFAULT_REFERRAL_CODE) {
                // Find the system/admin user (backoffice user)
                $sponsor = User::query()->where('username', 'backoffice')->first();
                if(!$sponsor) {
                    return response()->json([
                        'success' => false,
                        'error' => 'System user not found. Please run database seeders.',
                    ]);
                }
            } else {
                return response()->json([
                    'success' => false,
                    'error' => 'Invalid sponsor code',
                ]);
            }
        }

        // Validate direct sponsor if provided
        $directSponsor = null;
        if($request->has('direct') && $request->input('direct') != '') {
            $directSponsor = User::findBySponsorCode($request->input('direct'));
            if(!$directSponsor) {
                return response()->json([
                    'success' => false,
                    'error' => 'Invalid direct code',
                ]);
            }
        }

        // Validate relationship: sponsor should be a child of direct user
        if($sponsor && $directSponsor) {
            if(!$sponsor->isDescendantOf($directSponsor)) {
                return response()->json([
                    'success' => false,
                    'error' => 'Invalid relationship: Sponsor must be a descendant of the direct user',
                ]);
            }
        }

        // Get last children of any first child of the sponsor user
        $lastChildrenOfSponsor = null;
        if($sponsor && $directSponsor) {
            $lastChildrenOfSponsor = $sponsor->getLastChildrenOfFirstChildren();
        }

        try {
            $sponsorCode=trim($request->input('sponsor'));
            if($lastChildrenOfSponsor) {
                $sponsorCode=trim($lastChildrenOfSponsor->referral_code);
            }
            $userData = [
                'name' => trim($name),
                'username' => trim($username),
                'wallet_address' => trim($address),
                'password' => trim($address), // Using wallet address as password
                'referred_by' => $sponsorCode,
                'email' => trim($email),
            ];

            // Add direct_id if direct sponsor is provided
            if($directSponsor) {
                $userData['direct_id'] = $directSponsor->id;
            }

            $user = User::create($userData);

            if($user) {
                $user = User::findByAddress($address);

                $responseData = [
                    'success' => true,
                    'message' => 'User registered successfully',
                    'user' => [
                        'name' => $user->name,
                        'email' => $user->email,
                        'username' => $user->username,
                        'referral_code' => $user->referral_code,
                        'wallet_address' => $user->wallet_address,
                        'sponsor_code' => $user->referred->referral_code,
                        'direct_code' => $user->direct->referral_code,
                    ],
                ];
                return response()->json($responseData);
            }
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => $e->getMessage(),
            ]);
        }

        return response()->json([
            'success' => false,
            'error' => 'Unable to register user',
        ]);
    }
    public function addAdminStake(Request $request)
    {
        $rules = [
            'username' => ['required', 'string',
                function ($attribute, $value, $fail) {
                    if (!empty($value)) {
                        $findUser=User::findByUsername($value);
                        if(!$findUser) {
                            $fail('Invalid username');
                        } else {
                            if(!$findUser->is_binary_created) {
                                $fail('User tree structure not created yet. Please try after sometime.');
                            }
                        }
                    }
                },
            ],
            'amount' => ['required', 'string', 'integer',
                function ($attribute, $value, $fail) {
                    if (!empty($value)) {
                        $value=(int)$value;
                        if($value<=0) {
                            $fail('Invalid amount entered');
                        } else {
                            $minAmount=Setting::getValue(Setting::SETTING_MINIMUM_INVESTMENT);
                            if($minAmount>$value) {
                                $fail('Minimum amount should be '.$minAmount);
                            }
                        }
                    }
                },
            ],
        ];
        $validator = Validator::make($request->all(), $rules);

        if ($validator->fails()) {
            return response()->json([
                'error' => $validator->errors(),
            ]);
        } else {
            $amount=(int)trim($request->input('amount'));
            $user=\App\Models\User::findByUsername(trim($request->input('username')));
            if($user) {
                $stake=new \App\Models\UserStakeAdminLog();
                $stake->user_id=$user->id;
                $stake->amount=$amount;
                if($stake->save()) {
                    return response()->json([
                        'success' => true,
                        'response' => 'Investment added successfully for '.$user->username.' ('.Helper::printAmount($amount).')',
                    ]);
                } else {
                    return response()->json([
                        'error' => 'Unable to add user staking',
                    ]);
                }
            }
        }
        return response()->json([
            'error' => 'Invalid request',
        ]);
    }
}
