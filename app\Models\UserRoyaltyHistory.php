<?php

namespace App\Models;

use App\Base\Model\BaseModel;
use App\Components\Helper;
use App\Constants\CommonConstants;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\DB;

class UserRoyaltyHistory extends BaseModel
{
    protected $fillable = [
        'ref_no',
        'user_id',
        'rank_id',
        'date',
        'amount',
        'raw_amount',
        'percentage',
        'created_at',
        'updated_at',
    ];

    public function user()
    {
        return $this->hasOne(User::class, 'id', 'user_id');
    }

    public function rank()
    {
        return $this->hasOne(RankSetting::class, 'id', 'rank_id');
    }

    public static function saveHistory($user_id,$rank_id,$amount=null,$percentage=null,$date=null) {
        if(!$date) {
            $date=date(CommonConstants::PHP_DATE_FORMAT_SHORT);
        }
        $model=self::query()
            ->where('user_id','=',$user_id)
            ->where('rank_id','=',$rank_id)
            ->where('date','=',$date)
            ->limit(1)->first();
        if($model) {
            return false;
        } else {
            $model=new UserRoyaltyHistory();
            $model->user_id=$user_id;
            $model->rank_id=$rank_id;
            $model->date=$date;
            if(!$amount) {
                $amount = $model->rank->total_business;
            }
            if(!$percentage) {
                $fetchPercentage=RoyaltySetting::query()
                    ->where('rank_id','=',$model->rank_id)
                    ->first();
                if($fetchPercentage) {
                    $percentage=$fetchPercentage->incentive;
                }
            }
            if(!isset($amount) || $amount<=0 || !$percentage) {
                return false;
            }
            $finalAmount=round((($amount*CommonConstants::OTHER_LEG_PERCENTAGE)/100),CommonConstants::USD_PRECISION);
            $finalAmount=round((($finalAmount*$percentage)/100),CommonConstants::USD_PRECISION);
            $model->raw_amount=$amount;
            $model->percentage=$percentage;
            $model->amount=$finalAmount;
            if($model->save()) {
                $user=$model->user;
                $amount=$model->amount;
                $amount = Helper::fetchUpdatedAmount($amount,$user->income_x,$user->active_investment,$user->total_earnings);
                $user->addTransaction(
                    'Royalty credited for '.date("M Y",strtotime($model->date)),
                    TransactionType::TRANSACTION_TYPE_RANK_REWARD,
                    CommonConstants::CREDIT,
                    $amount,
                    Wallet::WALLET_EARNINGS,
                    json_encode(['rank_id' => $model->rank_id,'date'=>$model->date]),
                    $model->rank_id
                );
                return true;
            }
        }
        return false;
    }

    public static function processRoyalty() {
        try {
            $settings=RoyaltySetting::query()->orderBy("rank_id")->get();
            if(count($settings)>0) {
                $time=time();
                //$time=strtotime("2025-03-30");
                $date=date("d",$time);
                if(date("m",$time)=="02") {
                    if(date("t",$time)==date("d",$time)) {
                        $date="30";
                    }
                }
                $date=(int)$date;
                foreach ($settings as $setting) {
                    $exp=explode(",",$setting->allowed_days);
                    if(count($exp)>0) {
                        if(in_array($date,$exp)) {
                            $users=User::query()
                                ->where('rank_id','=',$setting->rank_id)
                                ->where('status','=',User::STATUS_ACTIVE)
                                ->orderBy("id")->get();
                            if(count($users)>0) {
                                foreach ($users as $user) {
                                    if($user->active_investment>0) {
                                        self::saveHistory($user->id,$setting->rank_id,$setting->rank->total_business,$setting->incentive,date(CommonConstants::PHP_DATE_FORMAT_SHORT,$time));
                                    }
                                }
                            }
                        }
                    }
                }
            } else {
                throw new \Exception('No royalty settings found');
            }
        } catch (\Exception $e) {

        }
        return false;
    }
}
