<?php

namespace App\Models;

use App\Base\Model\BaseModel;

class Enquiry extends BaseModel
{
    const STATUS_NEW = 'new';
    const STATUS_READ = 'read';
    const STATUS_REPLIED = 'replied';
    const STATUS_CLOSED = 'closed';

    protected $fillable = [
        'first_name',
        'last_name',
        'email',
        'phone',
        'company_name',
        'message',
        'status',
        'created_ip',
        'user_agent',
        'created_at',
        'updated_at',
    ];

    public function onCreating()
    {
        // Sanitize and format data
        if($this->first_name) {
            $this->first_name = ucwords(strtolower(trim(strip_tags($this->first_name))));
        }
        if($this->last_name) {
            $this->last_name = ucwords(strtolower(trim(strip_tags($this->last_name))));
        }
        if($this->email) {
            $this->email = strtolower(trim(strip_tags($this->email)));
        }
        if($this->phone) {
            $this->phone = trim(strip_tags($this->phone));
        }
        if($this->company_name) {
            $this->company_name = trim(strip_tags($this->company_name));
        }
        if($this->message) {
            $this->message = trim(strip_tags($this->message));
        }
        if(!$this->status) {
            $this->status = self::STATUS_NEW;
        }

        parent::onCreating();
    }

    public function getFullNameAttribute()
    {
        return $this->first_name . ' ' . $this->last_name;
    }

    public function apiData()
    {
        return [
            'id' => $this->id,
            'first_name' => $this->first_name,
            'last_name' => $this->last_name,
            'full_name' => $this->getFullNameAttribute(),
            'email' => $this->email,
            'phone' => $this->phone,
            'company_name' => $this->company_name,
            'message' => $this->message,
            'status' => $this->status,
            'created_at' => $this->created_at,
        ];
    }
}
