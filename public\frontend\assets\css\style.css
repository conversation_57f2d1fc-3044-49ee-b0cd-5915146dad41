:root {
    --btn-gradiant-color-background:
        /*linear-gradient(0deg, rgba(3, 3, 3, 0), rgba(3, 3, 3, 0)),
        radial-gradient(28.98% 110.94% at 43.75% -31.25%, rgba(187, 143, 68, 0.1) 21.25%, rgba(0, 0, 0, 0) 100%),
        radial-gradient(80.68% 51.24% at 19.32% 40.62%, rgba(187, 143, 68, 0.4) 0%, rgba(139, 91, 26, 0.24) 100%),
        radial-gradient(130.16% 129.69% at 63.64% -12.5%, rgba(255, 206, 128, 0.1) 0%, rgba(100, 70, 20, 0) 100%),
        radial-gradient(42.85% 184.29% at 15.34% 39.06%, rgba(255, 198, 112, 0.1) 0%, rgba(100, 70, 20, 0) 100%);*/
        linear-gradient(90deg,rgba(97, 67, 36, 1) 0%, rgba(187, 143, 68, 1) 100%);

    --theme-bg-color: #000000;
    --theme-text-color: #fff;
}


/**:not(.scroller *) {
    transition: all 0.3s ease-in-out !important;
}*/

.sidebar, .sidebar * {
    transition: all 0.3s ease-in-out !important;
}
html, body, #scroll-container {
    overflow-x: hidden;

}

body {
    margin: 0;
    padding: 0;
    background-color: var(--theme-bg-color);
    color: var(--theme-text-color);
    overflow-y: scroll;
    font-family: "Barlow", sans-serif;
    overflow-x: hidden !important;
    box-sizing: border-box;
    position: relative;
}

#wave-canvas {
    display: flex;
    width: 100vw;
    height: 100vh;
    position: fixed;
    top: 410px;
    transform: rotate(170deg);
    opacity: 0.8;
    right: -70px;
}
::-webkit-scrollbar {
    display: none !important;
    width: 0;
    height: 0;
}
#desktop-login-regs-btns{
    visibility: visible !important;
}


.scroller {
    /*border: 4px solid #333;*/
    /*width: 300px;*/
}

.scroller section {
    /*min-height: 100%;*/
    /*padding: 10px;*/
    align-content: center;
    position: relative;
    z-index: 9 !important;
}


.scroller {
    height: 100vh;
    overflow-y: scroll;
    scroll-snap-type: y mandatory;
}

.scroller section {
    scroll-snap-align: start;
}

section:not(.Toastify){
    align-items: center;
    min-height: 100vh !important;
    align-content: center;
    display: flex;
}

.top-bar {
    position: fixed;
    width: 100%;
    z-index: 99;
    background-color: #0000003d;
    backdrop-filter: blur(5px);
    top: 0 !important;
}

.navbar-brand .logo {
    max-width: 170px;
}

.login-btn:hover, .register-btn:hover {
    /*box-shadow: 0 0 10px rgba(155, 74, 233, 0.46);*/
    box-shadow: 0 0 10px rgba(187, 143, 68, 0.46);
    background: var(--btn-gradiant-color-background);
}

.login-btn.active, .register-btn.active {
    border: unset !important;
    background: var(--btn-gradiant-color-background);
}

.login-btn, .register-btn {
    /*background: #D9D9D91A;*/
    background: var(--btn-gradiant-color-background);
    color: #ffffff;
    border-radius: 54px;
    padding: 24px 50px;
    font-family: "Barlow", sans-serif;
    font-weight: 400;
    line-height: 100%;
    font-size: 20px;
    border:0px  !important;

}
.modal .modal-content {
    background: #010102;
    color: #fff !important;
    /* box-shadow: 0px 0px 20px #bb8f44; */
    border: 2px solid #bb8f44;
    border-radius: 25px;
}
.modal-header{
    border-bottom: 0px !important;
}
.modal-footer{
    border-top: 0px !important;
}
.modal .modal-content .login-btn{
    padding: 15px 30px;
}
.modal-title{
    color: #bb8f44;
}
.modal input, .modal input:focus{
    background: #252525;
    border: 1px solid #bb8f44;
    color: #bb8f44;
}
.modal label{
    color: #bb8f44;
}
.modal .col-md-12{
    margin-bottom: 15px;
}
.modal small, .modal strong{
    color: #bb8f44;
}
.text-golden{
    color: #bb8f44;
    font-size: 20px;
}


.header-btns {
    gap: 30px;
    display: flex;
}

.top-bg-circle-img {
    background-image: url("../images/circle-bg.png");
    background-repeat: no-repeat;
    background-position: center;
    background-size: cover;
    width: 270px;
    height: 270px;
    position: fixed;
    top: 16px;
    left: 80px;

    /* ✅ Combined animation */
    animation:
            rotate-circle 20s linear infinite,
            fadeInOut 6s ease-in-out infinite;
    transform-origin: center center;
}

@keyframes rotate-circle {
    0% {
        transform: rotate(0deg);
    }
    100% {
        transform: rotate(360deg);
    }
}

@keyframes fadeInOut {
    0%, 100% {
        opacity: 0.2;
    }
    50% {
        opacity: 0.5;
    }
}



.sidebar {
    position: fixed;
    width: 245px;
    border-left: 2px solid #ffffff;
    top: 50%;
    left: 100px;
    z-index: 9;
    margin-top: -270px;
}

.sidebar::before,
.sidebar::after {
    content: "";
    position: absolute;
    left: -30px;
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background-image: url("../images/menu-puls-img.png");
    background-size: cover;
    background-repeat: no-repeat;
    background-position: center;
    box-shadow: 0 0 10px rgba(255, 255, 255, 0.3);
    animation: shadowPulse 2s infinite ease-in-out;
    z-index: 1;
}

@keyframes shadowPulse {
    0%, 100% {
        box-shadow: 0 0 10px rgba(255, 255, 255, 0.3);
    }
    50% {
        box-shadow: 0 0 20px rgba(255, 255, 255, 0.6);
    }
}

.sidebar::before {
    top: -30px;
}

.sidebar::after {
    bottom: -30px;
}

.sidebar ul {
    list-style: none;
    padding: 60px 0;
    margin: 0;
}

.sidebar li {
    position: relative;
    margin: 25px 0;
    padding: 10px 35px;
}

.sidebar li::before {
    content: none;
}

.sidebar #menu-icon {
    content: "";
    position: absolute;
    left: -35px;
    top: 87px;
    width: 70px;
    height: 70px;
    background-color: #00041C;
    border-radius: 50%;
    background-image: url('../images/favicon.png');
    background-size: 80%;
    background-position: center;
    background-repeat: no-repeat;
}

/*.sidebar li:has(a.active)::before {*/
/*    content: "";*/
/*    position: absolute;*/
/*    left: -35px;*/
/*    top: 0;*/
/*    width: 70px;*/
/*    height: 70px;*/
/*    background-color: #00041C;*/
/*    border-radius: 50%;*/
/*    background-image: url('../images/favicon.png');*/
/*    background-size: 80%;*/
/*    background-position: center;*/
/*    background-repeat: no-repeat;*/
/*}*/


.sidebar a {
    text-decoration: none;
    color: #ffffff;
    font-size: 20px;
    font-weight: 500;
    transition: 0.3s;
}

.sidebar a.active {
    /*color: #bb8f44;*/
    color: #bb8f44;
    font-weight: bold;
    text-shadow: 0 0 10px #bb8f44;
}

.sidebar a:hover {
    /*color: #bb8f44;*/
    color: #bb8f44;
}

.social-media-icon {
    position: fixed;
    right: 30px;
    top: 50%;
    z-index: 9;
    margin-top: -130px;
}

.social-media-icon ul {
    padding: 0;
    margin: 0;
}

.social-media-icon ul li {
    list-style-type: none;
    margin: 15px 0;
}

.social-media-icon ul li a {
    font-size: 35px;
    height: 60px;
    display: flex;
    background: #D9D9D91A;
    color: #ffffff;
    font-weight: 500;
    text-decoration: none;
    justify-content: center;
    width: 60px;
    align-items: center;
    border-radius: 50%;
}

.social-media-icon ul li a:hover {
    background: var(--btn-gradiant-color-background);
}

.page-wrapper {
    position: relative;
    padding: 150px 106px 0 345px;
    /*height: calc(100vh - 100px);*/
    align-content: center;
    overflow: auto;
}

.page-wrapper::before {
    content: "DRIXE";
    position: fixed;
    top: 200px;
    left: 0;
    right: 0;
    margin: auto;
    font-family: 'Impact', sans-serif;
    font-weight: 400;
    z-index: 0;
    font-size: 429px;
    line-height: 100%;
    letter-spacing: 0;
    text-align: center;
    color: #FFFFFF0C;
    -webkit-text-stroke: 1.5px rgba(255, 255, 255, 0.1);
    text-stroke: 1.5px rgba(255, 255, 255, 0.1);
    opacity: 0.5;

}

.page-wrapper::after {
    content: "";
    background-color: #bb8f444D;
    height: 663px;
    width: 631px;
    box-shadow: 2px 2px 63px #bb8f444D;
    position: fixed;
    top: 0;
    left: 0;
    filter: blur(100px);
    z-index: 0;
    animation: floatEffect 30s infinite linear alternate;
    pointer-events: none;
}

/* Keyframes for animation */
@keyframes floatEffect {
    0% {
        top: 0;
        left: 0;
    }
    25% {
        top: 10%;
        left: 70%;
    }
    50% {
        top: 60%;
        left: 30%;
    }
    75% {
        top: 80%;
        left: 80%;
    }
    100% {
        top: 0%;
        left: 0%;
    }
}


.main::before {
    content: "";
    background-image: url("../images/spider-nest-bg.png");
    background-position: center;
    width: 100%;
    position: fixed;
    background-size: cover;
    height: 100%;
    /*z-index: 9999999999;*/
    overflow: hidden;
}
/*.homepage {*/
/*    background-image: url("../images/loop.gif");*/
/*    background-position: center;*/
/*    background-repeat: no-repeat;*/
/*    background-size: cover;*/
/*    backdrop-filter: blur(55px);*/
/*}*/
.homepage-text h1, .aboutpage-text h1, .contact-page-text h1, .roadmap-page-text h1, .services .services-page-text h1 {
    font-family: "Barlow", sans-serif;
    font-weight: 800;
    font-size: 50px;
    line-height: 64px;
    text-transform: uppercase;
    margin-bottom: 35px;
}

.homepage-text p, .aboutpage-text p, .roadmap .roadmap-page-text p {
    margin-right: 35px;
    line-height: 22px;
    margin-bottom: 35px;
    font-family: "Barlow", sans-serif;
    font-weight: 400;
    font-size: 18px;

}

.homepage-text h1 span, .aboutpage-text h1 span, .roadmap .roadmap-page-text h1 span {
    /*color: #bb8f44;*/
    color: #bb8f44;
}

.homepage-text .join-btn, .sales .sales-outer .sales-btn button {
    border: unset !important;
    background: var(--btn-gradiant-color-background);
    border-radius: 54px;
    padding: 24px 50px;
    font-family: "Barlow", sans-serif;
    font-weight: 400;
    line-height: 100%;
    font-size: 20px;
    color: #ffffff;
}

.top-bar .navbar .navbar-toggler .navbar-toggler-icon {
    filter: contrast(0.0);
}

.footer-bg {
    text-align: center;
}

.footer-bg img {
    position: fixed;
    bottom: 0;
    left: 50%;
    margin-left: -50vw !important;
}

.about .img-about-div::after {
    content: "";
    position: absolute;
    top: 44%;
    left: 204px;
    transform: rotate(351deg);
    width: 609px;
    height: 350px;
    background-image: url("../images/hand-about-section.png");
    background-position: center right;
    background-size: cover;
    background-repeat: no-repeat;
    z-index: 1;

    animation: swayLeftRight 3s ease-in-out infinite;
}

/* Animation keyframes */
@keyframes swayLeftRight {
    0% {
        transform: translateX(0) rotate(351deg);
    }
    50% {
        transform: translateX(20px) rotate(351deg);
    }
    100% {
        transform: translateX(0) rotate(351deg);
    }
}



.about .img-about-div {
    width: 235px;
    position: relative;
    /*z-index: -9;*/
}

.card.custom-card {
    background: #3b2f1b;
    border-radius: 20px;
    width: 37rem;
    height: 100%;
}

.card.custom-card .card-body .card-title::after {
    content: "";
    position: absolute;
    top: 46px;
    left: 18px;
    height: 2px;
    width: 110px;
    /*background-color: #bb8f44;*/
    background-color: #bb8f44;
}

.card.custom-card .card-body .card-title {
    font-family: "Barlow", sans-serif;
    font-weight: 600;
    font-size: 25px;
    color: #ffffff;
    line-height: 25px;
    margin-bottom: 20px;
}

.card.custom-card .card-body .card-text {
    color: #ffffff;
    font-family: "Barlow", sans-serif;
    font-weight: 400;
    font-size: 18px;
    line-height: 25px;

}

.card.custom-card .custom-card-btn {
    /*color: #bb8f44;*/
    color: #bb8f44;
    font-family: "Barlow", sans-serif;
    font-weight: 400;
    font-size: 18px;
    line-height: 18px;
    text-decoration: none;
}

.card-holding-div-about {
    display: flex;
    justify-content: start;
    /* position: absolute; */
    z-index: 2;
    position: relative;
}

.card-holding-div-about .custom-card-outer {
    position: relative;
    top: 58px;
    border-radius: 20px;
    right: 26px;
    background-color: transparent;
    border: 20px solid #0000003d;
}

.contact-us .contact-page-text h1, .roadmap .roadmap-page-text {
    text-align: center;

}

.contact-us {
    background: rgba(0, 0, 0, 0.4);
    padding: 40px 20px;
    border-radius: 20px;
}

.contact-us ul {
    list-style: none;
    padding-left: 0;
}

.contact-us .contact-info h4 {
    padding: 30px 0;
    font-family: "Barlow", sans-serif;
    font-weight: 700;
    font-size: 25px;
    line-height: 100%;
    text-transform: uppercase;
    /*color: #bb8f44;*/
    color: #bb8f44;

}

.contact-us .contact-info ul li i {
    font-size: 25px;
}

.contact-us .contact-info ul li {
    font-family: "Barlow", sans-serif;
    font-weight: 500;
    font-size: 20.67px;
    line-height: 28px;
    margin-bottom: 25px;
    color: #fff;
    display: flex;
    align-items: center;
    gap: 11px;
    align-content: center;
}

.contact-us .contact-info {
    padding: 40px 20px;
    background-color: #7c7c7c1a;
    border-radius: 20px;
    align-content: center;
    height: 100%;
}

.contact-us .form-section .form-control {
    background-color: transparent;
    border: none;
    border-bottom: 2px solid #aaa;
    border-radius: 0;
    color: white;
    padding-right: 0;
    padding-left: 0;
    font-weight: lighter;
}

.contact-us .form-section .form-control::placeholder {
    color: #ffffff;
}

.contact-us .form-section .form-control:focus {
    box-shadow: none;
    border-bottom: 2px solid #fff;
    background-color: transparent;
    color: white;
}

.contact-us .form-section .form-section {
    margin: 0 auto;
    padding: 5px 0;
}

.contact-us .form-section .btn-send {
    text-align: right;

}

.contact-us .form-section .btn-send button {
    background: var(--btn-gradiant-color-background);
    color: white;
    border: none;
    padding: 10px 30px;
    border-radius: 25px;
    transition: all 0.3s ease;
}

.btn-send button:hover {
    box-shadow: 0 0 10px rgba(187, 143, 68, 0.42);
}


.roadmap {
    position: relative;
}

.roadmap .swiper {
    overflow: unset;
}
.roadmap .roadmap-content {
    display: flex;
    justify-content: space-between;
    gap: 40px;
}

.roadmap .roadmap-content .roadmap-wave-img-2 .roadmap-steps {
    position: relative;
    padding-left: 10px;
    height: 233px;
    top: -26px;
    /* margin-right: 10px; */
    max-width: 250px;
    z-index: 9;
    margin: auto;
}

.roadmap .roadmap-content .roadmap-steps {
    position: relative;
    padding-left: 10px;
    height: 190px;
    top: 0;
    /*margin-right: 10px;*/
    max-width: 250px;
    z-index: 9;
    margin: auto;
}

.roadmap .roadmap-content .roadmap-steps::after {
    content: "";
    position: absolute;
    left: -18px;
    bottom: -18px;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background-image: url("../images/menu-puls-img.png");
    background-size: cover;
    background-repeat: no-repeat;
    background-position: center;
    box-shadow: 0 0 10px rgba(255, 255, 255, 0.3);
    animation: shadowPulse 2s infinite ease-in-out;
    z-index: -1;
}

.roadmap .roadmap-content .roadmap-steps::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 2px;
    height: 100%;
    background: linear-gradient(180deg, rgba(115, 115, 115, 0) 0%, #bb8f44 100%);
}

.roadmap .roadmap-content .roadmap-steps .roadmap-desc {
    font-size: 12.23px;
    font-family: "Barlow", sans-serif;
    font-weight: 400;
    line-height: 20px;

}

.roadmap .roadmap-content .roadmap-steps .roadmap-title {
    background: linear-gradient(274.44deg, #bb8f44 0.25%, rgba(65, 0, 31, 0.11) 99.75%);
    padding: 20px 0;
    font-size: 12.23px;
    width: 100%;
    display: block;
    border-radius: 10px;
}


.roadmap .roadmap-page-text {
    margin-bottom: 90px;
}

.slider-btn .carousel-control-next, .slider-btn .carousel-control-prev {
    position: unset !important;
    width: 4%;
}


.slider-btn {
    text-align: end;
    margin-top: 10px;
    display: flex;
    justify-content: end;
    gap: 12px;
}

.roadmap .slider-btn {
    margin-top: 110px;
}

.slider-btn i {
    font-size: 50px;
    font-weight: bold;
    color: #bb8f44;
    cursor: pointer;
}

.services .service-step {
    position: relative;
    padding: 0 60px;
}

.services .service-step h1 {
    font-family: "Barlow", sans-serif;
    font-weight: 700;
    font-size: 175px;
    line-height: 100%;
    text-align: right;
}

.services .service-step h3 {
    font-family: "Barlow", sans-serif;
    font-weight: 700;
    font-size: 30px;
    line-height: 35px;
}

.services .service-step p {
    font-family: "Barlow", sans-serif;
    font-weight: 400;
    font-size: 18px;
    line-height: 25px

}

.services .service-step::after {
    content: "";
    background-size: contain;
    background-position: right;
    height: 361px;
    width: 35px;
    top: 0;
    background-repeat: no-repeat;
    right: 0;
    background-image: url("../images/service-step-img.png");
    position: absolute;
}

.roadmap-content .owl-stage-outer {
    overflow: unset !important;
}

.roadmap .roadmap-content .owl-stage-outer .owl-item:not(.active) {
    opacity: 0.1;
}

.roadmap-content .roadmap-wave-img-1, .roadmap-content .roadmap-wave-img-2, .roadmap-content .roadmap-wave-img-3 {
    position: relative;
    /*width: 100%;*/
    /*height: 100%;*/
    margin: auto;
}

.roadmap-content .roadmap-wave-img-1::after {
    content: "";
    background-image: url("../images/roadmap-wave-1.png");
    background-position: bottom;
    background-size: contain;
    position: absolute;
    width: 562px;
    height: 100%;
    background-repeat: no-repeat;
    bottom: -94px;

    /* 👇 Animation properties */
    animation: moveLeftRight 4s ease-in-out infinite;
}



.roadmap-content .roadmap-wave-img-2::after {
    content: "";
    background-image: url("../images/roadmap-wave-2.png");
    background-position: bottom;
    background-size: contain;
    position: absolute;
    width: 478px;
    right: -22px;
    height: 100%;
    background-repeat: no-repeat;
    bottom: -75px;
    /* 👇 Animation properties */
    animation: moveLeftRight 4s ease-in-out infinite;
}

.roadmap-content .roadmap-wave-img-3::after {
    content: "";
    background-image: url("../images/roadmap-wave-3.png");
    background-position: bottom;
    background-size: contain;
    position: absolute;
    width: 478px;
    right: -70px;
    height: 100%;
    background-repeat: no-repeat;
    bottom: -95px;
    /* 👇 Animation properties */
    animation: moveLeftRight 4s ease-in-out infinite;
}

@keyframes moveLeftRight {
    0% {
        right: 14px;
    }
    50% {
        right: 30px;
    }
    100% {
        right: 14px;
    }
}
.header .offcanvas .offcanvas-header {
    background-color: #000000c4;
}

.header .offcanvas .offcanvas-body {
    padding: 0;
    border-top: 1px solid white;
    border-bottom: 1px solid white;
    background-color: #000000;
}

.header .offcanvas .offcanvas-body #mobile-menu .header-btns {
    flex-direction: column;
}

.header .offcanvas .offcanvas-body #mobile-menu .header-btns .login-btn, .header .offcanvas .offcanvas-body #mobile-menu .header-btns .register-btn {
    background: var(--btn-gradiant-color-background) !important;
    color: #ffffff;
    border-radius: 0;
    padding: 17px 0;
    font-family: "Barlow", sans-serif;
    font-weight: 400;
    line-height: 100%;
    font-size: 16px;
}

.header .offcanvas .offcanvas-body #mobile-menu ul li a {
    padding: 15px;
    height: 100%;
    background-color: #000000;
    display: block;
    text-decoration: none;
    color: white;
}

.header .offcanvas .offcanvas-body #mobile-menu ul li {
    list-style: none;
    margin: 10px 0;
}

.header .offcanvas .offcanvas-body #mobile-menu ul {
    padding: 0;
}

.header .offcanvas .offcanvas-header .btn-close {
    filter: invert(1);
}

.main .header .offcanvas .offcanvas-footer {
    padding: 10px 0;
}

.main .header .offcanvas {
    background-color: #000000;
}

.offcanvas .offcanvas-footer #mobile-social-media-icon ul li {
    list-style-type: none;
}

.offcanvas .offcanvas-footer #mobile-social-media-icon ul {
    padding: 0;
    display: flex;
    justify-content: center;
    gap: 10px;
    margin-bottom: 0;
}

.offcanvas .offcanvas-footer #mobile-social-media-icon ul li a {
    font-size: 35px;
    height: 60px;
    display: flex;
    background: var(--btn-gradiant-color-background);
    color: #ffffff;
    font-weight: 500;
    text-decoration: none;
    justify-content: center;
    width: 60px;
    align-items: center;
    border-radius: 50%;
}

.offcanvas .offcanvas-footer #mobile-social-media-icon ul li a {
    font-size: 20px;
    height: 40px;
    width: 40px;
}

.header .offcanvas .offcanvas-header .logo {
    max-width: 160px;
}

#owl-demo .item {
    margin: 3px;
}

#owl-demo .item img {
    display: block;
    width: 100%;
    height: auto;
}

.img-block img {
    animation: smoothFloat 6s ease-in-out infinite;
}

/* Keyframes for natural floating effect */
@keyframes smoothFloat {
    0% {
        transform: translateY(0);
    }
    25% {
        transform: translateY(-10px);
    }
    50% {
        transform: translateY(-5px);
    }
    75% {
        transform: translateY(-15px);
    }
    100% {
        transform: translateY(0);
    }
}

#particles-js {
    position: fixed;
    width: 100%;
    height: 100%;
    z-index: -9;
    top: 0;
    left: 0;
    background-color: #000;
}
.sales .sales-outer .table tr th, .sales .sales-outer .table tr td {
    width: 50%;
    padding: 20px 0;
    text-transform: uppercase;

}
.sales .sales-outer .table tr td{
    text-align: right;
    font-weight: 800;

}
.sales .sales-outer .table tr span{
    /*color: #bb8f44;*/
    color: #bb8f44;
}
.sales .sales-outer .table tr th{
    text-align: left;
    font-weight: 300;
}
.sales .sales-outer .table  {
    --bs-table-bg: transparent !important;
    --bs-table-color: #ffffff !important;
    border-color: #505050;
}
.sales .sales-timer-text {
    font-size: 20px;
}
.sales .sales-btn {
    text-align: center;
}
.sales .sales-outer p{
    text-align: center;
    font-weight: bold;
}
.sales .sales-outer {
    padding: 40px 20px;
    background-color: #7c7c7c1a;
    border-radius: 20px;
    height: 100%;
}
.timer-container {
    display: flex;
    gap: 30px;
    justify-content: center;
    margin: 30px 0;
}

.time-box {
    background: radial-gradient(circle at center, #1f1f1f 0%, #000000 100%);
    border-radius: 50%;
    width: 100px;
    height: 100px;
    text-align: center;
    color: #ffffff;
    box-shadow: 0 0 10px rgba(187, 143, 68, 0.7);
    display: flex;
    flex-direction: column;
    justify-content: center;
}

.time-box span {
    font-size: 24px;
    font-weight: bold;
}

.time-box p {
    font-size: 14px;
    margin: 0;
    color: #ccc;
}

.progress-container {
    max-width: 600px;
    margin: 30px 0;

}

.progress-container .top-row {
    display: flex;
    justify-content: space-between;
    margin-bottom: 10px;
}

.progress-container .bottom-row .cap {
    position: relative;

}
.progress-container .bottom-row .cap::after {
    content: "";
    position: absolute;
    height: 18px;
    width: 3px;
    right: 50%;
    top: -20px;
    background-color: #999999;
}
/*.progress-container .bottom-row .cap::before {*/
/*    content: "";*/
/*    position: absolute;*/
/*    height: 25px;*/
/*    width: 3px;*/
/*    left: 53px;*/
/*    bottom: 40px;*/
/*    !* width: 80px; *!*/
/*    background-color: #999999;*/
/*}*/
.progress-container .bottom-row {
    display: flex;
    justify-content: space-around;
    margin-bottom: 10px;

}

.progress-container .label {
    font-size: 12px;
    color: #999;
    margin: 0;

}

.progress-container .value {
    font-size: 18px;
    font-weight: bold;
    margin: 0;
}

.progress-container .progress-bar {
    position: relative;
    height: 6px;
    background-color: #333;
    border-radius: 3px;
    overflow: hidden;
    margin: 20px 0;
}

/* Basic reset for default style */
.sales .custom-progress {
    -webkit-appearance: none;
    appearance: none;
    width: 100%;
    height: 20px;
    border-radius: 10px;
    overflow: hidden;
    background-color: #222;
    border: 0;
}

/* Webkit browsers (Chrome, Edge, Safari) */
.sales .custom-progress::-webkit-progress-bar {
    background-color: #222; /* Background color */
    border-radius: 10px;
}

.sales .custom-progress::-webkit-progress-value {
    background: linear-gradient(to right, #bb8f44, #dba548); /* Fill color */
    border-radius: 10px;
}

/* Firefox */
.sales .custom-progress::-moz-progress-bar {
    background: linear-gradient(to right, #bb8f44, #dba548); /* Fill color */
    border-radius: 10px;
    border-color: transparent;
}


.progress-container .cap .cap-value {
    color: #bb8f44;
}
.progress-container .cap p {
    margin: 2px 0;
    font-size: 13px;
    color: #999999;
}

.progress-container .cap-value {
    color: #bb8f44;
    font-weight: bold;
}
.custom-text-left .privacy-text p a {
    text-decoration: none;
    color: #bb8f44;
    border-bottom: 1px dashed #bb8f44;
}
.custom-text-left .privacy-text  {
    display: flex;
    justify-content: left;
    align-items: center;
    align-content: center;
}
.custom-text-left .privacy-text p {
    margin-bottom: 0 !important;
}
.custom-text-left p, .custom-text-left h5 {
    text-align: left !important;
    font-weight: normal !important;
}
.sales-outer.custom-text-left {
    max-height: 75vh;
    overflow-x: auto;
}
@media (min-width: 1200px) and (max-width: 1400px) {
    /*.about .img-about-div {*/
    /*    width: 234px;*/
    /*}*/
    .sales .sales-outer .table tr th, .sales .sales-outer .table tr td {
        padding: 14px 0;
        font-size: 12px;

    }
    .homepage-text .join-btn, .sales .sales-outer .sales-btn button {
        border-radius: 54px;
        padding: 14px 38px;
        font-size: 12px;
    }
    .time-box {
        width: 70px;
        height: 70px;
    }
    .time-box span {
        font-size: 18px;
        font-weight: bold;
    }
    .time-box p {
        font-size: 12px;
    }
    .about .img-about-div::after {
        top: 46%;
        left: 139px;
        transform: rotate(355deg);
        width: 500px;
        height: 298px;
    }
    .homepage-text h1, .aboutpage-text h1, .contact-page-text h1, .roadmap-page-text h1, .services .services-page-text h1 {
        font-size: 40px;
        line-height: 40px;
        margin-bottom: 20px;
    }
    .homepage-text p, .aboutpage-text p, .roadmap .roadmap-page-text p {
        margin-right: 35px;
        line-height: 22px;
        margin-bottom: 20px;
        font-size: 14px;
    }
    .about .img-about-div {
        width: 190px;
        position: absolute;
        top: 15px;
    }
    .card.custom-card .card-body .card-title {
        font-size: 20px;
        line-height: 24px;
        margin-bottom: 12px;
    }
    .card.custom-card .card-body .card-text {
        font-weight: 400;
        font-size: 14px;
        line-height: 18px;
        margin-bottom: 0;
    }
    .card.custom-card {
        border-radius: 15px;
        width: 18rem;
    }
    .card-holding-div-about .custom-card-outer {
        position: relative;
        top: 44px;
        right: 51px;
        border: 15px solid #0000003d;
    }
    .card.custom-card .custom-card-btn {
        font-size: 14px;

    }
    .services .service-step {
        position: relative;
        padding: 0 30px;
    }
    .services .service-step h1 {
        font-size: 110px;

    }
    .services .service-step h3 {
        font-size: 27px;
        line-height: 30px;
    }
    .services .service-step p {
        font-size: 14px;
        line-height: 20px;
    }
    .services .service-step::after {
        height: 314px;
        width: 28px;
        top: 0;
        right: -4px;
    }
    .roadmap .slider-btn {
        margin-top: 60px !important;
    }
    .roadmap-content .roadmap-wave-img-1::after {
        width: 425px;
        right: 64px;
        bottom: -80px;
    }
    .roadmap-content .roadmap-wave-img-3::after {
        width: 425px;
        right: -35px;
        bottom: -88px;
    }
    .roadmap-content .roadmap-wave-img-2::after {
        width: 425px;
        right: 20px;
        height: 100%;
        bottom: -66px;
    }
    .roadmap .roadmap-content .roadmap-wave-img-2 .roadmap-steps {
        height: 213px;
    }
    .page-wrapper::after {
        height: 150px;
        width: 150px;
        right: 608px;
        top: 200px;
    }
}
@media (min-width: 991px) and (max-width: 1400px) {
    .sidebar li {
        position: relative;
        margin: 20px 0;
        padding: 5px 20px;
    }

    .sidebar #menu-icon {
        left: -20px;
        top: 87px;
        width: 40px;
        height: 40px;
    }

    .sidebar {
        margin-top: -165px;

    }

    .sidebar ul {
        list-style: none;
        padding: 20px 0;
        margin: 0;
    }

    .sidebar li a {
        font-size: 14px;
    }

    .sidebar li {
        position: relative;
        margin: 10px 0;
        padding: 15px 50px;
    }

    .sidebar li:has(a.active)::before {
        left: -25px;
        top: 3px;
        width: 50px;
        height: 50px;
    }

    .sidebar::before, .sidebar::after {
        left: -26px;
        width: 50px;
        height: 50px;
    }

    .navbar-brand .logo {
        max-width: 140px;
    }

    .login-btn, .register-btn {
        padding: 17px 30px;
        font-size: 15px;
    }

    .social-media-icon ul li a {
        font-size: 20px;
        height: 40px;
        width: 40px;
    }
}
@media (max-width: 1200px) {
    .img-about-div {
        display: none;
    }

    .contact-us .form-section {
        margin-top: 30px;
    }

    .contact-us .contact-info {
        margin-top: 30px;
    }

    .contact-us .custom-row-reverse {
        flex-direction: column-reverse;
    }
}
@media (max-width: 991px) {
    .sidebar, .social-media-icon {
        display: none;
    }

    .page-wrapper {
        padding: 150px 40px 0 40px;

    }

    .slider-btn {
        text-align: center;
        margin-top: 80px;
        display: flex;
        justify-content: center;
        gap: 30px;
    }

    .page-wrapper::before {
        font-size: 320px;
    }

    .scroller {
        height: unset;
    }
    .top-bg-circle-img {
        width: 170px;
        height: 170px;
    }
}
@media (max-width: 767px) {
    .login-btn, .register-btn {
        padding: 15px 25px;
        font-size: 16px;
    }
    .sales-outer{
        text-align: center;
    }

    .navbar-brand .logo, .header .offcanvas .offcanvas-header .logo {
        max-width: 160px;
    }

    .footer-bg img {
        margin-left: -245px;
    }

    .page-wrapper {
        padding: 110px 40px 0 40px;
    }

    .homepage-text h1, .aboutpage-text h1, .contact-page-text h1, .roadmap-page-text h1, .services .services-page-text h1 {
        font-family: "Barlow", sans-serif;
        font-weight: 800;
        font-size: 28px;
        line-height: 38px;
        text-align: center;
        text-transform: uppercase;
        margin-bottom: 25px;
    }

    .homepage-text p, .aboutpage-text p, .roadmap .roadmap-page-text p {
        margin-right: 0;
        line-height: 22px;
        margin-bottom: 35px;
        text-align: center;
        font-family: "Barlow", sans-serif;
        font-weight: 400;
        font-size: 14px;
    }

    .homepage-text {
        text-align: center;
    }

    .homepage-text .join-btn, .sales .sales-outer .sales-btn button {
        border: unset !important;
        background: var(--btn-gradiant-color-background);
        border-radius: 54px;
        padding: 15px 22px;
        font-family: "Barlow", sans-serif;
        font-weight: 400;
        line-height: 100%;
        font-size: 14px;
        color: #ffffff;
    }

    .homepage .img-block {
        margin-top: 20px;
    }

    .card.custom-card .card-body .card-title {
        font-size: 16px;
        margin-bottom: 16px;
    }

    .card.custom-card .card-body .card-text {
        font-size: 14px;
    }

    .card.custom-card .custom-card-btn {
        font-size: 14px;
    }

    .card.custom-card {
        background: #D9D9D91A;
        border-radius: 20px;
        width: 14rem;
    }

    .card-holding-div-about .custom-card-outer {
        position: relative;
        top: 15px;
        border-radius: 20px;
        right: 0;
    }

    .services .service-step::after {
        height: 330px;
        width: 30px;
        top: 0;
        right: 0;
    }

    .services .service-step {
        padding: 0 35px;
    }

    .services .service-step h1 {
        font-size: 120px;
        line-height: 100%;
    }

    .services .service-step h3 {
        font-size: 24px;
        line-height: 35px;
    }

    .services .service-step p {
        font-size: 14px;
        line-height: 20px;
    }

    .page-wrapper::before {
        font-size: 120px;
    }
    .page-wrapper {
        padding: 90px 0 0 0;
    }
    section {
        min-height: unset !important;
        margin: 30px 0;
    }
}
@media (max-width: 500px) {
    .top-bg-circle-img {
        width: 70px;
        height: 70px;
        top: 52px;
        left: 10px;
    }
    .time-box {
        width: 60px;
        height: 60px;
    }
    .time-box span {
        font-size: 12px;
        font-weight: bold;
    }
    .time-box p {
        font-size: 10px;
    }
    .roadmap .slider-btn {
        margin-top: 66px;
    }
    .services .slider-btn {
        margin-top: 30px;
    }
    .navbar-brand .logo, .header .offcanvas .offcanvas-header .logo {
        max-width: 110px;
    }

    .header-btns {
        gap: 10px;
    }

    .login-btn, .register-btn {
        padding: 10px 18px;
        font-size: 14px;
    }

    .login-btn, .register-btn {
        border-radius: 16px;

    }



    .login-btn, .register-btn {
        padding: 10px 11px;
        font-size: 14px;
    }

    .footer-bg img {
        margin-left: -240px;
    }

    .card-holding-div-about {
        display: block;
        justify-content: center;
        position: unset;
        width: 100%;
        /* margin: auto; */
    }

    .card.custom-card {
        width: 100%;
    }

    .card-holding-div-about .custom-card-outer {
        position: relative;
        top: 15px;
        border-radius: 20px;
        right: 0;
        /* background-color: transparent; */
        border: 0;
    }
}

