<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends \App\Base\Database\MigrationBase
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        $this->schema->create('schedule_stakings', function (\App\Base\Database\BlueprintBase $table) {
            $table->id();
            $table->bigInteger('ref_no')->unique();
            $table->bigInteger('user_id')->nullable()->index();
            $table->bigInteger('user_stake_id')->nullable()->index();
            $table->string('request_hash')->nullable()->unique();
            $table->string('stake_hash')->nullable()->unique();
            $table->decimal('amount',48,8)->default(\App\Constants\CommonConstants::NO)->index();
            $table->integer('is_processed')->default(\App\Constants\CommonConstants::NO)->index();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('schedule_stakings');
    }
};
