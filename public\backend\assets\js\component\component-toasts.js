/*! For license information please see component-toasts.js.LICENSE.txt */
"use strict";document.addEventListener("DOMContentLoaded",(function(){const t=document.getElementById("liveToast1Btn"),e=document.getElementById("liveToast1");if(t){const n=bootstrap.Toast.getOrCreateInstance(e);t.addEventListener("click",(()=>{n.show()}))}const n=document.getElementById("liveToast2Btn"),o=document.getElementById("liveToast2");if(n){const t=bootstrap.Toast.getOrCreateInstance(o);n.addEventListener("click",(()=>{t.show()}))}const s=document.getElementById("liveToast3Btn"),c=document.getElementById("liveToast3");if(s){const t=bootstrap.Toast.getOrCreateInstance(c);s.addEventListener("click",(()=>{t.show()}))}})),$(window).on("load",(function(){}));