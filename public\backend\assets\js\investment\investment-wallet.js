/*! For license information please see investment-wallet.js.LICENSE.txt */
"use strict";document.addEventListener("DOMContentLoaded",(function(){window.randomScalingFactor=function(){return Math.round(20*Math.random())};var a=document.getElementById("areachartblue1").getContext("2d"),o=a.createLinearGradient(0,0,0,300);o.addColorStop(0,"rgba(0, 73, 232, 1)"),o.addColorStop(1,"rgba(0, 73, 232, 0)");var r=a.createLinearGradient(0,0,0,280);r.addColorStop(0,"rgba(0, 73, 232, 0.5)"),r.addColorStop(1,"rgba(0, 73, 232, 0)");var n={type:"bar",data:{labels:["1","2","3","4","5","7","8","9","10","11","12"],datasets:[{label:"# of Votes",data:[randomScalingFactor(),randomScalingFactor(),randomScalingFactor(),randomScalingFactor(),randomScalingFactor(),randomScalingFactor(),randomScalingFactor(),randomScalingFactor(),randomScalingFactor(),randomScalingFactor(),randomScalingFactor(),randomScalingFactor()],radius:0,backgroundColor:o,borderColor:"#015EC2",borderWidth:0,borderRadius:4,fill:!0,tension:.5},{label:"# of Votes",data:[randomScalingFactor(),randomScalingFactor(),randomScalingFactor(),randomScalingFactor(),randomScalingFactor(),randomScalingFactor(),randomScalingFactor(),randomScalingFactor(),randomScalingFactor(),randomScalingFactor(),randomScalingFactor(),randomScalingFactor()],radius:0,backgroundColor:r,borderColor:"#015EC2",borderWidth:0,borderRadius:4,fill:!0,tension:.5}]},options:{maintainAspectRatio:!1,plugins:{legend:{display:!1},tooltip:{enabled:!0}},scales:{y:{display:!0,beginAtZero:!0},x:{display:!0}}}},t=new Chart(a,n);setInterval((function(){n.data.datasets.forEach((function(a){a.data=a.data.map((function(){return randomScalingFactor()}))})),t.update()}),3e3)}));