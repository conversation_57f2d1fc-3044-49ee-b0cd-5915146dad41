/*! For license information please see investment-goals.js.LICENSE.txt */
"use strict";document.addEventListener("DOMContentLoaded",(function(){if($("#circleprogressgreen1").length>0){var t=new ProgressBar.Circle(circleprogressgreen1,{color:"#000000",strokeWidth:10,trailWidth:10,easing:"easeInOut",trailColor:"rgba(126, 170, 0, 0.15)",duration:1400,text:{autoStyleContainer:!1},from:{color:"#6faa00",width:10},to:{color:"#6faa00",width:10},step:function(t,e){e.path.setAttribute("stroke",t.color),e.path.setAttribute("stroke-width",t.width);var r=Math.round(100*e.value());0===r?e.setText(""):e.setText(r+"<small>%<small>")}});t.text.style.fontSize="24px",t.animate(.1)}if($("#circleprogressblue2").length>0){var e=new ProgressBar.Circle(circleprogressblue2,{color:"#FFFFFF",strokeWidth:10,trailWidth:10,easing:"easeInOut",trailColor:"rgba(255, 255, 255, 0.2)",duration:1400,text:{autoStyleContainer:!1},from:{color:"#FFFFFF",width:10},to:{color:"#FFFFFF",width:10},step:function(t,e){e.path.setAttribute("stroke",t.color),e.path.setAttribute("stroke-width",t.width);var r=Math.round(100*e.value());0===r?e.setText(""):e.setText(r+"<small>%<small>")}});e.text.style.fontSize="24px",e.animate(.5)}if($("#circleprogressblue").length>0){var r=new ProgressBar.Circle(circleprogressblue,{color:"#000000",strokeWidth:10,trailWidth:10,easing:"easeInOut",trailColor:"rgba(0, 73, 232, 0.15)",duration:1400,text:{autoStyleContainer:!1},from:{color:"#0049e8",width:10},to:{color:"#0049e8",width:10},step:function(t,e){e.path.setAttribute("stroke",t.color),e.path.setAttribute("stroke-width",t.width);var r=Math.round(100*e.value());0===r?e.setText(""):e.setText(r+"<small>%<small>")}});r.text.style.fontSize="24px",r.animate(.75)}if($("#circleprogressblue1").length>0){var o=new ProgressBar.Circle(circleprogressblue1,{color:"#000000",strokeWidth:10,trailWidth:10,easing:"easeInOut",trailColor:"rgba(0, 73, 232, 0.15)",duration:1400,text:{autoStyleContainer:!1},from:{color:"#0049e8",width:10},to:{color:"#0049e8",width:10},step:function(t,e){e.path.setAttribute("stroke",t.color),e.path.setAttribute("stroke-width",t.width);var r=Math.round(100*e.value());0===r?e.setText(""):e.setText(r+"<small>%<small>")}});o.text.style.fontSize="24px",o.animate(.85)}if($("#circleprogressblue3").length>0){var i=new ProgressBar.Circle(circleprogressblue3,{color:"#000000",strokeWidth:10,trailWidth:10,easing:"easeInOut",trailColor:"rgba(0, 73, 232, 0.15)",duration:1400,text:{autoStyleContainer:!1},from:{color:"#0049e8",width:10},to:{color:"#0049e8",width:10},step:function(t,e){e.path.setAttribute("stroke",t.color),e.path.setAttribute("stroke-width",t.width);var r=Math.round(100*e.value());0===r?e.setText(""):e.setText(r+"<small>%<small>")}});i.text.style.fontSize="24px",i.animate(.65)}}));