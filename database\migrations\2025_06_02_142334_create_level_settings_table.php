<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends \App\Base\Database\MigrationBase
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        $this->schema->create('level_settings', function (\App\Base\Database\BlueprintBase $table) {
            $table->id();
            $table->double('incentive')->comment('in %');
            $table->integer('directs')->comment('active');
            $table->integer('opened_levels');
            $table->double('direct_business')->comment('in USD');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('level_settings');
    }
};
