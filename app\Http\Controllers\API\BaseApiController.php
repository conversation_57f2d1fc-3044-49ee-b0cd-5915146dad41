<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;

class BaseApiController extends Controller
{
    /**
     * success response method.
     *
     * @return \Illuminate\Http\Response
     */
    public function sendResponse($result, $message=null)
    {
        $response = [
            'success' => true,
            'response' => $result,
        ];
        if($message) {
            $response['message'] = $message;
        }

        return response()->json($response, 200);
    }

    /**
     * return error response.
     *
     * @return \Illuminate\Http\Response
     */
    public function sendError($error, $errorMessages = [], $code = 404)
    {
        $response = [
            'success' => false,
            'message' => $error,
        ];

        if(!empty($errorMessages)){
            $response['data'] = $errorMessages;
        }

        return response()->json($response, $code);
    }

    public function fetchPaginationStats($result) {
        try {
            return [
                'total'=>$result->total(),
                'per_page'=>$result->perPage(),
                'current_page'=>$result->currentPage(),
                'last_page'=>$result->lastPage(),
            ];
        } catch (\Exception $e) {
            return [];
        }
    }
}
