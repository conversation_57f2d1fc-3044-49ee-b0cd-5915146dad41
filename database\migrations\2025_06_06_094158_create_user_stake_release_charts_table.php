<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends \App\Base\Database\MigrationBase
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        $this->schema->create('user_stake_release_charts', function (\App\Base\Database\BlueprintBase $table) {
            $table->id();
            $table->bigInteger('ref_no')->unique();
            $table->integer('user_id')->nullable()->index();
            $table->integer('user_stake_id')->nullable()->index();
            $table->decimal('tokens',48,8)->index();
            $table->string('txn_hash')->nullable()->unique();
            $table->date('release_date')->nullable()->index();
            $table->integer('is_released')->default(\App\Constants\CommonConstants::NO)->index();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('user_stake_release_charts');
    }
};
