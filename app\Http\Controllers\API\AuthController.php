<?php

namespace App\Http\Controllers\API;

use App\Components\Helper;
use App\Components\Web3NodeBackend;
use App\Constants\CommonConstants;
use App\Http\Controllers\Controller;
use App\Models\BlockchainTransaction;
use App\Models\LevelSetting;
use App\Models\RankSetting;
use App\Models\Setting;
use App\Models\Transaction;
use App\Models\TransactionType;
use App\Models\User;
use App\Models\UserDbLog;
use App\Models\UserStake;
use App\Models\UserStakeReferralChart;
use App\Models\UserStakeReleaseChart;
use App\Models\UserTicket;
use App\Models\UserTicketMessage;
use App\Models\UserWithdrawal;
use App\Models\Wallet;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use PHPUnit\Framework\Attributes\Ticket;

class AuthController extends Controller
{
    const DEFAULT_MESSAGE=['error'=>'Invalid API credentials or request'];

    // User Login API
    public function login(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'hash' => 'required|string|max:255',
        ]);

        if ($validator->fails()) {
            return response()->json(['error' => $validator->errors()]);
        }

        $hash=$request->input('hash');
        $address=Web3NodeBackend::verifyLoginHash($hash);
        if(!$address) {
            return response()->json([
                'error'=> 'Invalid verification hash',
            ]);
        }

        $user = User::findByAddress($address);
        if(!$user) {
            if(!$request->input('sponsor')) {
                return response()->json([
                    'success' => true,
                    'status' => 'new_user',
                    'address' => $address,
                    'hash' => $hash,
                    'message' => 'You have to register first',
                ]);
            } else {
                $validator = Validator::make($request->all(), [
                    'name' => 'required|string|max:255',
                    'sponsor' => 'required|string|max:255',
                    'email' => 'required|string|email|max:255',
                    'direct' => 'nullable|string|max:255',
                ]);

                if ($validator->fails()) {
                    return response()->json(['error' => $validator->errors()]);
                }

                $sponsor=User::findBySponsorCode($request->input('sponsor'));
                if(!$sponsor) {
                    return response()->json([
                        'error'=> 'Invalid sponsor code',
                    ]);
                }

                // Validate direct sponsor if provided
                $directSponsor = null;
                if($request->has('direct') && $request->input('direct') != '') {
                    $directSponsor = User::findBySponsorCode($request->input('direct'));
                    if(!$directSponsor) {
                        return response()->json([
                            'error'=> 'Invalid direct code',
                        ]);
                    }
                }

                // Validate relationship: sponsor should be a child of direct user
                if($sponsor && $directSponsor) {
                    if(!$sponsor->isDescendantOf($directSponsor)) {
                        return response()->json([
                            'error'=> 'Invalid relationship: Sponsor must be a descendant of the direct user',
                        ]);
                    }
                }

                // Get last children of any first child of the sponsor user
                $lastChildrenOfSponsor = null;
                if($sponsor && $directSponsor) {
                    $lastChildrenOfSponsor = $sponsor->getLastChildrenOfFirstChildren();
                }

                if($sponsor) {
                    try {
                        $sponsorCode=trim($request->input('sponsor'));
                        if($lastChildrenOfSponsor) {
                            $sponsorCode=trim($lastChildrenOfSponsor->referral_code);
                        }

                        $userData = [
                            'name' => trim($request->input('name')),
                            'wallet_address' => trim($address),
                            'password' => trim($address),
                            'referred_by' => $sponsorCode,
                            'email' => trim($request->input('email')),
                        ];

                        // Add direct_id if direct sponsor is provided
                        if($directSponsor) {
                            $userData['direct_id'] = $directSponsor->id;
                        }

                        $user = User::create($userData);
                        if($user) {
                            $user=User::findByAddress($address);
                        }
                    } catch (\Exception $e) {
                        return response()->json([
                            'error'=> $e->getMessage(),
                        ]);
                    }
                }
            }
        }

        if($user->status!=CommonConstants::STATUS_ACTIVE) {
            return response()->json([
                'error' => 'Account blocked. Contact support for more details.',
            ]);
        }
        if($user->user_role_id!=User::ROLE_USER) {
            return response()->json([
                'error' => 'Not authorized for login into this panel',
            ]);
        }
        if(!$user->is_binary_created) {
            return response()->json([
                'success' => true,
                'status' => 'account_setup',
                'address' => $address,
                'hash' => $hash,
                'message' => 'Please wait.. Account setup is in progress.',
            ]);
        }
        $token = $user->createToken('MyAppToken')->plainTextToken;

        $responseData = [
            'success' => true,
            'status' => 'login',
            'message' => 'Login successfully',
            'token' => $token,
            'user' => $user->apiData(),
        ];

        // Add last children information if available
        /* if(isset($lastChildrenOfSponsor) && !empty($lastChildrenOfSponsor)) {
            $responseData['sponsor_last_children'] = array_map(function($child) {
                return [
                    'id' => $child->id,
                    'referral_code' => $child->referral_code,
                    'username' => $child->username,
                    'wallet_address' => $child->wallet_address,
                ];
            }, $lastChildrenOfSponsor);
        } */

        return response()->json($responseData);
    }
    public function saveHash(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'hash' => 'required|string|max:255',
        ]);

        if ($validator->fails()) {
            return response()->json(['error' => $validator->errors()]);
        }

        $hash=$request->input('hash');
        $result=BlockchainTransaction::saveNewTransaction($hash);
        if($result) {
            return response()->json([
                'success' => true,
                'response' => $result->ref_no,
            ]);
        } else {
            return response()->json(['error' => ['hash'=>['Transaction hash already exist']]]);
        }
    }
    public function validateSponsor(Request $request) {
        $validator = Validator::make($request->all(), [
            'code' => 'required|string|max:255',
        ]);

        if ($validator->fails()) {
            return response()->json(['error' => $validator->errors()]);
        }

        $sponsor=$request->input('code');
        $sponsorDetails=User::findBySponsorCode($sponsor);
        if($sponsorDetails) {
            return response()->json([
                'success' => true,
                'sponsor' => [
                    'code'=>$sponsorDetails->referral_code,
                    'address'=>$sponsorDetails->wallet_address,
                    'short_address'=>User::shortAddress($sponsorDetails->wallet_address)
                ],
            ]);
        } else {
            return response()->json([
                'error' => 'Invalid sponsor code',
            ]);
        }
    }

    // User API (Protected)
    public function user(Request $request)
    {
        return response()->json([
            'success' => true,
            'user' => $request->user()->apiData(),
        ]);
    }
    public function userDbLogs(Request $request)
    {
        $log=[];
        $identity=$request->user();
        $checkLog=UserDbLog::query()
            ->where('user_id','=',$identity->id)
            ->orderByDesc("id")->first();
        if($checkLog) {
            try {
                $decode=json_decode($checkLog->log_data,true);
                if(is_array($decode)) {
                    $log=$decode;
                }
            } catch (\Exception $e) {
                $log=[];
            }
        }
        return response()->json([
            'success' => true,
            'data' => $log,
        ]);
    }
    public function userWallets(Request $request)
    {
        $response=[];
        $identity=$request->user();
        $wallets=Wallet::query()->orderBy('id')->get();
        if(count($wallets)>0) {
            foreach ($wallets as $wallet) {
                $userWallet=$identity->getUserWallet($wallet->id);
                $response[]=[
                    'id'=>$wallet->id,
                    'name'=>$wallet->name,
                    'balance'=>$userWallet->balance,
                    'formattedBalance'=>Helper::printNumber($userWallet->balance,CommonConstants::USD_PRECISION),
                ];
            }
        }
        return response()->json([
            'success' => true,
            'response' => $response,
        ]);
    }
    public function dashboardStats(Request $request)
    {
        $response=[];
        $identity=$request->user();
        $response['user']=$identity->apiData();
        $response['team']=[
            'total_team'=>$identity->total_team,
            'total_directs'=>$identity->total_directs,
            'active_directs'=>$identity->active_directs,
            'team_business'=>Helper::printAmount($identity->team_business),
            'current_month_business'=>Helper::printAmount($identity->current_month_business),
        ];
        $response['stakes']=[];
        $userStakes=UserStake::query()
            ->where('user_id','=',$identity->id)
            ->where('is_active','=',CommonConstants::YES)
            ->orderByDesc("investment_counter")->get();
        if(count($userStakes)>0) {
            foreach ($userStakes as $userStake) {
                $response['stakes'][]=$userStake->apiData();
            }
        }
        return response()->json([
            'success' => true,
            'response' => $response,
        ]);
    }
    public function stakedSchedule(Request $request)
    {
        $response=[];
        $identity=$request->user();
        $pendingRows=UserStakeReleaseChart::query()
            ->where('user_id','=',$identity->id)
            ->where('is_released','=',CommonConstants::NO)
            ->orderBy("release_date")
            ->get();
        if(count($pendingRows)>0) {
            foreach ($pendingRows as $pendingRow) {
                $response[]=[
                    'date'=>$pendingRow->release_date,
                    'tokens'=>Helper::printNumber($pendingRow->tokens,CommonConstants::TOKEN_PRECISION),
                ];
            }
        }
        return response()->json([
            'success' => true,
            'user_id'=>$identity->id,
            'response' => $response,
        ]);
    }
    public function referralSchedule(Request $request)
    {
        $response=[];
        $identity=$request->user();
        $pendingRows=UserStakeReferralChart::query()
            ->where('user_id','=',$identity->id)
            ->where('is_released','=',CommonConstants::NO)
            ->orderBy("release_date")
            ->get();
        if(count($pendingRows)>0) {
            foreach ($pendingRows as $pendingRow) {
                $response[]=[
                    'date'=>$pendingRow->release_date,
                    'tokens'=>Helper::printNumber($pendingRow->tokens,CommonConstants::TOKEN_PRECISION),
                ];
            }
        }
        return response()->json([
            'success' => true,
            'user_id'=>$identity->id,
            'response' => $response,
        ]);
    }
    public function userStakes(Request $request)
    {
        $response=[];
        $identity=$request->user();
        $userStakes=UserStake::query()
            ->where('user_id','=',$identity->id)
            ->orderByDesc("investment_counter")->get();
        if(count($userStakes)>0) {
            foreach ($userStakes as $userStake) {
                $response[]=$userStake->apiData();
            }
        }
        return response()->json([
            'success' => true,
            'response' => $response,
        ]);
    }
    public function userWithdrawals(Request $request)
    {
        $response=[];
        $identity=$request->user();
        $rows=UserWithdrawal::query()
            ->where('user_id','=',$identity->id)
            ->orderByDesc("id")->get();
        if(count($rows)>0) {
            foreach ($rows as $row) {
                $response[]=$row->apiData();
            }
        }
        return response()->json([
            'success' => true,
            'response' => $response,
        ]);
    }
    public function userTransactions(Request $request)
    {
        $response=[];
        $identity=$request->user();
        $rows=Transaction::query()
            ->where('user_id','=',$identity->id)
            ->orderByDesc("id")->limit(50)->get();
        if(count($rows)>0) {
            foreach ($rows as $row) {
                $response[]=$row->apiData();
            }
        }
        return response()->json([
            'success' => true,
            'response' => $response,
        ]);
    }
    public function userDirects(Request $request)
    {
        $response=[];
        $identity=$request->user();
        if($request->input('code')) {
            $code=trim($request->input('code'));
            $userDetails=User::findBySponsorCode($code);
            if(!$userDetails) {
                return response()->json([
                    'success' => true,
                    'response' => $response,
                ]);
            } else {
                if($userDetails->left_leg>$identity->left_leg && $userDetails->right_leg<$identity->right_leg) {
                    $identity=$userDetails;
                } else {
                    return response()->json([
                        'success' => true,
                        'response' => $response,
                    ]);
                }
            }
        }
        $directs=User::query()
            ->where('referred_by','=',$identity->id)
            ->orderBy("id")->get();
        if(count($directs)>0) {
            foreach ($directs as $direct) {
                $response[]=$direct->apiData();
            }
        }
        return response()->json([
            'success' => true,
            'response' => $response,
        ]);
    }
    public function userTree(Request $request)
    {
        $identity=$request->user();
        if($request->input('code')) {
            $code=trim($request->input('code'));
            $userDetails=User::findBySponsorCode($code);
            if($userDetails) {
                if($userDetails->left_leg>$identity->left_leg && $userDetails->right_leg<$identity->right_leg) {
                    $identity=$userDetails;
                }
            }
        }
        $response=$identity->createTreeData();

        return response()->json([
            'success' => true,
            'response' => $response,
        ]);
    }
    public function userTickets(Request $request)
    {
        $response=[];
        $identity=$request->user();
        $rows=UserTicket::query()
            ->where('user_id','=',$identity->id)
            ->orderBy("id")->get();
        if(count($rows)>0) {
            foreach ($rows as $row) {
                $response[]=$row->apiData();
            }
        }
        return response()->json([
            'success' => true,
            'response' => $response,
        ]);
    }
    public function levelWiseBusiness(Request $request)
    {
        $response=[];
        $identity=$request->user();
        $level=1;
        if($request->input('level')) {
            $level=(int)$request->input('level');
            if($level<=0) {
                $level=1;
            }
        }
        $levelBusiness=$identity->getLevelWiseBusiness($level);
        $response['business']=$levelBusiness;
        $response['formattedBusiness']=Helper::printAmount($levelBusiness);
        return response()->json([
            'success' => true,
            'response' => $response,
        ]);
    }
    public function openedLevels(Request $request)
    {
        $response=[];
        $identity=$request->user();
        $rows=LevelSetting::query()->orderBy("id")->get();
        if(count($rows)>0) {
            $response['stats']=$identity->fetchOpenedLevelLogs();
            $flag=true;
            foreach ($rows as $row) {
                if($identity->opened_levels<$row->opened_levels) {
                    $flag=false;
                }
                $response['levels'][]=[
                    'id'=>$row->id,
                    'incentive'=>$row->incentive.'%',
                    'directs'=>$row->directs,
                    'opened_levels'=>$row->opened_levels,
                    'direct_business'=>Helper::printAmount($row->direct_business),
                    'is_achieved'=>(int)$flag,
                ];
            }
        }
        return response()->json([
            'success' => true,
            'response' => $response,
        ]);
    }
    public function userRank(Request $request)
    {
        $response=[];
        $identity=$request->user();
        $rows=RankSetting::query()->orderBy("id")->get();
        if(count($rows)>0) {
            $response['stats']=$identity->fetchRankLogs();
            $flag=true;
            foreach ($rows as $row) {
                if(!$identity->rank_id || $identity->rank_id<$row->id) {
                    $flag=false;
                }
                $response['ranks'][]=[
                    'name'=>$row->name,
                    'business'=>Helper::printAmount($row->total_business),
                    'incentive'=>Helper::printAmount($row->incentive),
                    'is_achieved'=>(int)$flag,
                    'note'=>'Note: Team business should be in <strong>40 : 40 : 20</strong> ratio',
                ];
            }
        }
        return response()->json([
            'success' => true,
            'response' => $response,
        ]);
    }
    public function monthlyBusiness(Request $request)
    {
        $identity=$request->user();
        $response=$identity->getMonthWiseTeamBusiness();

        return response()->json([
            'success' => true,
            'response' => $response,
        ]);
    }
    public function transactionTypes(Request $request)
    {
        $identity=$request->user();
        $response=[];
        $types=TransactionType::query()->orderBy('id')->get();
        if(count($types)>0) {
            foreach ($types as $type) {
                $response[]=[
                    'id'=>$type->id,
                    'name'=>$type->name,
                ];
            }
        }
        return response()->json([
            'success' => true,
            'response' => $response,
        ]);
    }
    public function validateInvestmentRequest(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'amount' => 'required|numeric',
        ]);

        if ($validator->fails()) {
            return response()->json(['error' => $validator->errors()]);
        }

        $identity=$request->user();
        $amount=round($request->input('amount'),CommonConstants::USD_PRECISION);
        if($amount<=0) {
            return response()->json(['error' => ['amount'=>['Invalid amount entered']]]);
        }
        $minAmount=Setting::getValue(Setting::SETTING_MINIMUM_INVESTMENT);
        if($minAmount>$amount) {
            return response()->json(['error' => ['amount'=>['Minimum amount should be greater than $'.$minAmount]]]);
        }

        $remainingLimit=$identity->getInvestmentRemainingLimit();
        if($amount>$remainingLimit) {
            if($minAmount>$remainingLimit) {
                return response()->json(['error' => ['amount' => ['You can reached the maximum limit']]]);
            } else {
                return response()->json(['error' => ['amount' => ['You can invest upto ' . Helper::printAmount($remainingLimit)]]]);
            }
        }

        return response()->json([
            'success' => true,
            'response' => $amount,
        ]);
    }

    public function validateWithdrawalRequest(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'amount' => 'required|numeric',
        ]);

        if ($validator->fails()) {
            return response()->json(['error' => $validator->errors()]);
        }

        $identity=$request->user();
        $amount=round($request->input('amount'),CommonConstants::USD_PRECISION);
        if($amount<=0) {
            return response()->json(['error' => ['amount'=>['Invalid amount entered']]]);
        }
        $minAmount=Setting::getValue(Setting::SETTING_MINIMUM_WITHDRAWAL);
        if($minAmount>$amount) {
            return response()->json(['error' => ['amount'=>['Minimum amount should be greater than $'.$minAmount]]]);
        }
        $multipleAmount=Setting::getValue(Setting::SETTING_WITHDRAWAL_MULTIPLE);
        if(($amount%$multipleAmount)!==0) {
            return response()->json(['error' => ['amount'=>['Amount should be multiple of $'.$multipleAmount]]]);
        }

        $userWallet=$identity->getUserWallet(Wallet::WALLET_WITHDRAWAL);
        if($amount>$userWallet->balance) {
            return response()->json(['error' => ['amount'=>['Insufficient wallet balance. You have '.Helper::printAmount($userWallet->balance).' in your withdrawal wallet.']]]);
        }

        return response()->json([
            'success' => true,
            'response' => $amount,
        ]);
    }

    public function createTicket(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'subject' => 'required|string|max:255',
            'message' => 'required|string',
        ]);

        if ($validator->fails()) {
            return response()->json(['error' => $validator->errors()]);
        }

        $identity=$request->user();

        $model=new UserTicket();
        $model->user_id=$identity->id;
        $model->subject=trim($request->input('subject'));
        if($model->save()) {
            $messageModel=new UserTicketMessage();
            $messageModel->user_id=$model->user_id;
            $messageModel->user_ticket_id=$model->id;
            $messageModel->message=trim($request->input('message'));
            if($messageModel->save()) {
                $model=UserTicket::query()->where('id','=',$model->id)->first();
                return response()->json([
                    'success' => true,
                    'response' => $model->apiData(),
                ]);
            } else {
                return response()->json(['error' => ['ticket'=>['Something went wrong. Try later.']]]);
            }
        } else {
            return response()->json(['error' => ['ticket'=>['Unable to generate new ticket']]]);
        }
    }

    public function closeTicket(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'ref_no' => 'required|string',
        ]);

        if ($validator->fails()) {
            return response()->json(['error' => $validator->errors()]);
        }

        $identity=$request->user();

        $userTicket=UserTicket::query()
            ->where('user_id','=',$identity->id)
            ->where('ref_no','=',$request->input('ref_no'))
            ->first();
        if(!$userTicket) {
            return response()->json(['error' => ['ref_no'=>['Invalid ticket reference number.']]]);
        } else {
            if($userTicket->is_active) {
                $userTicket->is_active=CommonConstants::NO;
                $userTicket->updated_at=date(CommonConstants::PHP_DATE_FORMAT);
                if(!$userTicket->update(['is_active','updated_at'])) {
                    return response()->json(['error' => ['ref_no'=>['Something went wrong. Try later.']]]);
                }
            }

            return response()->json([
                'success' => true,
                'response' => 'Closed successfully',
            ]);
        }
    }
    public function addTicketMessage(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'ref_no' => 'required|string',
            'message' => 'required|string',
        ]);

        if ($validator->fails()) {
            return response()->json(['error' => $validator->errors()]);
        }

        $identity=$request->user();

        $userTicket=UserTicket::query()
            ->where('user_id','=',$identity->id)
            ->where('ref_no','=',$request->input('ref_no'))
            ->where('is_active','=',CommonConstants::YES)
            ->first();
        if(!$userTicket) {
            return response()->json(['error' => ['ref_no'=>['Invalid ticket reference number or ticket closed.']]]);
        } else {
            $ticketMessage=new UserTicketMessage();
            $ticketMessage->user_id=$identity->id;
            $ticketMessage->user_ticket_id=$userTicket->id;
            $ticketMessage->message=$request->input('message');
            if($ticketMessage->save()) {
                return response()->json([
                    'success' => true,
                    'response' => $userTicket->apiData(),
                ]);
            }
        }

        return response()->json(['error' => ['ref_no'=>['Something went wrong. Try later.']]]);
    }

    // User Logout API
    public function logout(Request $request)
    {
        $request->user()->tokens()->delete();

        return response()->json([
            'success' => true,
            'message' => 'Logout successful.',
        ]);
    }
}
