import {PlaceholderPattern} from '@/components/ui/placeholder-pattern';
import AppLayout from '@/layouts/app-layout';
import {type BreadcrumbItem} from '@/types';
import {Head, Link} from '@inertiajs/react';
import UserLayout from "@/layouts/user/user-layout";
import {Line<PERSON><PERSON>, <PERSON><PERSON><PERSON>} from "@mui/x-charts";
import BreadcrumbsButtons from "@/components/user/breadcrumbButtons";


export default function portReward() {


    return (
        <UserLayout>

            <main className="adminuiux-content has-sidebar">

                <div className="container mt-4">
                    <div className="row gx-3 align-items-center">
                        <div className="col-12 col-sm">
                            <nav aria-label="breadcrumb" className="mb-2">
                                <ol className="breadcrumb mb-0">
                                    <li className="breadcrumb-item bi"><Link href={route('user.dashboard')}><i
                                        className="bi bi-house-door me-1 fs-14"></i> Dashboard</Link></li>
                                    <li className="breadcrumb-item active bi" aria-current="page">Shipping Port Reward</li>
                                </ol>
                            </nav>
                            <h5>Shipping Port Reward</h5>
                        </div>
                        <div className="col-12 col-sm-auto text-end py-3 py-sm-0">
                            <BreadcrumbsButtons/>
                        </div>
                    </div>
                </div>

                <div className="container mt-4" id="main-content" data-bs-spy="scroll" data-bs-target="#list-example"
                     data-bs-smooth-scroll="true">

                    <div className="row" id="list-item-2">
                        <div className="col-12 col-md-3 mb-4">
                            <div className="card adminuiux-card {/*bg-theme-1*/}">
                                <div className="card-body">

                                    <div className="row">
                                        <div className="text-center mb-3">
                                            <h4 className="mb-1 text-center ">
                                                <span className={'avatar avatar-100 bg-theme-1-subtle text-theme-1 rounded '}>$500</span>
                                            </h4>
                                        </div>
                                    </div>

                                    <div className="text-center">
                                        <h6 className="fw-normal">Working Income: <b className={'fw-bold'}>$300</b> </h6>
                                        <h6 className="fw-normal">Countable Working Income: <b className={'fw-bold'}>$3,000</b> </h6>
                                        <button className="btn btn-outline-white text-white border" style={{whiteSpace: 'break-spaces'}}><b className={'fw-bold'}>Note: </b> Working income should be in <b className={'fw-bold'}>100 : 100 </b>ratio</button>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div className="col-12 col-md-3 mb-4">
                            <div className="card adminuiux-card {/*bg-theme-1*/}">
                                <div className="card-body">

                                    <div className="row">
                                        <div className="text-center mb-3">
                                            <h4 className="mb-1 text-center ">
                                                <span className={'avatar avatar-100 bg-theme-1-subtle text-theme-1 rounded '}>$500</span>
                                            </h4>
                                        </div>
                                    </div>

                                    <div className="text-center">
                                        <h6 className="fw-normal">Working Income: <b className={'fw-bold'}>$300</b> </h6>
                                        <h6 className="fw-normal">Countable Working Income: <b className={'fw-bold'}>$3,000</b> </h6>
                                        <button className="btn btn-outline-white text-white border" style={{whiteSpace: 'break-spaces'}}><b className={'fw-bold'}>Note: </b> Working income should be in <b className={'fw-bold'}>100 : 100 </b>ratio</button>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div className="col-12 col-md-3 mb-4">
                            <div className="card adminuiux-card {/*bg-theme-1*/}">
                                <div className="card-body">

                                    <div className="row">
                                        <div className="text-center mb-3">
                                            <h4 className="mb-1 text-center ">
                                                <span className={'avatar avatar-100 bg-theme-1-subtle text-theme-1 rounded '}>$500</span>
                                            </h4>
                                        </div>
                                    </div>

                                    <div className="text-center">
                                        <h6 className="fw-normal">Working Income: <b className={'fw-bold'}>$300</b> </h6>
                                        <h6 className="fw-normal">Countable Working Income: <b className={'fw-bold'}>$3,000</b> </h6>
                                        <button className="btn btn-outline-white text-white border" style={{whiteSpace: 'break-spaces'}}><b className={'fw-bold'}>Note: </b> Working income should be in <b className={'fw-bold'}>100 : 100 </b>ratio</button>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div className="col-12 col-md-3 mb-4">
                            <div className="card adminuiux-card {/*bg-theme-1*/}">
                                <div className="card-body">

                                    <div className="row">
                                        <div className="text-center mb-3">
                                            <h4 className="mb-1 text-center ">
                                                <span className={'avatar avatar-100 bg-theme-1-subtle text-theme-1 rounded '}>$500</span>
                                            </h4>
                                        </div>
                                    </div>

                                    <div className="text-center">
                                        <h6 className="fw-normal">Working Income: <b className={'fw-bold'}>$300</b> </h6>
                                        <h6 className="fw-normal">Countable Working Income: <b className={'fw-bold'}>$3,000</b> </h6>
                                        <button className="btn btn-outline-white text-white border" style={{whiteSpace: 'break-spaces'}}><b className={'fw-bold'}>Note: </b> Working income should be in <b className={'fw-bold'}>100 : 100 </b>ratio</button>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div className="col-12 col-md-3 mb-4">
                            <div className="card adminuiux-card {/*bg-theme-1*/}">
                                <div className="card-body">

                                    <div className="row">
                                        <div className="text-center mb-3">
                                            <h4 className="mb-1 text-center ">
                                                <span className={'avatar avatar-100 bg-theme-1-subtle text-theme-1 rounded '}>$500</span>
                                            </h4>
                                        </div>
                                    </div>

                                    <div className="text-center">
                                        <h6 className="fw-normal">Working Income: <b className={'fw-bold'}>$300</b> </h6>
                                        <h6 className="fw-normal">Countable Working Income: <b className={'fw-bold'}>$3,000</b> </h6>
                                        <button className="btn btn-outline-white text-white border" style={{whiteSpace: 'break-spaces'}}><b className={'fw-bold'}>Note: </b> Working income should be in <b className={'fw-bold'}>100 : 100 </b>ratio</button>
                                    </div>
                                </div>
                            </div>
                        </div>

                    </div>

                </div>



            </main>


        </UserLayout>
    );
}
