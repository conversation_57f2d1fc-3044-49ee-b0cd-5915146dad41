<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends \App\Base\Database\MigrationBase
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        $this->schema->create('shift_balance_logs', function (\App\Base\Database\BlueprintBase $table) {
            $table->id();
            $table->bigInteger('ref_no')->unique();
            $table->bigInteger('user_id')->nullable()->index();
            $table->bigInteger('wallet_id')->nullable()->index();
            $table->decimal('amount',48,8)->default(\App\Constants\CommonConstants::NO)->index();
            $table->date('transaction_date')->index();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('shift_balance_logs');
    }
};
