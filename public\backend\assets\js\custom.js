if(document.getElementById('sidebar-toggler')!=null) {
    document.getElementById('sidebar-toggler').addEventListener('click', function () {
        document.body.classList.toggle('sidebar-close');
    });
}

document.querySelectorAll(".dropdown-toggle").forEach(function (toggle) {
    toggle.addEventListener("click", function (e) {
        e.preventDefault();
        e.stopImmediatePropagation();

        const parent = toggle.closest(".dropdown");
        const menu = parent.querySelector(".dropdown-menu");

        document.querySelectorAll(".dropdown.show .dropdown-menu").forEach(function (openMenu) {
            if (openMenu !== menu) {
                openMenu.classList.remove("show");
                openMenu.closest(".dropdown").classList.remove("show");
            }
        });

        if (toggle.classList.contains('close-dropdown')) {
            console.log('removing classes');
            menu.classList.remove("show");
            parent.classList.remove("show");
            toggle.classList.remove('close-dropdown');
        }
        else{
            console.log('adding classes');
            menu.classList.add("show");
            parent.classList.add("show");
            toggle.classList.add('close-dropdown');
        }

    });
});

if(window.innerWidth<768) {
    document.querySelectorAll(".menu-active-line .nav-link").forEach(function (toggle) {
        toggle.addEventListener("click", function (e) {
            document.body.classList.toggle('sidebar-close');
        });
    });
}


