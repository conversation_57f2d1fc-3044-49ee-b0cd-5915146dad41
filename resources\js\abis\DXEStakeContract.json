[{"inputs": [{"internalType": "address", "name": "usdtToken", "type": "address"}], "stateMutability": "nonpayable", "type": "constructor"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "account", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "namespace", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "actionType", "type": "uint256"}, {"indexed": false, "internalType": "bytes32", "name": "data", "type": "bytes32"}], "name": "ActionAuthorized", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "uint256", "name": "timestamp", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "supply", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "stakes", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "burn", "type": "uint256"}], "name": "DayReport", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "previousOwner", "type": "address"}, {"indexed": true, "internalType": "address", "name": "new<PERSON>wner", "type": "address"}], "name": "OwnershipTransferred", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "tokens", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "maturity", "type": "uint256"}], "name": "StakeAdded", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "tokens", "type": "uint256"}], "name": "StakeScheduled", "type": "event"}, {"inputs": [{"internalType": "uint256", "name": "_namespace", "type": "uint256"}, {"internalType": "uint256", "name": "_actionType", "type": "uint256"}, {"internalType": "bytes32", "name": "_data", "type": "bytes32"}], "name": "authorizeAction", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "_amount", "type": "uint256"}], "name": "clean", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "dailyMint", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "_amount", "type": "uint256"}], "name": "depositToken", "outputs": [], "stateMutability": "payable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "_amount", "type": "uint256"}], "name": "depositUSDT", "outputs": [], "stateMutability": "payable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "_of", "type": "address"}], "name": "getAuthorizations", "outputs": [{"components": [{"internalType": "address", "name": "_address", "type": "address"}, {"internalType": "uint256", "name": "_namespace", "type": "uint256"}, {"internalType": "uint256", "name": "_actionType", "type": "uint256"}, {"internalType": "bytes32", "name": "_data", "type": "bytes32"}], "internalType": "struct DrixieStakeContract.Authorization[]", "name": "", "type": "tuple[]"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "getDailyDayStart", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "getDailyReport", "outputs": [{"components": [{"internalType": "uint256", "name": "ts_start", "type": "uint256"}, {"internalType": "uint256", "name": "ts_end", "type": "uint256"}, {"internalType": "uint256", "name": "_stakes", "type": "uint256"}, {"internalType": "uint256", "name": "_burn", "type": "uint256"}, {"internalType": "uint256", "name": "_supply", "type": "uint256"}], "internalType": "struct DrixieStakeContract.DayReportStruct[]", "name": "", "type": "tuple[]"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "getDailyStakes", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "getDailyStakesLeft", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "getDailySupply", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "getDepositAllowed", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "get<PERSON><PERSON><PERSON>T<PERSON>", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "getOtherToken", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "_address", "type": "address"}], "name": "getStakes", "outputs": [{"components": [{"internalType": "address", "name": "_address", "type": "address"}, {"internalType": "uint256", "name": "_maturity", "type": "uint256"}, {"internalType": "uint256", "name": "_tokens", "type": "uint256"}, {"internalType": "uint256", "name": "_usdt", "type": "uint256"}, {"internalType": "bool", "name": "_matured", "type": "bool"}, {"internalType": "uint256", "name": "_depositType", "type": "uint256"}], "internalType": "struct DrixieStakeContract.Stake[]", "name": "", "type": "tuple[]"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "getUSDToken", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "owner", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "_address", "type": "address"}, {"internalType": "uint256", "name": "_stake", "type": "uint256"}, {"internalType": "uint256", "name": "_usdtTokens", "type": "uint256"}, {"internalType": "uint256", "name": "_depositType", "type": "uint256"}], "name": "registerStake", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "renounceOwnership", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "_amount", "type": "uint256"}], "name": "scheduleDepositUSDT", "outputs": [], "stateMutability": "payable", "type": "function"}, {"inputs": [{"internalType": "address payable", "name": "_to", "type": "address"}, {"internalType": "uint256", "name": "_amount", "type": "uint256"}], "name": "send<PERSON>oin", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "_tokenAddr", "type": "address"}, {"internalType": "address payable", "name": "_to", "type": "address"}, {"internalType": "uint256", "name": "_amount", "type": "uint256"}], "name": "sendToken", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bool", "name": "_value", "type": "bool"}], "name": "setDepositAllowed", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "d<PERSON><PERSON><PERSON><PERSON>", "type": "address"}], "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "_addr", "type": "address"}], "name": "setOtherToken", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "_addr", "type": "address"}], "name": "setUSDToken", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "new<PERSON>wner", "type": "address"}], "name": "transferOwnership", "outputs": [], "stateMutability": "nonpayable", "type": "function"}]