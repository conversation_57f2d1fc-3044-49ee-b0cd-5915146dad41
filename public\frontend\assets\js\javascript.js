
function cloneMenuForMobile() {
    const desktopMenu = document.getElementById('desktop-menu');
    const mobileMenu = document.getElementById('mobile-menu');
    const mobileSocialMediaIcon = document.getElementById('mobile-social-media-icon');
    const desktopSocialMediaIcon = document.getElementById('desktop-social-media-icon');
    const desktopLoginRegsbtns = document.getElementById('desktop-login-regs-btns');
    if (mobileSocialMediaIcon !=null && mobileMenu != null){
    if (window.innerWidth <= 991 && mobileMenu.children.length === 0 && mobileSocialMediaIcon.children.length === 0) {
        const clonedMenu = desktopMenu.querySelector('ul').cloneNode(true);
        const ClonedesktopSocialMediaIcon = desktopSocialMediaIcon.querySelector('ul').cloneNode(true);
        const CloneDesktopLoginRegsbtns = desktopLoginRegsbtns.querySelector('.header-btns').cloneNode(true);
        mobileMenu.appendChild(clonedMenu);
        mobileSocialMediaIcon.appendChild(ClonedesktopSocialMediaIcon);
        // mobileMenu.appendChild(CloneDesktopLoginRegsbtns);
    }
}
}

cloneMenuForMobile();

window.addEventListener('resize', () => {
    const mobileMenu = document.getElementById('mobile-menu');
    const mobileSocialMediaIcon = document.getElementById('mobile-social-media-icon');

    if (window.innerWidth <= 991 && mobileMenu.children.length === 0 && mobileSocialMediaIcon.children.length === 0) {
        cloneMenuForMobile();
    }
});



const sections = document.querySelectorAll('section');
const navLinks = document.querySelectorAll('#menu-list li a');

const observer = new IntersectionObserver((entries) => {
    entries.forEach(entry => {
        if (entry.isIntersecting) {
            navLinks.forEach(link => {
                link.classList.remove('active');
                if (link.getAttribute('href').substring(1) === entry.target.id) {

                    link.classList.add('active');

                    moveMenuIcon();
                }
            });
        }
    });
}, {
    threshold: 0.5
});

sections.forEach(section => {
    observer.observe(section);
});

const menuLinks = document.querySelectorAll('#menu-list li a');

menuLinks.forEach(link => {
    link.addEventListener('click', function () {
        console.log('testing');
        menuLinks.forEach(list => list.classList.remove('active'));
        this.classList.add('active');
        // console.log(link.getAttribute('href').substring(1),'dsd');
        document.getElementById(link.getAttribute('href').substring(1)).scrollIntoView({
            behavior: "auto" // or "auto"
        });
        moveMenuIcon();
    });
});


function moveMenuIcon(){
    const sidebar = document.getElementById('desktop-menu');
    const menuIcon = document.getElementById('menu-icon');
    const activeLink = sidebar.querySelector('a.active');

    if (activeLink) {
        const sidebarRect = sidebar.getBoundingClientRect();
        const activeRect = activeLink.getBoundingClientRect();

        const relativeTop = activeRect.top - sidebarRect.top;
        const relativeLeft = activeRect.left - sidebarRect.left;

        menuIcon.style.top=(relativeTop-23)+"px";
        console.log('Active Link Position Inside Sidebar:');
        console.log('Top:', relativeTop, 'px');

    }

}
document.querySelectorAll('.offcanvas-link').forEach(link => {
    link.addEventListener('click', function () {
        const sidebarElement = document.getElementById('offcanvasWithBothOptions');
        const offcanvas = bootstrap.Offcanvas.getInstance(sidebarElement);
        if (offcanvas) {
            offcanvas.hide();
        }
        console.log('hide');
    });
});




class WavesCanvas {
    constructor(elm, options = {}) {
        this.canvas = elm;

        if (!this.canvas) return;

        const data = this.canvas.dataset;

        this.settings = {
            waveCount: parseInt(data.waveCount) || options.waveCount || 9,
            amplitude: parseInt(data.amplitude) || options.amplitude || 50,
            baseSpeed: parseFloat(data.baseSpeed) || options.baseSpeed || 0.015,
            waveSpacing: parseInt(data.waveSpacing) || options.waveSpacing || 30,
            baseColor: data.baseColor ? data.baseColor.split(',').map(Number) : options.baseColor || [255, 255, 255],
            lineWidth: parseInt(data.lineWidth) || options.lineWidth || 1,
            direction: data.direction || options.direction || "right",
            leftOffset: data.leftOffset || options.leftOffset || 0,
            rightOffset: data.rightOffset || options.rightOffset || 0,
        };

        this.ctx = this.canvas.getContext("2d");
        this.waves = [];

        this.init();
    }
    resizeCanvas() {
        const rect = this.canvas.getBoundingClientRect();
        this.canvas.width = rect.width;
        this.canvas.height = rect.height;
        this.waves.forEach((wave) => wave.updateOffset());

    }

    Wave = class {
        constructor(index, settings, canvas) {
            this.index = index;
            this.phase = index * 0.3;
            this.settings = settings;
            this.canvas = canvas;
            /*this.color = `rgba(${settings.baseColor[0]}, ${
                settings.baseColor[1]
            }, ${settings.baseColor[2]}, ${1 - (this.index / this.settings.waveCount)})`;*/
            const r = 187;
            const g = 143;
            const b = 68;
            const alpha = 1 - (this.index / this.settings.waveCount);

            this.color = `rgba(${r}, ${g}, ${b}, ${alpha})`
            /*this.color = `rgba(${settings.baseColor[0]}, ${
                settings.baseColor[1]
            }, ${settings.baseColor[2]}, ${1 - (this.index / this.settings.waveCount)})`;*/
            this.updateOffset();
        }
        updateOffset() {
            const totalHeight =
                (this.settings.waveCount - 1) * this.settings.waveSpacing;
            const centerOffset = (this.canvas.height - totalHeight) / 2;
            this.yOffset = centerOffset + this.index * this.settings.waveSpacing;

        }

        draw(ctx) {
            ctx.beginPath();
            ctx.strokeStyle = this.color;
            ctx.lineWidth = this.settings.lineWidth;

            let firstX = 0;
            const leftOffsetPx =
                (this.settings.leftOffset / 100) * this.canvas.height;
            let firstY =
                this.yOffset +
                leftOffsetPx +
                Math.sin(firstX * 0.005 + this.phase) * this.settings.amplitude +
                Math.cos(firstX * 0.002 + this.phase) * this.settings.amplitude * 0.5;
            ctx.moveTo(firstX, firstY);

            for (let x = 0; x <= this.canvas.width; x += 20) {
                const t = x / this.canvas.width;
                const leftOffsetPx =
                    (this.settings.leftOffset / 100) * this.canvas.height;
                const rightOffsetPx =
                    (this.settings.rightOffset / 100) * this.canvas.height;
                const offset = leftOffsetPx * (1 - t) + rightOffsetPx * t;
                const y =
                    this.yOffset +
                    offset +
                    Math.sin(x * 0.005 + this.phase) * this.settings.amplitude +
                    Math.cos(x * 0.002 + this.phase) * this.settings.amplitude * 0.5;
                ctx.lineTo(x, y);
            }

            ctx.stroke();
        }

        update() {
            const speed =
                this.settings.direction === "right"
                    ? -this.settings.baseSpeed
                    : this.settings.baseSpeed;
            this.phase += speed;
        }
    };

    init() {

        if (typeof InstallTrigger !== 'undefined') {
            window.onload = () => {
                setTimeout(()=>{
                    this.callonload();
                },500);

            };
        } else {
            this.callonload();
        }


    }

    callonload(){
        window.addEventListener("resize", () => this.resizeCanvas());
        this.resizeCanvas();

        for (let i = 0; i < this.settings.waveCount; i++) {
            this.waves.push(new this.Wave(i, this.settings, this.canvas));
        }

        this.animate();
        moveMenuIcon();
        console.log('called function');
    }

    animate() {
        this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);

        this.waves.forEach((wave) => {
            wave.updateOffset();
            wave.update();
            wave.draw(this.ctx);
        });

        this.animationFrame = requestAnimationFrame(() => this.animate());
    }

    destroy() {
        window.removeEventListener("resize", this.resizeCanvas);
        this.waves = [];
        this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);
        cancelAnimationFrame(this.animationFrame);
    }

    updateSettings(newSettings) {
        this.settings = { ...this.settings, ...newSettings };
        this.waves = [];
        for (let i = 0; i < this.settings.waveCount; i++) {
            this.waves.push(new this.Wave(i, this.settings, this.canvas));
        }
    }
}

new WavesCanvas(document.querySelector("[data-waves]"))
