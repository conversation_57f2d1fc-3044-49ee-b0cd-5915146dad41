import {type BreadcrumbItem} from '@/types';
import {Head, Link} from '@inertiajs/react';
import UserLayout from "@/layouts/user/user-layout";
import BreadcrumbsButtons from "@/components/user/breadcrumbButtons";
import 'react-date-range/dist/styles.css';
import 'react-date-range/dist/theme/default.css';
import {useUserContext} from "@/context/UserContext";
import {useEffect, useState} from "react";
import UserService from "@/services/UserService";
import {PAGINATION_PAGE_SIZE, YES} from "@/constants/Constants";
import {Pagination, Stack} from "@mui/material";
import {z} from "zod";
import {DatePicker} from "antd";
const { RangePicker } = DatePicker;

export default function Transactions() {
    const {
        httpServiceObject,
        fetchBalanceFromApi,
        walletBalances,
        showNotification,
        copyButton
    } = useUserContext();

    const [transactions, setTransactions] = useState<any>(null);
    const [types, setTypes] = useState<any>(null);
    const [paginationPage, setPaginationPage] = useState(1);
    const [activePage, setActivePage] = useState(1);
    const [btnLoading, setBtnLoading] = useState(false);
    const [searchFilter, setSearchFilter] = useState({
        start_date:'', end_date:'', wallet_id: '', type_id: ''
    });

    const handlePagination = async (event: React.ChangeEvent<unknown>, value: number) => {

        setActivePage(value);
        let details = await UserService.transactions(httpServiceObject, value, PAGINATION_PAGE_SIZE, searchFilter);
        setTransactions(details?.data);

    };

    const fetchStats = async () => {

        try {
            await fetchBalanceFromApi();

            let stats = await UserService.transactions(httpServiceObject, paginationPage, PAGINATION_PAGE_SIZE, searchFilter);
            setTransactions(stats.data);
            let data = await UserService.transactionTypes(httpServiceObject);
            setTypes(data);

            let size = (stats?.pagination?.total > PAGINATION_PAGE_SIZE) ? (Math.ceil(stats?.pagination?.total / PAGINATION_PAGE_SIZE)) : 1;

            console.log(size, 'page size');
            setPaginationPage(size);

        } catch (error) {
            console.error("Error fetching stats:", error);
        }
    };
    useEffect(() => {
        fetchStats();
    }, [])

    const submitForm = async (e: React.FormEvent<HTMLFormElement>) => {
        e.preventDefault();
        setBtnLoading(true);

        const myformdata = new FormData(e.currentTarget);
        let formData = Object.fromEntries(myformdata.entries());
        try {
            console.log(myformdata);
            setPaginationPage(1);
            console.log('pageination changed')
            setSearchFilter(formData);
            console.log('search result changed');

        } catch (err) {
            if(err instanceof z.ZodError) {
                showNotification(err.issues[0].message);
            } else {
                console.log(err.message, 'asdfasdf', err);
                showNotification(err);
            }
        } finally {
            setBtnLoading(false);
        }
    };

    useEffect(()=>{
        fetchStats();
    },[searchFilter])
    const [range, setRange] = useState([]);

    return (
        <UserLayout>

            <main className="adminuiux-content has-sidebar">

                <div className="container mt-4">
                    <div className="row gx-3 align-items-center">
                        <div className="col-12 col-sm">
                            <nav aria-label="breadcrumb" className="mb-2">
                                <ol className="breadcrumb mb-0">
                                    <li className="breadcrumb-item bi"><Link href={route('user.dashboard')}><i
                                        className="bi bi-house-door me-1 fs-14"></i> Dashboard</Link></li>
                                    <li className="breadcrumb-item active bi" aria-current="page">Transactions
                                    </li>
                                </ol>
                            </nav>
                            <h5>Transactions</h5>
                        </div>
                        {/*<div className="col-12 col-sm-auto text-end py-3 py-sm-0">
                            <BreadcrumbsButtons/>
                        </div>*/}
                    </div>
                </div>


                <div className="container" id="main-content">
                    <div className="card adminuiux-card mt-4 mb-0">
                        <div className="card-body">
                            <form onSubmit={submitForm}>
                                <div className="row">
                                    {/*<div className="col">
                                    <div className="form-floating">
                                        <input type="text" className="form-control" id="Search"
                                               placeholder="Search" />
                                        <label htmlFor="Search">Search</label>
                                    </div>
                                </div>
                                <div className="col">
                                    <div className="form-floating">
                                        <input type="date" className="form-control" id="Date"
                                               placeholder="Date" />
                                        <label htmlFor="Date">Date</label>
                                    </div>
                                </div>*/}
                                    <div className="col-12 col-md m-mb-15">
                                      {/* <RangePicker className={'form-control'} onChange={(dates, dateStrings) => {
                                           const [start_date, end_date] = dateStrings;
                                       }}/>*/}
                                        <RangePicker
                                            className="form-control"
                                            onChange={(dates, dateStrings) => setRange(dateStrings)}
                                        />

                                        <input type="hidden" name="start_date" value={range?.[0] || ''} />
                                        <input type="hidden" name="end_date" value={range?.[1] || ''} />
                                    </div>

                                    <div className="col-12 col-md m-mb-15">
                                        <div className="form-floating1 custom-select-dropdown">
                                            <select className="form-select" name={'type_id'}>
                                                <option value="">Category</option>
                                                {types &&
                                                Object.entries(types)
                                                    .map(([key, item]) => (
                                                        <option value={item?.id} key={key}>{item?.name}</option>
                                                    ))}
                                            </select>
                                        </div>
                                    </div>
                                    <div className="col-12 col-md m-mb-15">
                                        <div className="form-floating1 custom-select-dropdown">
                                            <select className="form-select" name={'wallet_id'}>
                                                <option value="">Wallet</option>
                                                {walletBalances &&
                                                Object.entries(walletBalances)
                                                    .map(([key, item]) => (
                                                        <option value={item?.id} key={key}>{item?.name}</option>
                                                    ))}
                                            </select>
                                        </div>
                                    </div>
                                    <div className="col-12 col-md-auto d-flex">
                                        <button type="submit" className="btn btn-theme w-100" disabled={btnLoading}>
                                            {btnLoading ? "Loading..." : "Search"}
                                        </button>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>

                    <div className="card adminuiux-card mt-4 mb-0">
                        <div className="card-body">
                            <div className="table-responsive">
                                <table
                                    className="table">
                                    <thead>
                                    <tr>
                                        <th className="text-center sorting">Time</th>
                                        <th className="text-center sorting">Ref. No.</th>
                                        <th className="text-center sorting">Category</th>
                                        <th className="text-center sorting">Particulars</th>
                                        <th className="text-center sorting">Wallet</th>
                                        <th className="text-center sorting">Debit</th>
                                        <th className="text-center sorting">Credit</th>
                                    </tr>
                                    </thead>
                                    <tbody>


                                    {(transactions && transactions.length > 0) ? (
                                        transactions &&
                                        Object.entries(transactions)
                                            .map(([key, item]) => (
                                                <tr className="odd" key={key}>
                                                    <td className="text-center">{item?.created_at}</td>
                                                    <td className="text-center">{item?.ref_no}</td>
                                                    <td className="text-center">{item?.type}</td>
                                                    <td className="text-center">{item?.particulars}</td>
                                                    <td className="text-center">{item?.wallet}</td>
                                                    <td className="text-center">{(item?.debit_amount != '') && <span
                                                        className="badge badge-light text-bg-theme-1">{item?.debit_amount}</span>}</td>
                                                    <td className="text-center">
                                                        {(item?.credit_amount != '') && <span
                                                            className="badge badge-light text-bg-theme-1">{item?.credit_amount}</span>}
                                                    </td>
                                                </tr>
                                            ))
                                    ) : (
                                        <tr className="odd">
                                            <td className="text-center" colSpan={7}>No Transactions Found</td>
                                        </tr>

                                    )}


                                    </tbody>
                                </table>

                                {(transactions?.length > 0) ?
                                    <div className="col-12 d-flex align-items-center justify-content-center">
                                        <Stack spacing={2}>
                                            <Pagination count={paginationPage} page={activePage}
                                                        onChange={handlePagination}/>
                                        </Stack>
                                    </div>
                                    : ''
                                }
                            </div>
                        </div>
                    </div>


                </div>


            </main>


        </UserLayout>
    );
}
