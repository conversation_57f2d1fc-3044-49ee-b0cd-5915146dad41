<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends \App\Base\Database\MigrationBase
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        $this->schema->create('user_royalty_histories', function (\App\Base\Database\BlueprintBase $table) {
            $table->id();
            $table->bigInteger('ref_no')->unique();
            $table->bigInteger('user_id')->nullable()->index();
            $table->bigInteger('rank_id')->nullable()->index();
            $table->date('date')->index();
            $table->decimal('amount',48,8)->nullable()->index();
            $table->decimal('raw_amount',48,8)->nullable()->index();
            $table->double('percentage')->index();
            $table->timestamps();

            $table->unique(['user_id','rank_id','date'],'user_rank_date');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('user_royalty_histories');
    }
};
