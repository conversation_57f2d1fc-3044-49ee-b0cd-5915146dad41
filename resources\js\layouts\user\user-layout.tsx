import {type BreadcrumbItem} from '@/types';
import {type PropsWithChildren} from 'react';
import {Head, Link} from '@inertiajs/react';
import {useEffect} from "react";
import {useUserContext} from "@/context/UserContext";
// import '/frontend/assets/js/javascript.js';

export default function UserLayout({
                                       children,
                                       breadcrumbs = []
                                   }: PropsWithChildren<{ breadcrumbs?: BreadcrumbItem[] }>) {
    const {copyButton, userDetails,address,userToken,isConnected,connectWallet,disconnectWallet,showNotification} = useUserContext();

    useEffect(() => {

         const script = document.createElement('script');
         script.src = 'https://cdn.jsdelivr.net/npm/bootstrap@5.3.6/dist/js/bootstrap.bundle.min.js';
        script.integrity = "sha384-j1CDi7MgGQ12Z7Qab0qlWQ/Qqz24Gc6BM0thvEMVjHnfYGF0rmFCozFSxQBxwHKO";
        script.crossOrigin = "anonymous";
        script.async = true;
         document.body.appendChild(script);

        const script2 = document.createElement('script');
        script2.src = "/backend/assets/js/custom.js?v="+Math.random();
        document.body.appendChild(script2);

        return () => {
            document.body.removeChild(script);
            document.body.removeChild(script2);
        };
    }, []);

    useEffect(() => {
        if(!isConnected || !userDetails) {
            disconnectWallet();
        }
    }, [isConnected,userDetails]);

    const logout = function () {
        disconnectWallet();
    }
    return (

        <>
            <Head>
                <title>{userDetails?.name}</title>
                <link rel="preconnect" href="https://fonts.googleapis.com"/>
                <link rel="preconnect" href="https://fonts.gstatic.com" crossOrigin/>
                <link
                    href="https://fonts.googleapis.com/css2?family=Lexend:wght@100..900&family=Open+Sans:ital,wght@0,300..800;1,300..800&display=swap"
                    rel="stylesheet"/>
                <link rel="stylesheet"
                      href="https://maxst.icons8.com/vue-static/landings/line-awesome/line-awesome/1.3.0/css/line-awesome.min.css"/>
                <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.6/dist/css/bootstrap.min.css" rel="stylesheet"
                      integrity="sha384-4Q6Gf2aSP4eDXB8Miphtr37CMZZQ5oXLH2yaXMJ2w8e2ZtHTl7GptT4jmndRuHDT"
                      crossOrigin="anonymous" />

                <link href="/backend/assets/css/app.css" rel="stylesheet"/>
                <link href="/backend/assets/css/custom.css" rel="stylesheet"/>

                {/*<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.6/dist/js/bootstrap.bundle.min.js"
                        integrity="sha384-j1CDi7MgGQ12Z7Qab0qlWQ/Qqz24Gc6BM0thvEMVjHnfYGF0rmFCozFSxQBxwHKO"
                        crossOrigin="anonymous"></script>*/}
            </Head>


            <header className="adminuiux-header">

                <nav className="navbar navbar-expand-lg fixed-top">
                    <div className="container-fluid">


                        <button className="btn btn-link btn-square sidebar-toggler" id={'sidebar-toggler'} type="button"
                        >
                            {/*<i className="sidebar-svg" data-feather="menu"></i>*/}
                            <i className="las la-bars"></i>
                        </button>

                        <Link className="navbar-brand" href={route('user.dashboard')}>
                            <img data-bs-img="light" src="/backend/assets/img/logo-512.png" alt=""/>
                            <img data-bs-img="dark" src="/backend/assets/img/logo-512.png" alt=""/>
                            <div className="">
                                <span className="h4">DRIXE</span>
                            </div>
                        </Link>


                        <div className="col-12 col-md-6 col-lg-6 col-xxl-5 mx-auto" id="header-navbar">
                            <div className="input-group">
                                <input type="text" className="form-control form-control-lg border-theme-1"
                                       placeholder="Referral Code" aria-describedby="button-addon2"
                                       defaultValue={userDetails?.referral_link} disabled="" style={{fontSize: '15px' }}/>
                                    <button className="btn btn-lg btn-outline-theme" type="button" id="button-addon2" onClick={()=>copyButton(userDetails?.referral_link)}><i className="bi bi-copy"></i></button>
                            </div>

                        </div>


                        <div className="ms-auto">
                            <div className="dropdown d-inline-block topbar-user-address">
                                <button
                                    className="btn btn-link btn-square btn-icon btn-link-header no-caret"
                                    type="button" >
                                    <small>{userDetails?.short_address}</small>

                                </button>

                            </div>


                            <div className="dropdown d-inline-block">
                                <a className="dropdown-toggle btn btn-link btn-square btn-link-header style-none no-caret px-0"
                                   id="userprofiledd" data-bs-toggle="dropdown" aria-expanded="false" role="button">
                                    <div className="row gx-0 d-inline-flex">
                                        <div className="col-auto align-self-center">
                                            <figure className="avatar avatar-28 rounded-circle coverimg align-middle">
                                                <img src="/backend/assets/img/logo-512.png" alt=""/>
                                            </figure>
                                        </div>
                                    </div>
                                </a>
                                <div className="dropdown-menu dropdown-menu-end width-300 pt-0 px-0 sm-mi-45px"
                                     aria-labelledby="userprofiledd">
                                    <div className="bg-theme-1-space rounded py-3 mb-3 dropdown-dontclose">
                                        <div className="row gx-0">
                                            <div className="col-auto px-3">
                                                <figure
                                                    className="avatar avatar-50 rounded-circle coverimg align-middle">
                                                    <img src="/backend/assets/img/logo-512.png" alt=""/>
                                                </figure>
                                            </div>
                                            <div className="col align-self-center ">
                                                <p className="mb-1"><span>{userDetails?.username}</span></p>
                                                <p><i className="bi bi-envelope me-2"></i> {userDetails?.email}</p>
                                            </div>
                                        </div>
                                    </div>
                                    <div className="px-2">
                                        <div>
                                            <Link className="dropdown-item" href={route('user.dashboard')}>
                                                <i className="bi bi-columns-gap avatar avatar-18 me-1"></i> My Dashboard
                                            </Link>
                                        </div>
                                        <div>
                                            <Link className="dropdown-item" href={route('user.investmentHistory')}>
                                                <i className="bi bi-piggy-bank avatar avatar-18 me-1"></i> My Stakes
                                            </Link>
                                        </div>
                                        <div>
                                            <Link className="dropdown-item theme-red" href={'#'} onClick={logout}>
                                                <i className="bi bi-box-arrow-right avatar avatar-18 me-1"></i> Disconnect
                                            </Link>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </nav>
            </header>


            <div className="adminuiux-wrap">

                <div className="adminuiux-sidebar">
                    <div className="adminuiux-sidebar-inner">

                        <div className="px-3 not-iconic mt-3">
                            <div className="row">
                                <div className="col align-self-center ">
                                    <h6 className="fw-medium">Main Menu</h6>
                                </div>
                                <div className="col-auto">
                                    <a className="btn btn-link btn-square" data-bs-toggle="collapse"
                                       data-bs-target="#usersidebarprofile" aria-expanded="false" role="button"
                                       aria-controls="usersidebarprofile">
                                        <i data-feather="user"></i>
                                    </a>
                                </div>
                            </div>
                            <div className="text-center collapse " id="usersidebarprofile">
                                <figure className="avatar avatar-100 rounded-circle coverimg my-3">
                                    <img src="/backend/assets/img/modern-ai-image/user-6.jpg" alt=""/>
                                </figure>
                                <h5 className="mb-1 fw-medium">Drixe</h5>
                                <p className="small"></p>
                            </div>
                        </div>

                        <ul className="nav flex-column menu-active-line">

                            <li className="nav-item">
                                <Link href={route('user.dashboard')} className="nav-link">
                                    <i className="menu-icon bi bi-columns-gap"></i>
                                    <span className="menu-name">Dashboard</span>
                                </Link>
                            </li>

                            <li className="nav-item dropdown">
                                <Link href={'#'} className="nav-link dropdown-toggle"
                                   data-bs-toggle="dropdown">
                                    <i className="menu-icon bi bi-piggy-bank"></i>
                                    <span className="menu-name">Investment</span>
                                </Link>
                                <div className="dropdown-menu">
                                    <div className="nav-item">
                                        <Link href={route('user.investNow')} className="nav-link">
                                            <span className="menu-name">Invest Now</span>
                                        </Link>
                                    </div>
                                    {/*<div className="nav-item">
                                        <Link href={route('user.depositHistory')} className="nav-link">
                                            <span className="menu-name">Deposit History</span>
                                        </Link>
                                    </div>*/}
                                    <div className="nav-item">
                                        <Link href={route('user.investmentHistory')} className="nav-link">
                                            <span className="menu-name">Investment History</span>
                                        </Link>
                                    </div>
                                </div>
                            </li>
                            <li className="nav-item dropdown">
                                <Link href={'#'} className="nav-link dropdown-toggle"
                                   data-bs-toggle="dropdown">
                                    <i className="menu-icon bi bi-people"></i>
                                    <span className="menu-name">Team</span>
                                </Link>
                                <div className="dropdown-menu">
                                    <div className="nav-item">
                                        <Link href={route('user.directs')} className="nav-link">
                                            <span className="menu-name">Directs</span>
                                        </Link>
                                    </div>
                                    <div className="nav-item">
                                        <Link href={route('user.tree')} className="nav-link">
                                            <span className="menu-name">Genealogy</span>
                                        </Link>
                                    </div>
                                </div>
                            </li>
                            <li className="nav-item dropdown">
                                <Link href={'#'} className="nav-link dropdown-toggle"
                                   data-bs-toggle="dropdown">
                                    <i className="menu-icon bi bi-trophy"></i>
                                    <span className="menu-name">Achievement</span>
                                </Link>
                                <div className="dropdown-menu">
                                    <div className="nav-item">
                                        <Link href={route('user.rank')} className="nav-link">
                                            <span className="menu-name">Ranks</span>
                                        </Link>
                                    </div>
                                    <div className="nav-item">
                                        <Link href={route('user.openLevels')} className="nav-link">
                                            <span className="menu-name">Opened Levels </span>
                                        </Link>
                                    </div>
                                </div>
                            </li>
                            <li className="nav-item dropdown">
                                <Link href={'#'} className="nav-link dropdown-toggle"
                                   data-bs-toggle="dropdown">
                                    <i className="menu-icon bi bi-currency-dollar"></i>
                                    <span className="menu-name">Business</span>
                                </Link>
                                <div className="dropdown-menu">
                                    <div className="nav-item">
                                        <Link href={route('user.levelWiseBusiness')} className="nav-link">
                                            <span className="menu-name">Level Wise</span>
                                        </Link>
                                    </div>
                                    <div className="nav-item">
                                        <Link href={route('user.teamBusiness')} className="nav-link">
                                            <span className="menu-name">Monthly Team Business</span>
                                        </Link>
                                    </div>
                                </div>
                            </li>

                            <li className="nav-item dropdown">
                                <Link href={'#'} className="nav-link dropdown-toggle"
                                   data-bs-toggle="dropdown">
                                    <i className="menu-icon bi bi-gift"></i>
                                    <span className="menu-name">Rewards</span>
                                </Link>
                                <div className="dropdown-menu">

                                    <div className="nav-item">
                                        <Link href={route('user.royalty')} className="nav-link">
                                            <span className="menu-name">Royalty</span>
                                        </Link>
                                    </div>
                                </div>
                            </li>

                            <li className="nav-item dropdown">
                                <Link href={'#'} className="nav-link dropdown-toggle"
                                   data-bs-toggle="dropdown">
                                    <i className="menu-icon bi bi-person-vcard"></i>
                                    <span className="menu-name">My Account</span>
                                </Link>
                                <div className="dropdown-menu">

                                    <div className="nav-item">
                                        <Link href={route('user.transactions')} className="nav-link">
                                            <span className="menu-name">All Transactions History</span>
                                        </Link>

                                    </div>
                                </div>
                            </li>

                            <li className="nav-item dropdown">
                                <Link href={'#'} className="nav-link dropdown-toggle"
                                      data-bs-toggle="dropdown">
                                    <i className="menu-icon bi bi-card-heading"></i>
                                    <span className="menu-name">Withdraw</span>
                                </Link>
                                <div className="dropdown-menu">
                                    <div className="nav-item">
                                        <Link href={route('user.withdrawNow')} className="nav-link">
                                            <span className="menu-name">Withdraw Now</span>
                                        </Link>
                                    </div>
                                    <div className="nav-item">
                                        <Link href={route('user.withdrawalHistory')} className="nav-link">
                                            <span className="menu-name">Withdrawal History</span>
                                        </Link>
                                    </div>
                                </div>
                            </li>

                            {/*<li className="nav-item dropdown">
                                <a href="javascript:void(0)" className="nav-link dropdown-toggle"
                                   data-bs-toggle="dropdown">
                                    <i className="menu-icon bi bi-arrow-left-right"></i>
                                    <span className="menu-name">Transfer</span>
                                </a>
                                <div className="dropdown-menu">
                                    <div className="nav-item">
                                        <a href="investment-company-shares.html" className="nav-link">
                                            <i className="menu-icon bi bi-bank"></i>
                                            <span className="menu-name">Transfer History</span>
                                        </a>
                                    </div>
                                    <div className="nav-item">
                                        <a href="investment-mutual-funds.html" className="nav-link">
                                            <i className="menu-icon bi bi-cash-coin"></i>
                                            <span className="menu-name">Transfer from Withdrawals</span>
                                        </a>
                                    </div>
                                </div>
                            </li>

                            <li className="nav-item dropdown">
                                <a href="javascript:void(0)" className="nav-link dropdown-toggle"
                                   data-bs-toggle="dropdown">
                                    <i className="menu-icon bi bi-files"></i>
                                    <span className="menu-name">User Documents</span>
                                </a>
                                <div className="dropdown-menu">
                                    <div className="nav-item">
                                        <a href="investment-company-shares.html" className="nav-link">
                                            <i className="menu-icon bi bi-bank"></i>
                                            <span className="menu-name">Passport</span>
                                        </a>
                                    </div>
                                </div>
                            </li>*/}

                            <li className="nav-item dropdown">
                                <Link href={'#'} className="nav-link dropdown-toggle"
                                   data-bs-toggle="dropdown">
                                    <i className="menu-icon bi bi-envelope"></i>
                                    <span className="menu-name">Support</span>
                                </Link>
                                <div className="dropdown-menu">
                                    <div className="nav-item">
                                        <Link href={route('user.tickets')} className="nav-link">
                                            <i className="menu-icon bi bi-bank"></i>
                                            <span className="menu-name">Inbox / Compose</span>
                                        </Link>
                                    </div>
                                </div>
                            </li>


                            <li className="nav-item">
                                <a target={"_blank"} download={''} href={'/BusinessPlan.pdf'} className="nav-link">
                                    <i className="menu-icon bi bi-filetype-pdf"></i>
                                    <span className="menu-name">Download PDF</span>
                                </a>
                            </li>
                            <li className="nav-item">
                                <Link href={'#'} onClick={logout} className="nav-link">
                                    <i className="menu-icon bi bi-box-arrow-right"></i>
                                    <span className="menu-name">Disconnect</span>
                                </Link>
                            </li>
                        </ul>

                    </div>
                </div>
                {children}
            </div>


            <footer className="adminuiux-footer has-adminuiux-sidebar mt-auto">
                <div className="container-fluid">
                    <div className="row">
                        <div className="col-12 col-md col-lg py-2">
                <span className="small">{/*Copyright @2025*/}
                </span>
                        </div>

                    </div>
                </div>
            </footer>

            {/*</body>*/}
        </>
    );
}
