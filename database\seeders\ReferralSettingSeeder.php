<?php

namespace Database\Seeders;

use App\Constants\CommonConstants;
use App\Models\Country;
use App\Models\ReferralSetting;
use App\Models\Setting;
use App\Models\TopupSetting;
use App\Models\User;
use App\Models\UserWallet;
use App\Models\Wallet;
use Faker\Provider\UserAgent;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;

class ReferralSettingSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $rows=[
            ['level_id'=>1,'incentive'=>10],
        ];

        foreach ($rows as $row) {
            if(!ReferralSetting::query()->where('level_id','=',$row['level_id'])->first()) {
                ReferralSetting::create($row);
            }
        }
    }
}
