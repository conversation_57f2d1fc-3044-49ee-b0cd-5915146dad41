<?php

namespace App\Models;

use App\Base\Model\BaseModel;
use App\Constants\CommonConstants;
use Illuminate\Database\Eloquent\Model;

class  Country extends BaseModel
{
    protected $fillable = [
        'name','iso','iso3','num_code','phone_code',
    ];

    public function onCreating()
    {
        $this->name=trim(strtoupper($this->name));
        parent::onCreating();
    }

    public static function getCountryFromCountryCode($country_code)
    {
        return self::query()->where('iso','=', $country_code)->first();
    }
}
