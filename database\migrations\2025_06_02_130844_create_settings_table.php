<?php

use Illuminate\Support\Facades\Schema;

return new class extends \App\Base\Database\MigrationBase
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        $this->schema->create('settings', function (\App\Base\Database\BlueprintBase $table) {
            $table->id();
            $table->string('name')->unique();
            $table->text('value')->nullable();
            $table->enum('type',[
                \App\Constants\CommonConstants::ENUM_SETTINGS_TYPE_INPUT,
                \App\Constants\CommonConstants::ENUM_SETTINGS_TYPE_SELECT,
                \App\Constants\CommonConstants::ENUM_SETTINGS_TYPE_TEXTAREA,
            ])->default(\App\Constants\CommonConstants::ENUM_SETTINGS_TYPE_INPUT);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('settings');
    }
};
