<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends \App\Base\Database\MigrationBase
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        $this->schema->create('logs', function (\App\Base\Database\BlueprintBase $table) {
            $table->id();
            $table->foreignId('user_id')->nullable()->references('id')->on('users')->nullOnDelete();
            $table->string('particulars', 1000);
            $table->string('type');
            $table->longText('data')->nullable();
            $table->createdAtTime();
            $table->ipAddresses();
            $table->userAgent();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('logs');
    }
};
