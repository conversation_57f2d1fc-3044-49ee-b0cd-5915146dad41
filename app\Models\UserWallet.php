<?php

namespace App\Models;

use App\Base\Model\BaseModel;

class UserWallet extends BaseModel
{
    protected $fillable = [
        'user_id','wallet_id',
    ];

    public function user()
    {
        return $this->hasOne(User::class, 'id', 'user_id');
    }

    public function wallet()
    {
        return $this->hasOne(Wallet::class, 'id', 'wallet_id');
    }

    public static function fetchUserWallet($user_id,$wallet_id) {
        try {
            $model=self::query()
                ->where('user_id','=',$user_id)
                ->where('wallet_id','=',$wallet_id)
                ->first();
            if($model) {
                return $model;
            } else {
                $model=new UserWallet();
                $model->user_id=$user_id;
                $model->wallet_id=$wallet_id;
                if($model->save()) {
                    return self::fetchUserWallet($user_id,$wallet_id);
                }
            }
        } catch (\Exception $e) {

        }
        return false;
    }
}
