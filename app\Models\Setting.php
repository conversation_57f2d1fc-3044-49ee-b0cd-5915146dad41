<?php

namespace App\Models;

use App\Base\Model\BaseModel;
use App\Constants\CommonConstants;
use Illuminate\Database\Eloquent\Model;

class Setting extends BaseModel
{
    const SETTING_MINIMUM_INVESTMENT = 1;
    const SETTING_MAXIMUM_INVESTMENT = 2;
    const SETTING_MONTHLY_DIVIDEND = 3;
    const SETTING_STAKE_DURATION = 4;
    const SETTING_STAKE_MATURITY_DURATION = 5;
    const SETTING_REFERRAL_BONUS_RELEASE_DURATION = 6;
    const SETTING_MINIMUM_WITHDRAWAL = 7;
    const SETTING_WITHDRAWAL_MULTIPLE = 8;
    const SETTING_DAILY_SUPPLY = 9;

    public static function getValue($id)
    {
        $model=self::query()->where('id','=',$id)->first();
        if($model) {
            return trim($model->value);
        }
        return false;
    }
}
